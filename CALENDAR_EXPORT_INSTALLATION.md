# Calendar Export Feature Installation Guide

## 📋 Overview
Panduan instalasi untuk fitur Export Calendar yang memungkinkan export kalender dalam format PDF, PNG, dan JPG.

## 🔧 Prerequisites
- PHP 8.2+
- Laravel 12.0+
- Composer
- GD Extension (untuk image processing)

## 📦 Installation Steps

### 1. Install Required Packages
```bash
composer require barryvdh/laravel-dompdf intervention/image
```

### 2. Publish Configuration (Optional)
```bash
php artisan vendor:publish --provider="Barryvdh\DomPDF\ServiceProvider"
```

### 3. Create Storage Directories
```bash
mkdir -p storage/app/temp
mkdir -p storage/fonts
chmod 755 storage/app/temp
chmod 755 storage/fonts
```

### 4. Update Environment (Optional)
Add to your `.env` file if needed:
```env
DOMPDF_ENABLE_REMOTE=true
DOMPDF_ENABLE_HTML5_PARSER=true
```

## 📁 Files Created

### Controllers
- `app/Http/Controllers/CalendarExportController.php`

### Views
- `resources/views/calendar/export.blade.php`
- `resources/views/exports/calendar-pdf.blade.php`
- `resources/views/exports/calendar-image.blade.php`

### Configuration
- `config/dompdf.php`

### Documentation
- `docs/features/calendar-export-feature.md`

## 🔗 Routes Added
```php
// In routes/web.php (already added)
Route::get('/calendar/export-form', [CalendarExportController::class, 'showExportForm'])
    ->name('calendar.export.form');
Route::get('/calendar/export', [CalendarExportController::class, 'export'])
    ->name('calendar.export');
```

## 🎯 Features Available

### Export Formats
1. **PDF** - Professional document format
2. **PNG** - High-quality image with transparency
3. **JPG** - Compressed image format

### Export Options
- Month/Year selection
- Project filtering
- Format selection with descriptions
- User-friendly interface

## 🚀 How to Use

### 1. Access Export Form
- Go to Calendar page
- Click "Export Calendar" button
- Or visit: `/calendar/export-form`

### 2. Select Options
- Choose export format (PDF/PNG/JPG)
- Select month and year
- Optionally filter by project

### 3. Export
- Click "Export Calendar"
- File will download automatically

## 📊 Sample Output

### PDF Export
- **Filename**: `calendar-2025-7.pdf`
- **Layout**: A4 Landscape
- **Content**: Calendar grid, legend, project details

### Image Export
- **Filename**: `calendar-2025-7.png/jpg`
- **Size**: 1200x800 pixels
- **Content**: Calendar grid with high contrast

## 🔍 Testing

### Test Export Functionality
1. Create some test projects with dates
2. Go to calendar export form
3. Try each format (PDF, PNG, JPG)
4. Verify downloads work correctly
5. Check file content and quality

### Test Different Scenarios
- Export current month
- Export past/future months
- Export with project filter
- Export with no projects
- Export with many projects

## 🛠 Troubleshooting

### Common Issues

#### 1. Memory Limit Error
```bash
# Increase PHP memory limit
ini_set('memory_limit', '256M');
```

#### 2. Permission Denied
```bash
# Fix storage permissions
chmod -R 755 storage/
chown -R www-data:www-data storage/
```

#### 3. Missing GD Extension
```bash
# Install GD extension (Ubuntu/Debian)
sudo apt-get install php8.2-gd

# Install GD extension (CentOS/RHEL)
sudo yum install php-gd
```

#### 4. DomPDF Issues
```bash
# Clear config cache
php artisan config:clear
php artisan cache:clear

# Check DomPDF installation
composer show barryvdh/laravel-dompdf
```

### Debug Mode
Enable debug mode in controller for troubleshooting:
```php
// In CalendarExportController.php
private $debug = true; // Set to true for debugging
```

## 📈 Performance Considerations

### Optimization Tips
1. **Memory**: Increase PHP memory limit for large calendars
2. **Timeout**: Increase max_execution_time for complex exports
3. **Caching**: Consider caching calendar data
4. **Cleanup**: Temporary files are auto-cleaned

### Expected Performance
- **PDF Generation**: 2-3 seconds
- **Image Generation**: 3-5 seconds
- **Memory Usage**: 50-100MB peak
- **File Sizes**:
  - PDF: 200-500KB
  - PNG: 100-300KB
  - JPG: 50-150KB

## 🔒 Security Notes

### Security Features
- Authentication required
- Input validation
- Secure file handling
- Automatic cleanup
- No direct file system access

### Best Practices
- Files stored in secure temp directory
- Automatic file cleanup after download
- Validated input parameters
- Protected routes

## 🎨 Customization

### Styling
Modify the export templates:
- `resources/views/exports/calendar-pdf.blade.php` - PDF styling
- `resources/views/exports/calendar-image.blade.php` - Image styling

### Colors
Update color schemes in the templates:
```css
.event-project-start { background-color: #10B981; }
.event-project-end { background-color: #EF4444; }
.event-calendar { background-color: #3B82F6; }
```

### Layout
Modify calendar layout and content in:
- `CalendarExportController::generateCalendarGrid()`
- Template files for visual changes

## 📞 Support

### Getting Help
1. Check this documentation
2. Review error logs: `storage/logs/laravel.log`
3. Test with simple scenarios first
4. Check package documentation:
   - [DomPDF](https://github.com/barryvdh/laravel-dompdf)
   - [Intervention Image](https://image.intervention.io/)

### Common Commands
```bash
# Check package versions
composer show | grep -E "(dompdf|intervention)"

# Clear all caches
php artisan optimize:clear

# Check PHP extensions
php -m | grep -E "(gd|dom|mbstring)"

# Test file permissions
ls -la storage/app/temp/
```

## ✅ Verification Checklist

- [ ] Packages installed successfully
- [ ] Storage directories created with correct permissions
- [ ] Routes accessible
- [ ] Export form loads correctly
- [ ] PDF export works
- [ ] PNG export works
- [ ] JPG export works
- [ ] Files download correctly
- [ ] Temporary files cleaned up
- [ ] No errors in logs

## 🎉 Success!

If all steps completed successfully, you now have a fully functional Calendar Export feature that can generate professional-quality calendar exports in multiple formats!

---

**Installation Guide Version**: 1.0  
**Compatible with**: DisaCloud05-v4  
**Last Updated**: 28 Juni 2025
