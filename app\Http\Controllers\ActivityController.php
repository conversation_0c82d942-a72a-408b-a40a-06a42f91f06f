<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\Project;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Project $project)
    {
        $activities = $project->activities()
            ->with(['assignedUser', 'creator'])
            ->latest()
            ->get();

        return view('projects.activities.index', compact('project', 'activities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Project $project)
    {
        return view('projects.activities.create', compact('project'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Project $project)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:planned,in_progress,completed,on_hold',
            'priority' => 'required|in:low,medium,high',
            'start_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:start_date',
            'assigned_to' => 'nullable|exists:users,id'
        ]);

        $activity = $project->activities()->create([
            ...$validated,
            'created_by' => auth()->id()
        ]);

        return redirect()
            ->route('projects.activities.index', $project)
            ->with('success', 'Aktivitas berhasil dibuat');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project, Activity $activity)
    {
        return view('projects.activities.edit', compact('project', 'activity'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project, Activity $activity)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:planned,in_progress,completed,on_hold',
            'priority' => 'required|in:low,medium,high',
            'start_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:start_date',
            'assigned_to' => 'nullable|exists:users,id'
        ]);

        $activity->update($validated);

        return redirect()
            ->route('projects.activities.index', $project)
            ->with('success', 'Aktivitas berhasil diperbarui');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project, Activity $activity)
    {
        $activity->delete();

        return redirect()
            ->route('projects.activities.index', $project)
            ->with('success', 'Aktivitas berhasil dihapus');
    }
}
