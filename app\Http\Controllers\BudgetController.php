<?php

namespace App\Http\Controllers;

use App\Models\Budget;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class BudgetController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Budget::with('project');
        
        // Filter by project if specified
        if ($request->has('project_id') && $request->project_id) {
            $query->where('project_id', $request->project_id);
        }
        
        // Filter by status if specified
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }
        
        // Search by description if specified
        if ($request->has('search') && $request->search) {
            $query->where('description', 'like', '%' . $request->search . '%');
        }
        
        $filteredBudgets = $query->orderBy('budget_date', 'desc')->get();

        // Group budgets by project_id. Ensure project relation is loaded for accessing project details.
        $budgetsGroupedByProject = $filteredBudgets->groupBy(function($budget) {
            return $budget->project_id ?? 'unassigned'; // Group by project_id, handle nulls
        });

        // Calculate summary statistics from the filtered (but not yet grouped) budgets
        $totalBudget = $filteredBudgets->sum('amount');
        $approvedBudget = $filteredBudgets->where('status', 'approved')->sum('amount');
        $pendingBudget = $filteredBudgets->where('status', 'pending')->sum('amount');
        $totalEstimatedProfit = $filteredBudgets->sum('estimated_profit');

        // Get all projects for the filter dropdown
        $allProjects = Project::orderBy('name')->get();

        return view('budgets.index', compact(
            'budgetsGroupedByProject',
            'allProjects',
            'totalBudget',
            'approvedBudget',
            'pendingBudget',
            'totalEstimatedProfit'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $projects = Project::orderBy('name')->get();
        return view('budgets.create', compact('projects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'project_id' => 'required|exists:projects,id',
            'amount' => 'required|numeric|min:0',
            'estimated_profit' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'budget_date' => 'required|date',
            'status' => 'required|in:pending,approved,rejected',
            'invoice_file' => 'nullable|file|mimes:pdf|max:10240',
        ]);
        
        $data = $request->except('invoice_file');
        
        // Calculate profit percentage
        if ($request->amount > 0) {
            $data['profit_percentage'] = ($request->estimated_profit / $request->amount) * 100;
        } else {
            $data['profit_percentage'] = 0;
        }
        
        // Handle invoice file upload
        if ($request->hasFile('invoice_file')) {
            $file = $request->file('invoice_file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('invoices', $filename, 'public');
            $data['invoice_file'] = $path;
        }
        
        Budget::create($data);
        
        return redirect()->route('budgets.index')
            ->with('success', 'Budget berhasil ditambahkan');
    }

    /**
     * Display the specified resource.
     */
    public function show(Budget $budget)
    {
        // Load the project with its materials to calculate budget usage
        $budget->load('project.materials');
        
        // Get materials for this project
        $materials = $budget->project->materials;
        $totalMaterialsCost = $budget->project->getTotalMaterialsCostAttribute();
        $remainingAmount = $budget->getRemainingAmountAttribute();
        $usagePercentage = $budget->getUsagePercentageAttribute();
        
        return view('budgets.show', compact('budget', 'materials', 'totalMaterialsCost', 'remainingAmount', 'usagePercentage'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Budget $budget)
    {
        $projects = Project::orderBy('name')->get();
        return view('budgets.edit', compact('budget', 'projects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Budget $budget)
    {
        $request->validate([
            'project_id' => 'required|exists:projects,id',
            'amount' => 'required|numeric|min:0',
            'estimated_profit' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'budget_date' => 'required|date',
            'status' => 'required|in:pending,approved,rejected',
            'invoice_file' => 'nullable|file|mimes:pdf|max:10240',
        ]);
        
        $data = $request->except('invoice_file');
        
        // Calculate profit percentage
        if ($request->amount > 0) {
            $data['profit_percentage'] = ($request->estimated_profit / $request->amount) * 100;
        } else {
            $data['profit_percentage'] = 0;
        }
        
        // Handle invoice file upload
        if ($request->hasFile('invoice_file')) {
            // Delete old file if exists
            if ($budget->invoice_file) {
                Storage::disk('public')->delete($budget->invoice_file);
            }
            
            $file = $request->file('invoice_file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('invoices', $filename, 'public');
            $data['invoice_file'] = $path;
        }
        
        $budget->update($data);
        
        return redirect()->route('budgets.index')
            ->with('success', 'Budget berhasil diperbarui');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Budget $budget)
    {
        // Delete invoice file if exists
        if ($budget->invoice_file) {
            Storage::disk('public')->delete($budget->invoice_file);
        }
        
        $budget->delete();
        
        return redirect()->route('budgets.index')
            ->with('success', 'Budget berhasil dihapus');
    }
    
    /**
     * Compare budgets between projects
     */
    public function compare()
    {
        $projects = Project::has('budget')->orderBy('name')->get();
        
        // Get selected projects for comparison or default to empty array
        $selectedProjects = request('projects', []);
        
        $comparisonData = [];
        
        if (!empty($selectedProjects)) {
            // Get budget data for selected projects
            $budgetData = Budget::whereIn('project_id', $selectedProjects)
                ->with('project')
                ->get()
                ->groupBy('project_id');
                
            foreach ($budgetData as $projectId => $budgets) {
                $project = Project::find($projectId);
                $comparisonData[] = [
                    'project_name' => $project->name,
                    'total_budget' => $budgets->sum('amount'),
                    'total_profit' => $budgets->sum('estimated_profit'),
                    'avg_profit_percentage' => $budgets->avg('profit_percentage'),
                    'budget_count' => $budgets->count(),
                    'approved_budget' => $budgets->where('status', 'approved')->sum('amount'),
                    'pending_budget' => $budgets->where('status', 'pending')->sum('amount')
                ];
            }
        }
        
        return view('budgets.compare', compact('projects', 'selectedProjects', 'comparisonData'));
    }
    
    /**
     * Download invoice file
     */
    public function downloadInvoice(Budget $budget)
    {
        if (!$budget->invoice_file) {
            return redirect()->back()->with('error', 'Tidak ada file invoice untuk budget ini');
        }
        
        return Storage::disk('public')->download($budget->invoice_file);
    }
}
