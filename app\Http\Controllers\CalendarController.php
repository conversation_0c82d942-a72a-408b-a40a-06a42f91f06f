<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Worker;
use App\Models\Activity;
use Illuminate\Http\Request;

class CalendarController extends Controller
{
    public function index()
    {
        $projects = Project::all();
        $workers = Worker::all();
        return view('calendar.index', compact('projects', 'workers'));
    }

    public function events(Request $request)
    {
        $query = Activity::with(['project', 'worker']);

        if ($request->filled('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        if ($request->filled('worker_id')) {
            $query->whereHas('project.workers', function($q) use ($request) {
                $q->where('workers.id', $request->worker_id);
            });
        }

        $activities = $query->get();

        return $activities->map(function ($activity) {
            $color = $this->getEventColor($activity->project->priority ?? 'medium');
            
            return [
                'id' => $activity->id,
                'title' => $activity->name,
                'start' => $activity->start_date,
                'end' => $activity->end_date,
                'description' => $activity->description,
                'project_name' => $activity->project->name,
                'worker_name' => $activity->worker ? $activity->worker->name : 'Belum ditugaskan',
                'backgroundColor' => $color,
                'borderColor' => $color,
            ];
        });
    }

    private function getEventColor($priority)
    {
        return match($priority) {
            'high' => '#EF4444',   // Merah untuk prioritas tinggi
            'low' => '#10B981',    // Hijau untuk prioritas rendah
            default => '#3B82F6',  // Biru untuk prioritas sedang
        };
    }
}
