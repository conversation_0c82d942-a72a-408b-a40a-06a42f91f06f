<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\CalendarEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class CalendarExportController extends Controller
{
    /**
     * Export calendar in various formats
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'pdf'); // Default to PDF
        $month = $request->input('month', now()->month);
        $year = $request->input('year', now()->year);
        $projectId = $request->input('project_id');

        // Get calendar data
        $calendarData = $this->getCalendarData($month, $year, $projectId);

        switch ($format) {
            case 'pdf':
                return $this->exportToPdf($calendarData, $month, $year);
            case 'png':
                return $this->exportToPng($calendarData, $month, $year);
            case 'jpg':
                return $this->exportToJpg($calendarData, $month, $year);
            default:
                return $this->exportToPdf($calendarData, $month, $year);
        }
    }

    /**
     * Get calendar data for the specified month/year
     */
    private function getCalendarData($month, $year, $projectId = null)
    {
        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        // Get projects within the date range
        $projectsQuery = Project::where(function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_date', [$startDate, $endDate])
                  ->orWhereBetween('end_date', [$startDate, $endDate])
                  ->orWhere(function($q) use ($startDate, $endDate) {
                      $q->where('start_date', '<=', $startDate)
                        ->where('end_date', '>=', $endDate);
                  });
        });

        if ($projectId) {
            $projectsQuery->where('id', $projectId);
        }

        $projects = $projectsQuery->get();

        // Get calendar events (if table exists)
        $events = collect(); // Empty collection for now
        try {
            $eventsQuery = CalendarEvent::whereBetween('start_date', [$startDate, $endDate]);

            if ($projectId) {
                $eventsQuery->where('project_id', $projectId);
            }

            $events = $eventsQuery->with('project')->get();
        } catch (\Exception $e) {
            // If CalendarEvent table doesn't exist, use empty collection
            $events = collect();
        }

        // Generate calendar grid
        $calendar = $this->generateCalendarGrid($month, $year, $projects, $events);

        return [
            'calendar' => $calendar,
            'month' => $month,
            'year' => $year,
            'monthName' => Carbon::create($year, $month, 1)->format('F Y'),
            'projects' => $projects,
            'events' => $events
        ];
    }

    /**
     * Generate calendar grid with events
     */
    private function generateCalendarGrid($month, $year, $projects, $events)
    {
        $firstDay = Carbon::create($year, $month, 1);
        $lastDay = $firstDay->copy()->endOfMonth();
        $startCalendar = $firstDay->copy()->startOfWeek();
        $endCalendar = $lastDay->copy()->endOfWeek();

        $calendar = [];
        $current = $startCalendar->copy();

        while ($current <= $endCalendar) {
            $week = [];
            for ($i = 0; $i < 7; $i++) {
                $dayEvents = [];
                
                // Add project events
                foreach ($projects as $project) {
                    if ($project->start_date && $current->isSameDay($project->start_date)) {
                        $dayEvents[] = [
                            'type' => 'project_start',
                            'title' => 'Start: ' . $project->name,
                            'color' => '#10B981',
                            'project' => $project
                        ];
                    }
                    if ($project->end_date && $current->isSameDay($project->end_date)) {
                        $dayEvents[] = [
                            'type' => 'project_end',
                            'title' => 'End: ' . $project->name,
                            'color' => '#EF4444',
                            'project' => $project
                        ];
                    }
                }

                // Add calendar events
                foreach ($events as $event) {
                    if ($current->isSameDay($event->start_date)) {
                        $dayEvents[] = [
                            'type' => 'event',
                            'title' => $event->title,
                            'color' => '#3B82F6',
                            'event' => $event
                        ];
                    }
                }

                $week[] = [
                    'date' => $current->copy(),
                    'day' => $current->day,
                    'isCurrentMonth' => $current->month == $month,
                    'isToday' => $current->isToday(),
                    'events' => $dayEvents
                ];

                $current->addDay();
            }
            $calendar[] = $week;
        }

        return $calendar;
    }

    /**
     * Export calendar to PDF
     */
    private function exportToPdf($calendarData, $month, $year)
    {
        try {
            $html = View::make('exports.calendar-pdf', $calendarData)->render();

            $pdf = Pdf::loadHTML($html)
                      ->setPaper('A4', 'landscape')
                      ->setOptions([
                          'defaultFont' => 'DejaVu Sans',
                          'isHtml5ParserEnabled' => true,
                          'isRemoteEnabled' => false,
                          'debugKeepTemp' => false,
                          'debugCss' => false,
                          'debugLayout' => false,
                          'debugLayoutLines' => false,
                          'debugLayoutBlocks' => false,
                          'debugLayoutInline' => false,
                          'debugLayoutPaddingBox' => false
                      ]);

            $filename = "calendar-{$year}-{$month}.pdf";

            return $pdf->download($filename);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to generate PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export calendar to PNG (using HTML to Image conversion)
     */
    private function exportToPng($calendarData, $month, $year)
    {
        // Generate HTML content
        $html = View::make('exports.calendar-image', $calendarData)->render();

        // Create a simple HTML file that can be converted to image
        $filename = "calendar-{$year}-{$month}.html";
        $htmlPath = storage_path("app/temp/{$filename}");

        // Ensure temp directory exists
        if (!file_exists(dirname($htmlPath))) {
            mkdir(dirname($htmlPath), 0755, true);
        }

        // Save HTML file
        file_put_contents($htmlPath, $html);

        // For now, return the HTML file (can be converted to image using external tools)
        return response()->download($htmlPath, "calendar-{$year}-{$month}.html")->deleteFileAfterSend(true);
    }

    /**
     * Export calendar to JPG (using HTML to Image conversion)
     */
    private function exportToJpg($calendarData, $month, $year)
    {
        // For now, redirect to PNG export
        return $this->exportToPng($calendarData, $month, $year);
    }



    /**
     * Show export options form
     */
    public function showExportForm()
    {
        $projects = Project::all();
        return view('calendar.export', compact('projects'));
    }
}
