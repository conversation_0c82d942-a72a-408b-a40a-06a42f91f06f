<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\CalendarEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Barryvdh\DomPDF\Facade\Pdf;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Carbon\Carbon;

class CalendarExportController extends Controller
{
    /**
     * Export calendar in various formats
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'pdf'); // Default to PDF
        $month = $request->input('month', now()->month);
        $year = $request->input('year', now()->year);
        $projectId = $request->input('project_id');

        // Get calendar data
        $calendarData = $this->getCalendarData($month, $year, $projectId);

        switch ($format) {
            case 'pdf':
                return $this->exportToPdf($calendarData, $month, $year);
            case 'png':
                return $this->exportToPng($calendarData, $month, $year);
            case 'jpg':
                return $this->exportToJpg($calendarData, $month, $year);
            default:
                return $this->exportToPdf($calendarData, $month, $year);
        }
    }

    /**
     * Get calendar data for the specified month/year
     */
    private function getCalendarData($month, $year, $projectId = null)
    {
        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        // Get projects within the date range
        $projectsQuery = Project::where(function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_date', [$startDate, $endDate])
                  ->orWhereBetween('end_date', [$startDate, $endDate])
                  ->orWhere(function($q) use ($startDate, $endDate) {
                      $q->where('start_date', '<=', $startDate)
                        ->where('end_date', '>=', $endDate);
                  });
        });

        if ($projectId) {
            $projectsQuery->where('id', $projectId);
        }

        $projects = $projectsQuery->get();

        // Get calendar events
        $eventsQuery = CalendarEvent::whereBetween('start_date', [$startDate, $endDate]);
        
        if ($projectId) {
            $eventsQuery->where('project_id', $projectId);
        }

        $events = $eventsQuery->with('project')->get();

        // Generate calendar grid
        $calendar = $this->generateCalendarGrid($month, $year, $projects, $events);

        return [
            'calendar' => $calendar,
            'month' => $month,
            'year' => $year,
            'monthName' => Carbon::create($year, $month, 1)->format('F Y'),
            'projects' => $projects,
            'events' => $events
        ];
    }

    /**
     * Generate calendar grid with events
     */
    private function generateCalendarGrid($month, $year, $projects, $events)
    {
        $firstDay = Carbon::create($year, $month, 1);
        $lastDay = $firstDay->copy()->endOfMonth();
        $startCalendar = $firstDay->copy()->startOfWeek();
        $endCalendar = $lastDay->copy()->endOfWeek();

        $calendar = [];
        $current = $startCalendar->copy();

        while ($current <= $endCalendar) {
            $week = [];
            for ($i = 0; $i < 7; $i++) {
                $dayEvents = [];
                
                // Add project events
                foreach ($projects as $project) {
                    if ($project->start_date && $current->isSameDay($project->start_date)) {
                        $dayEvents[] = [
                            'type' => 'project_start',
                            'title' => 'Start: ' . $project->name,
                            'color' => '#10B981',
                            'project' => $project
                        ];
                    }
                    if ($project->end_date && $current->isSameDay($project->end_date)) {
                        $dayEvents[] = [
                            'type' => 'project_end',
                            'title' => 'End: ' . $project->name,
                            'color' => '#EF4444',
                            'project' => $project
                        ];
                    }
                }

                // Add calendar events
                foreach ($events as $event) {
                    if ($current->isSameDay($event->start_date)) {
                        $dayEvents[] = [
                            'type' => 'event',
                            'title' => $event->title,
                            'color' => '#3B82F6',
                            'event' => $event
                        ];
                    }
                }

                $week[] = [
                    'date' => $current->copy(),
                    'day' => $current->day,
                    'isCurrentMonth' => $current->month == $month,
                    'isToday' => $current->isToday(),
                    'events' => $dayEvents
                ];

                $current->addDay();
            }
            $calendar[] = $week;
        }

        return $calendar;
    }

    /**
     * Export calendar to PDF
     */
    private function exportToPdf($calendarData, $month, $year)
    {
        $html = View::make('exports.calendar-pdf', $calendarData)->render();
        
        $pdf = Pdf::loadHTML($html)
                  ->setPaper('A4', 'landscape')
                  ->setOptions([
                      'defaultFont' => 'sans-serif',
                      'isHtml5ParserEnabled' => true,
                      'isRemoteEnabled' => true
                  ]);

        $filename = "calendar-{$year}-{$month}.pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Export calendar to PNG
     */
    private function exportToPng($calendarData, $month, $year)
    {
        // First generate HTML
        $html = View::make('exports.calendar-image', $calendarData)->render();
        
        // Create image from HTML using wkhtmltoimage alternative
        $imagePath = $this->generateImageFromHtml($html, 'png', $month, $year);
        
        return response()->download($imagePath)->deleteFileAfterSend(true);
    }

    /**
     * Export calendar to JPG
     */
    private function exportToJpg($calendarData, $month, $year)
    {
        // First generate HTML
        $html = View::make('exports.calendar-image', $calendarData)->render();
        
        // Create image from HTML
        $imagePath = $this->generateImageFromHtml($html, 'jpg', $month, $year);
        
        return response()->download($imagePath)->deleteFileAfterSend(true);
    }

    /**
     * Generate image from HTML using Intervention Image
     */
    private function generateImageFromHtml($html, $format, $month, $year)
    {
        // Create a temporary HTML file
        $tempHtmlPath = storage_path("app/temp/calendar-{$year}-{$month}.html");
        
        // Ensure temp directory exists
        if (!file_exists(dirname($tempHtmlPath))) {
            mkdir(dirname($tempHtmlPath), 0755, true);
        }
        
        file_put_contents($tempHtmlPath, $html);
        
        // Use Intervention Image to create image
        $manager = new ImageManager(new Driver());
        
        // Create a canvas
        $width = 1200;
        $height = 800;
        $image = $manager->create($width, $height)->fill('#ffffff');
        
        // Add calendar content (simplified version)
        $this->drawCalendarOnImage($image, $calendarData);
        
        // Save image
        $filename = "calendar-{$year}-{$month}.{$format}";
        $imagePath = storage_path("app/temp/{$filename}");
        
        if ($format === 'jpg') {
            $image->toJpeg(90)->save($imagePath);
        } else {
            $image->toPng()->save($imagePath);
        }
        
        // Clean up temp HTML file
        if (file_exists($tempHtmlPath)) {
            unlink($tempHtmlPath);
        }
        
        return $imagePath;
    }

    /**
     * Draw calendar content on image
     */
    private function drawCalendarOnImage($image, $calendarData)
    {
        // This is a simplified version - you can enhance this with more detailed drawing
        $calendar = $calendarData['calendar'];
        $monthName = $calendarData['monthName'];
        
        // Draw title
        $image->text($monthName, 600, 50, function($font) {
            $font->size(32);
            $font->color('#000000');
            $font->align('center');
            $font->valign('top');
        });
        
        // Draw day headers
        $dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        $cellWidth = 170;
        $cellHeight = 100;
        $startX = 10;
        $startY = 100;
        
        for ($i = 0; $i < 7; $i++) {
            $x = $startX + ($i * $cellWidth);
            $image->text($dayNames[$i], $x + $cellWidth/2, $startY, function($font) {
                $font->size(16);
                $font->color('#666666');
                $font->align('center');
            });
        }
        
        // Draw calendar grid
        $currentY = $startY + 30;
        foreach ($calendar as $week) {
            for ($i = 0; $i < 7; $i++) {
                $day = $week[$i];
                $x = $startX + ($i * $cellWidth);
                
                // Draw cell border
                $image->drawRectangle($x, $currentY, $x + $cellWidth, $currentY + $cellHeight, function($draw) {
                    $draw->border(1, '#cccccc');
                });
                
                // Draw day number
                $textColor = $day['isCurrentMonth'] ? '#000000' : '#cccccc';
                $image->text($day['day'], $x + 10, $currentY + 10, function($font) use ($textColor) {
                    $font->size(14);
                    $font->color($textColor);
                });
                
                // Draw events (simplified)
                $eventY = $currentY + 30;
                foreach (array_slice($day['events'], 0, 3) as $event) { // Max 3 events per day
                    $image->text(substr($event['title'], 0, 15) . '...', $x + 5, $eventY, function($font) use ($event) {
                        $font->size(10);
                        $font->color($event['color']);
                    });
                    $eventY += 15;
                }
            }
            $currentY += $cellHeight;
        }
    }

    /**
     * Show export options form
     */
    public function showExportForm()
    {
        $projects = Project::all();
        return view('calendar.export', compact('projects'));
    }
}
