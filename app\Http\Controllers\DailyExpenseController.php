<?php

namespace App\Http\Controllers;

use App\Models\DailyExpense;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DailyExpenseController extends Controller
{
    /**
     * Display a listing of the daily expenses.
     */
    public function index(Request $request)
    {
        $projectId = $request->query('project_id');
        
        $query = DailyExpense::with(['project', 'user']);
        
        if ($projectId) {
            $query->where('project_id', $projectId);
        }
        
        $dailyExpenses = $query->latest()->paginate(10);
        $projects = Project::pluck('name', 'id');
        
        return view('daily_expenses.index', compact('dailyExpenses', 'projects', 'projectId'));
    }

    /**
     * Show the form for creating a new daily expense.
     */
    public function create(Request $request)
    {
        $projectId = $request->query('project_id');
        $projects = Project::pluck('name', 'id');
        
        // Preset the expense date to today
        $expenseDate = now()->format('Y-m-d');
        
        return view('daily_expenses.create', compact('projects', 'projectId', 'expenseDate'));
    }

    /**
     * Store a newly created daily expense in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'expense_date' => 'required|date',
            'category' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
        ]);

        // Add the current user as the creator of the expense
        $validated['user_id'] = Auth::id();

        DailyExpense::create($validated);

        return redirect()->route('daily_expenses.index', ['project_id' => $validated['project_id']])
            ->with('success', 'Daily expense recorded successfully.');
    }

    /**
     * Display the specified daily expense.
     */
    public function show(DailyExpense $dailyExpense)
    {
        return view('daily_expenses.show', compact('dailyExpense'));
    }

    /**
     * Show the form for editing the specified daily expense.
     */
    public function edit(DailyExpense $dailyExpense)
    {
        $projects = Project::pluck('name', 'id');
        return view('daily_expenses.edit', compact('dailyExpense', 'projects'));
    }

    /**
     * Update the specified daily expense in storage.
     */
    public function update(Request $request, DailyExpense $dailyExpense)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'expense_date' => 'required|date',
            'category' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
        ]);

        $dailyExpense->update($validated);

        return redirect()->route('daily_expenses.index', ['project_id' => $validated['project_id']])
            ->with('success', 'Daily expense updated successfully.');
    }

    /**
     * Remove the specified daily expense from storage.
     */
    public function destroy(DailyExpense $dailyExpense)
    {
        $projectId = $dailyExpense->project_id;
        $dailyExpense->delete();

        return redirect()->route('daily_expenses.index', ['project_id' => $projectId])
            ->with('success', 'Daily expense deleted successfully.');
    }
}
