<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Activity;
use App\Models\Notification;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        // Total proyek aktif
        $totalActiveProjects = Project::where('status', 'in_progress')->count();
        
        // Get deadline filter from request or default to 7 days
        $deadlineFilter = $request->input('deadline_filter', '7_days');
        
        // Set days based on filter
        $days = 7; // Default
        if ($deadlineFilter === '1_day') {
            $days = 1;
        } elseif ($deadlineFilter === '1_week') {
            $days = 7;
        } elseif ($deadlineFilter === '1_month') {
            $days = 30;
        }
        
        // Proyek yang mendekati deadline berdasarkan filter
        $upcomingDeadlines = Project::where('status', 'in_progress')
            ->where('end_date', '>=', now())
            ->where('end_date', '<=', now()->addDays($days))
            ->orderBy('end_date')
            ->get();
            
        // Distribusi status proyek
        $projectStatusDistribution = [
            'planned' => Project::where('status', 'planned')->count(),
            'in_progress' => Project::where('status', 'in_progress')->count(),
            'completed' => Project::where('status', 'completed')->count(),
            'on_hold' => Project::where('status', 'on_hold')->count(),
        ];
        
        // Proyek aktif dengan progress
        $activeProjects = Project::where('status', 'in_progress')
            ->orderBy('end_date')
            ->get()
            ->map(function ($project) {
                $totalDays = $project->start_date->diffInDays($project->end_date);
                // If start date is in the future, set days elapsed to 0
                $daysElapsed = now()->lt($project->start_date) ? 0 : $project->start_date->diffInDays(now());
                // Ensure progress is between 0% and 100%
                $progress = $totalDays > 0 ? max(0, min(100, round(($daysElapsed / $totalDays) * 100))) : 0;
                
                return [
                    'id' => $project->id,
                    'name' => $project->name,
                    'progress' => $progress,
                    'start_date' => $project->start_date->format('d M Y'),
                    'end_date' => $project->end_date->format('d M Y'),
                    'days_left' => (int)now()->diffInDays($project->end_date, false)
                ];
            });

        // Timeline aktivitas removed

        // Update status terbaru
        $recentStatusUpdates = Project::where('updated_at', '>=', now()->subDays(7))
            ->orderBy('updated_at', 'desc')
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'name' => $project->name,
                    'status' => $project->status,
                    'updated_at' => $project->updated_at->diffForHumans(),
                    'description' => $project->description
                ];
            });

        // Notifikasi penting
        $notifications = [
            // Proyek yang hampir deadline (3 hari atau kurang)
            'urgent_deadlines' => Project::where('status', 'in_progress')
                ->where('end_date', '>=', now())
                ->where('end_date', '<=', now()->addDays(3))
                ->get(),
            
            // Proyek yang belum ada progress dalam 7 hari
            'stalled_projects' => Project::where('status', 'in_progress')
                ->where('updated_at', '<=', now()->subDays(7))
                ->get(),
            
            // Proyek yang melewati deadline
            'overdue_projects' => Project::where('status', 'in_progress')
                ->where('end_date', '<', now())
                ->get()
        ];

        return view('dashboard.index', compact(
            'totalActiveProjects',
            'upcomingDeadlines',
            'projectStatusDistribution',
            'activeProjects',
            'recentStatusUpdates',
            'notifications'
        ));
    }
} 