<?php

namespace App\Http\Controllers;

use App\Models\Material;
use App\Models\Project;
use Illuminate\Http\Request;

class MaterialController extends Controller
{
    /**
     * Display a listing of the materials.
     */
    public function index()
    {
        $materials = Material::with('project')->latest()->paginate(10);
        return view('materials.index', compact('materials'));
    }

    /**
     * Show the form for creating a new material.
     */
    public function create()
    {
        $projects = Project::pluck('name', 'id');
        return view('materials.create', compact('projects'));
    }

    /**
     * Store a newly created material in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'cost' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
            'unit' => 'nullable|string|max:50',
            'purchase_date' => 'nullable|date'
        ]);

        Material::create($validated);

        // If this is an AJAX request, return JSON
        if ($request->ajax()) {
            $project = Project::find($validated['project_id']);
            $budget = $project->budget;
            
            return response()->json([
                'success' => true,
                'message' => 'Material added successfully',
                'total_materials_cost' => $project->getTotalMaterialsCostAttribute(),
                'budget_remaining' => $budget ? $budget->getRemainingAmountAttribute() : 0,
                'budget_usage_percentage' => $budget ? $budget->getUsagePercentageAttribute() : 0
            ]);
        }

        return redirect()->route('materials.index')
            ->with('success', 'Material added successfully.');
    }

    /**
     * Display the specified material.
     */
    public function show(Material $material)
    {
        return view('materials.show', compact('material'));
    }

    /**
     * Show the form for editing the specified material.
     */
    public function edit(Material $material)
    {
        $projects = Project::pluck('name', 'id');
        return view('materials.edit', compact('material', 'projects'));
    }

    /**
     * Update the specified material in storage.
     */
    public function update(Request $request, Material $material)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'cost' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
            'unit' => 'nullable|string|max:50',
            'purchase_date' => 'nullable|date'
        ]);

        $oldProjectId = $material->project_id;
        $material->update($validated);

        // If project changed, we need to update both projects' budgets
        if ($oldProjectId != $validated['project_id'] && $request->ajax()) {
            $oldProject = Project::find($oldProjectId);
            $newProject = Project::find($validated['project_id']);
            
            return response()->json([
                'success' => true,
                'message' => 'Material updated successfully',
                'old_project' => [
                    'id' => $oldProject->id,
                    'total_materials_cost' => $oldProject->getTotalMaterialsCostAttribute(),
                    'budget_remaining' => $oldProject->budget ? $oldProject->budget->getRemainingAmountAttribute() : 0,
                    'budget_usage_percentage' => $oldProject->budget ? $oldProject->budget->getUsagePercentageAttribute() : 0
                ],
                'new_project' => [
                    'id' => $newProject->id,
                    'total_materials_cost' => $newProject->getTotalMaterialsCostAttribute(),
                    'budget_remaining' => $newProject->budget ? $newProject->budget->getRemainingAmountAttribute() : 0,
                    'budget_usage_percentage' => $newProject->budget ? $newProject->budget->getUsagePercentageAttribute() : 0
                ]
            ]);
        }

        // If this is an AJAX request, return JSON
        if ($request->ajax()) {
            $project = Project::find($validated['project_id']);
            $budget = $project->budget;
            
            return response()->json([
                'success' => true,
                'message' => 'Material updated successfully',
                'total_materials_cost' => $project->getTotalMaterialsCostAttribute(),
                'budget_remaining' => $budget ? $budget->getRemainingAmountAttribute() : 0,
                'budget_usage_percentage' => $budget ? $budget->getUsagePercentageAttribute() : 0
            ]);
        }

        return redirect()->route('materials.index')
            ->with('success', 'Material updated successfully.');
    }

    /**
     * Remove the specified material from storage.
     */
    public function destroy(Material $material)
    {
        $projectId = $material->project_id;
        $material->delete();

        // If this is an AJAX request, return JSON
        if (request()->ajax()) {
            $project = Project::find($projectId);
            $budget = $project->budget;
            
            return response()->json([
                'success' => true,
                'message' => 'Material deleted successfully',
                'total_materials_cost' => $project->getTotalMaterialsCostAttribute(),
                'budget_remaining' => $budget ? $budget->getRemainingAmountAttribute() : 0,
                'budget_usage_percentage' => $budget ? $budget->getUsagePercentageAttribute() : 0
            ]);
        }

        return redirect()->route('materials.index')
            ->with('success', 'Material deleted successfully.');
    }

    /**
     * Get materials for a specific project.
     */
    public function getProjectMaterials($projectId)
    {
        $materials = Material::where('project_id', $projectId)->get();
        $project = Project::find($projectId);
        $budget = $project->budget;
        
        return response()->json([
            'materials' => $materials,
            'total_cost' => $project->getTotalMaterialsCostAttribute(),
            'budget_amount' => $budget ? $budget->amount : 0,
            'budget_remaining' => $budget ? $budget->getRemainingAmountAttribute() : 0,
            'budget_usage_percentage' => $budget ? $budget->getUsagePercentageAttribute() : 0
        ]);
    }
}
