<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Task; // Added Task model
use App\Models\Budget;
use App\Models\CalendarEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ProjectController extends Controller
{
    public function index(Request $request)
    {
        $status = $request->input('status');
        
        $projects = Project::query();
        
        if ($status) {
            $projects->where('status', $status);
        }
        
        $projects = $projects->orderBy('start_date', 'asc')->get();
        
        return view('projects.index', compact('projects', 'status'));
    }
    
    public function create()
    {
        return view('projects.create');
    }
    
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:planned,in_progress,completed,on_hold',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'budget_material' => 'nullable|numeric|min:0',
            'budget_jasa' => 'nullable|numeric|min:0',
            'tasks' => 'nullable|array',
            'tasks.*.name' => 'required|string',
            'tasks.*.description' => 'nullable|string',
        ]);

        // Konversi tanggal menggunakan Carbon
        if ($request->filled('start_date')) {
            $validated['start_date'] = Carbon::parse($request->start_date)->startOfDay();
        }
        
        if ($request->filled('end_date')) {
            $validated['end_date'] = Carbon::parse($request->end_date)->endOfDay();
        }

        $project = Project::create($validated);

        // Sync with Budgets table
        $budgetDate = $project->start_date ? Carbon::parse($project->start_date) : $project->created_at;

        if (isset($project->budget_material) && $project->budget_material > 0) {
            Budget::create([
                'project_id' => $project->id,
                'amount' => $project->budget_material,
                'description' => 'Anggaran Material - ' . $project->name,
                'budget_date' => $budgetDate,
                'status' => 'approved',
                'type' => 'material'
            ]);
        }

        if (isset($project->budget_jasa) && $project->budget_jasa > 0) {
            Budget::create([
                'project_id' => $project->id,
                'amount' => $project->budget_jasa,
                'description' => 'Anggaran Jasa - ' . $project->name,
                'budget_date' => $budgetDate,
                'status' => 'approved',
                'type' => 'jasa'
            ]);
        }

        // Save tasks if provided
        if ($request->has('tasks')) {
            foreach ($request->input('tasks') as $taskData) {
                if (!empty($taskData['name'])) { // Ensure task name is not empty
                    $project->tasks()->create([
                        'name' => $taskData['name'],
                        'description' => $taskData['description'] ?? null, // Assuming description might be added later
                        'status' => 'pending', // Default status for new tasks
                        'completed' => false, // Default completed status
                        // 'due_date' => null, // Optional: set if provided
                        // 'priority' => 'normal', // Optional: set a default priority
                    ]);
                }
            }
        }

        return redirect()
            ->route('projects.index')
            ->with('success', 'Proyek berhasil dibuat, anggaran disinkronkan, beserta tugas-tugasnya');
    }
    
    public function edit(Project $project)
    {
        $project->load('workers', 'tasks'); // Load tasks along with workers
        
        // Get workers not already assigned to this project
        $availableWorkers = \App\Models\Worker::whereDoesntHave('projects', function($query) use ($project) {
            $query->where('projects.id', $project->id);
        })->get();
        
        return view('projects.edit', compact('project', 'availableWorkers'));
    }
    
    public function update(Request $request, Project $project)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:planning,in_progress,completed,on_hold',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'budget_material' => 'nullable|numeric|min:0',
            'budget_jasa' => 'nullable|numeric|min:0',
            'tasks' => 'nullable|array', // Main container for all task data
            'tasks.*.id' => 'nullable|integer|exists:tasks,id', // For existing tasks, their ID
            'tasks.*.name' => 'nullable|string|max:255', // Name for any task (new or existing)
            'delete_tasks' => 'nullable|array', // Array of task IDs to be deleted
            'delete_tasks.*' => 'integer|exists:tasks,id', // Each ID in delete_tasks must be a valid task ID
        ]);

        // Prepare data for the main project update
        $projectUpdateData = [
            'name' => $validatedData['name'],
            'description' => $validatedData['description'],
            'status' => $validatedData['status'],
            'budget_material' => $validatedData['budget_material'] ?? null,
            'budget_jasa' => $validatedData['budget_jasa'] ?? null,
        ];

        // Handle start and end dates using Carbon for parsing and formatting
        if (!empty($validatedData['start_date'])) {
            $projectUpdateData['start_date'] = Carbon::parse($validatedData['start_date'])->startOfDay();
        } else {
            $projectUpdateData['start_date'] = null;
        }
        
        if (!empty($validatedData['end_date'])) {
            $projectUpdateData['end_date'] = Carbon::parse($validatedData['end_date'])->endOfDay();
        } else {
            $projectUpdateData['end_date'] = null;
        }

        $project->update($projectUpdateData);

        // Refresh project model to get the most up-to-date values, especially if name changed
        $project->refresh(); 

        // Sync with Budgets table
        $budgetDate = $project->start_date ? Carbon::parse($project->start_date) : $project->created_at;
        $descMaterial = 'Anggaran Material - ' . $project->name;
        $descJasa = 'Anggaran Jasa - ' . $project->name;

        // Handle Material Budget
        if (isset($project->budget_material) && $project->budget_material > 0) {
            Budget::updateOrCreate(
                ['project_id' => $project->id, 'description' => $descMaterial],
                ['amount' => $project->budget_material, 'budget_date' => $budgetDate, 'status' => 'approved', 'type' => 'material']
            );
        } else {
            Budget::where('project_id', $project->id)->where('description', $descMaterial)->delete();
        }

        // Handle Jasa Budget
        if (isset($project->budget_jasa) && $project->budget_jasa > 0) {
            Budget::updateOrCreate(
                ['project_id' => $project->id, 'description' => $descJasa],
                ['amount' => $project->budget_jasa, 'budget_date' => $budgetDate, 'status' => 'approved', 'type' => 'jasa']
            );
        } else {
            Budget::where('project_id', $project->id)->where('description', $descJasa)->delete();
        }

        // --- Task Processing --- 

        // 1. Handle Deletions: Process tasks marked for deletion first
        if (!empty($validatedData['delete_tasks'])) {
            Task::whereIn('id', $validatedData['delete_tasks'])
                ->where('project_id', $project->id)
                ->delete();
        }

        // 2. Handle Updates and Creations
        $submittedTasksData = $request->input('tasks', []);
        foreach ($submittedTasksData as $taskKey => $taskInput) {
            if (!is_array($taskInput) || empty($taskInput['name'])) {
                continue;
            }
            $taskName = $taskInput['name'];
            if (isset($taskInput['id']) && is_numeric($taskInput['id'])) {
                $taskId = $taskInput['id'];
                $task = Task::where('id', $taskId)->where('project_id', $project->id)->first();
                if ($task && $task->name !== $taskName) {
                    $task->update(['name' => $taskName]);
                }
            } elseif (strpos($taskKey, 'new_') === 0) {
                $project->tasks()->create([
                    'name' => $taskName,
                    'description' => null,
                    'status' => 'pending',
                    'completed' => false,
                ]);
            }
        }

        return redirect()
            ->route('projects.index')
            ->with('success', 'Proyek berhasil diperbarui, anggaran disinkronkan, beserta detail tugasnya.');
    }
    
    public function destroy(Project $project)
    {
        $project->delete();

        return redirect()
            ->route('projects.index')
            ->with('success', 'Proyek berhasil dihapus.');
    }
    
    /**
     * Mark a project as completed
     */
    public function markAsCompleted(Project $project)
    {
        $project->update([
            'status' => 'completed'
        ]);
        
        return redirect()
            ->route('projects.index')
            ->with('success', 'Proyek berhasil ditandai sebagai selesai');
    }
    
    public function budgets()
    {
        $budgets = Budget::all();
        return view('projects.budgets', compact('budgets'));
    }
    
    public function calendar(Request $request)
    {
        $date = $request->input('date') ? Carbon::parse($request->input('date')) : Carbon::now();
        $selectedProjectId = $request->input('project_id');
        
        $projects = Project::all();
        
        // Filter proyek jika ada yang dipilih
        $displayedProjects = $selectedProjectId 
            ? Project::where('id', $selectedProjectId)->get()
            : $projects;
            
        $activeProjects = Project::where('status', 'in_progress')
            ->when($selectedProjectId, function($query) use ($selectedProjectId) {
                return $query->where('id', $selectedProjectId);
            })
            ->orderBy('start_date', 'asc')
            ->get();
            
        return view('projects.calendar', compact('projects', 'activeProjects', 'date', 'selectedProjectId', 'displayedProjects'));
    }

    public function updateDate(Request $request, Project $project)
    {
        try {
            $request->validate([
                'old_date' => 'required|date',
                'new_date' => 'required|date'
            ]);

            $oldDate = Carbon::parse($request->old_date);
            $newDate = Carbon::parse($request->new_date);
            $daysDiff = $newDate->diffInDays($oldDate, false);

            DB::beginTransaction();

            // Update tanggal proyek
            if ($project->start_date && $project->start_date->format('Y-m-d') == $oldDate->format('Y-m-d')) {
                $project->start_date = $newDate;
                $project->end_date = $project->end_date->addDays($daysDiff);
            } elseif ($project->end_date && $project->end_date->format('Y-m-d') == $oldDate->format('Y-m-d')) {
                $project->end_date = $newDate;
            }

            // Validasi tanggal
            if ($project->start_date >= $project->end_date) {
                throw new \Exception('Tanggal mulai tidak boleh lebih besar dari tanggal selesai');
            }

            // Activities code removed

            $project->save();
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tanggal berhasil diperbarui'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    public function exportCalendar()
    {
        $projects = Project::all();
        $calendar = "BEGIN:VCALENDAR\r\n";
        $calendar .= "VERSION:2.0\r\n";
        $calendar .= "PRODID:-//DisaCloud//Project Calendar//EN\r\n";
        
        foreach ($projects as $project) {
            if ($project->start_date) {
                $calendar .= "BEGIN:VEVENT\r\n";
                $calendar .= "UID:" . uniqid() . "@disacloud.com\r\n";
                $calendar .= "DTSTAMP:" . now()->format('Ymd\THis\Z') . "\r\n";
                $calendar .= "DTSTART:" . $project->start_date->format('Ymd\THis\Z') . "\r\n";
                $calendar .= "SUMMARY:Mulai: " . $project->name . "\r\n";
                $calendar .= "DESCRIPTION:" . $project->description . "\r\n";
                $calendar .= "END:VEVENT\r\n";
            }
            
            if ($project->end_date) {
                $calendar .= "BEGIN:VEVENT\r\n";
                $calendar .= "UID:" . uniqid() . "@disacloud.com\r\n";
                $calendar .= "DTSTAMP:" . now()->format('Ymd\THis\Z') . "\r\n";
                $calendar .= "DTSTART:" . $project->end_date->format('Ymd\THis\Z') . "\r\n";
                $calendar .= "SUMMARY:Deadline: " . $project->name . "\r\n";
                $calendar .= "DESCRIPTION:" . $project->description . "\r\n";
                $calendar .= "END:VEVENT\r\n";
            }
        }
        
        $calendar .= "END:VCALENDAR";
        
        return response($calendar)
            ->header('Content-Type', 'text/calendar; charset=utf-8')
            ->header('Content-Disposition', 'attachment; filename="project-calendar.ics"');
    }

    public function active()
    {
        $projects = Project::where('status', 'in_progress')
            ->orderBy('start_date', 'asc')
            ->get();
        return view('projects.active', compact('projects'));
    }
} 