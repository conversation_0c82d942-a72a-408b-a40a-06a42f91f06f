<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\DailyReport;
use App\Models\Task;
use App\Models\Material;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ReportController extends Controller
{
    public function index()
    {
        $projects = Project::orderBy('status')->orderBy('start_date', 'desc')->get();
        return view('reports.index', compact('projects'));
    }

    public function show(Project $project)
    {
        // Check if project has start and end dates
        if (!$project->start_date || !$project->end_date) {
            return redirect()
                ->route('reports.index')
                ->with('error', 'Proyek ini tidak memiliki tanggal mulai atau tanggal selesai yang valid.');
        }
        
        $startDate = Carbon::parse($project->start_date)->startOfDay();
        $endDate = Carbon::parse($project->end_date)->startOfDay();
        $today = Carbon::today();
        
        // <PERSON><PERSON>lkan semua tanggal dalam rentang proyek
        $reportDates = [];
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $reportDates[] = [
                'date' => clone $currentDate,
                'report' => $project->dailyReports()->whereDate('report_date', $currentDate)->first(),
                'is_past_or_today' => $currentDate->lte($today)
            ];
            $currentDate->addDay();
        }
        
        // Get tasks for this project for the checklist
        $tasks = $project->tasks()->orderBy('priority', 'desc')->get();
        $taskCompletionPercentage = $project->getTaskCompletionPercentageAttribute();
        
        // Get budget information
        $budget = $project->budget;
        $materials = $project->materials;
        $totalMaterialsCost = $project->getTotalMaterialsCostAttribute();
        $budgetRemaining = $budget ? $budget->getRemainingAmountAttribute() : 0;
        $budgetUsagePercentage = $budget ? $budget->getUsagePercentageAttribute() : 0;

        return view('reports.show', compact(
            'project', 
            'reportDates', 
            'tasks', 
            'taskCompletionPercentage', 
            'budget', 
            'materials', 
            'totalMaterialsCost', 
            'budgetRemaining', 
            'budgetUsagePercentage'
        ));
    }

    public function create(Project $project, Request $request)
    {
        $date = Carbon::parse($request->date);
        
        // Ambil laporan sebelumnya jika ada
        $previousReport = $project->dailyReports()
            ->whereDate('report_date', '<', $date)
            ->orderBy('report_date', 'desc')
            ->first();
            
        $tasks = $project->tasks()->orderBy('created_at')->get(); // Fetch tasks for the project

        return view('reports.create', compact('project', 'date', 'previousReport', 'tasks')); // Add tasks to compact
    }

    public function store(Project $project, Request $request)
    {
        $validated = $request->validate([
            'report_date' => 'required|date',
            'activities_done' => 'required|string',
            'challenges' => 'nullable|string',
            'next_plan' => 'required|string',
            'tasks_completed' => 'nullable|array', // For submitted task checkboxes
        ]);

        // Hitung progress otomatis dari tugas
        $tasksForProject = $project->tasks; // Eloquent collection
        $submittedTaskCompletionMap = $request->input('tasks_completed', []); // e.g., [task_id => "on", ...]
        $totalTasks = $tasksForProject->count();
        $completedTasks = 0;
        foreach ($tasksForProject as $task) {
            $isSubmittedAsCompleted = array_key_exists($task->id, $submittedTaskCompletionMap);
            if ($isSubmittedAsCompleted) {
                if (!$task->completed || $task->status !== 'completed') {
                    $task->completed = true;
                    $task->status = 'completed';
                    $task->save();
                }
                $completedTasks++;
            } else {
                if ($task->completed || $task->status === 'completed') {
                    $task->completed = false;
                    $task->status = 'pending';
                    $task->save();
                }
            }
        }
        $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;
        $validated['progress_percentage'] = $progress;

        $project->dailyReports()->create($validated);

        return redirect()
            ->route('reports.show', $project)
            ->with('success', 'Laporan harian berhasil disimpan beserta status tugas.');
    }

    public function edit(Project $project, DailyReport $report)
    {
        $tasks = $project->tasks()->orderBy('created_at')->get();
        return view('reports.edit', compact('project', 'report', 'tasks'));
    }

    public function update(Project $project, DailyReport $report, Request $request)
    {
        $validated = $request->validate([
            'activities_done' => 'required|string',
            'challenges' => 'nullable|string',
            'next_plan' => 'required|string',
            'tasks_completed' => 'nullable|array',
        ]);

        // Hitung progress otomatis dari tugas
        $tasksForProject = $project->tasks;
        $submittedTaskCompletionMap = $request->input('tasks_completed', []);
        $totalTasks = $tasksForProject->count();
        $completedTasks = 0;
        foreach ($tasksForProject as $task) {
            $isSubmittedAsCompleted = array_key_exists($task->id, $submittedTaskCompletionMap);
            if ($isSubmittedAsCompleted) {
                if (!$task->completed || $task->status !== 'completed') {
                    $task->completed = true;
                    $task->status = 'completed';
                    $task->save();
                }
                $completedTasks++;
            } else {
                if ($task->completed || $task->status === 'completed') {
                    $task->completed = false;
                    $task->status = 'pending';
                    $task->save();
                }
            }
        }
        $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;
        $validated['progress_percentage'] = $progress;

        $report->update($validated);

        return redirect()
            ->route('reports.show', $project)
            ->with('success', 'Laporan harian berhasil diperbarui');
    }
    
    /**
     * Project dashboard with task and budget overview
     */
    public function dashboard(Project $project)
    {
        // Load project with relationships
        $project->load(['tasks', 'materials', 'budget', 'dailyReports' => function($query) {
            $query->orderBy('report_date', 'desc')->limit(5);
        }]);
        
        // Task statistics
        $tasks = $project->tasks;
        $taskCompletionPercentage = $project->getTaskCompletionPercentageAttribute();
        $pendingTasks = $tasks->where('status', 'pending')->count();
        $inProgressTasks = $tasks->where('status', 'in_progress')->count();
        $completedTasks = $tasks->where('status', 'completed')->count();
        
        // Budget statistics
        $budget = $project->budget;
        $materials = $project->materials;
        $totalMaterialsCost = $project->getTotalMaterialsCostAttribute();
        $budgetRemaining = $budget ? $budget->getRemainingAmountAttribute() : 0;
        $budgetUsagePercentage = $budget ? $budget->getUsagePercentageAttribute() : 0;
        
        // Recent daily reports
        $recentReports = $project->dailyReports;
        
        return view('reports.dashboard', compact(
            'project',
            'tasks',
            'taskCompletionPercentage',
            'pendingTasks',
            'inProgressTasks',
            'completedTasks',
            'budget',
            'materials',
            'totalMaterialsCost',
            'budgetRemaining',
            'budgetUsagePercentage',
            'recentReports'
        ));
    }
    
    /**
     * Update task status via AJAX
     */
    public function updateTaskStatus(Project $project, Task $task, Request $request)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed',
        ]);

        $task->update(['status' => $validated['status']]);
        
        // Calculate new task completion percentage
        $taskCompletionPercentage = $project->getTaskCompletionPercentageAttribute();

        return response()->json([
            'success' => true,
            'message' => 'Status tugas berhasil diperbarui',
            'task' => $task,
            'taskCompletionPercentage' => $taskCompletionPercentage
        ]);
    }

    /**
     * Update massal progress_percentage pada semua laporan harian sesuai status tugas terakhir.
     */
    public function updateAllReportsProgress()
    {
        $projects = Project::with('tasks', 'dailyReports')->get();
        foreach ($projects as $project) {
            $totalTasks = $project->tasks->count();
            $completedTasks = $project->tasks->where('status', 'completed')->count();
            $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;
            foreach ($project->dailyReports as $report) {
                $report->progress_percentage = $progress;
                $report->save();
            }
        }
        return 'Update massal progress laporan harian selesai.';
    }
} 