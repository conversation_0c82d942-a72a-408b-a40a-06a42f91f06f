<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class SimpleCalendarExportController extends Controller
{
    /**
     * Show export form
     */
    public function showExportForm()
    {
        $projects = Project::all();
        return view('calendar.simple-export', compact('projects'));
    }

    /**
     * Export calendar
     */
    public function export(Request $request)
    {
        $format = $request->input('format', 'pdf');
        $month = $request->input('month', now()->month);
        $year = $request->input('year', now()->year);
        $projectId = $request->input('project_id');

        // Get calendar data
        $calendarData = $this->getCalendarData($month, $year, $projectId);

        if ($format === 'pdf') {
            return $this->exportToPdf($calendarData, $month, $year);
        } else {
            // For PNG/JPG, return HTML for now
            return $this->exportToHtml($calendarData, $month, $year);
        }
    }

    /**
     * Get calendar data
     */
    private function getCalendarData($month, $year, $projectId = null)
    {
        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        // Get projects within the date range
        $projectsQuery = Project::where(function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_date', [$startDate, $endDate])
                  ->orWhereBetween('end_date', [$startDate, $endDate])
                  ->orWhere(function($q) use ($startDate, $endDate) {
                      $q->where('start_date', '<=', $startDate)
                        ->where('end_date', '>=', $endDate);
                  });
        });

        if ($projectId) {
            $projectsQuery->where('id', $projectId);
        }

        $projects = $projectsQuery->get();

        // Generate calendar grid
        $calendar = $this->generateCalendarGrid($month, $year, $projects);

        return [
            'calendar' => $calendar,
            'month' => $month,
            'year' => $year,
            'monthName' => Carbon::create($year, $month, 1)->format('F Y'),
            'projects' => $projects,
        ];
    }

    /**
     * Generate calendar grid
     */
    private function generateCalendarGrid($month, $year, $projects)
    {
        $firstDay = Carbon::create($year, $month, 1);
        $lastDay = $firstDay->copy()->endOfMonth();
        $startCalendar = $firstDay->copy()->startOfWeek();
        $endCalendar = $lastDay->copy()->endOfWeek();

        $calendar = [];
        $current = $startCalendar->copy();

        while ($current <= $endCalendar) {
            $week = [];
            for ($i = 0; $i < 7; $i++) {
                $dayEvents = [];
                
                // Add project events
                foreach ($projects as $project) {
                    if ($project->start_date && $current->isSameDay($project->start_date)) {
                        $dayEvents[] = [
                            'type' => 'project_start',
                            'title' => 'Start: ' . $project->name,
                            'color' => '#10B981',
                            'project' => $project
                        ];
                    }
                    if ($project->end_date && $current->isSameDay($project->end_date)) {
                        $dayEvents[] = [
                            'type' => 'project_end',
                            'title' => 'End: ' . $project->name,
                            'color' => '#EF4444',
                            'project' => $project
                        ];
                    }
                }

                $week[] = [
                    'date' => $current->copy(),
                    'day' => $current->day,
                    'isCurrentMonth' => $current->month == $month,
                    'isToday' => $current->isToday(),
                    'events' => $dayEvents
                ];

                $current->addDay();
            }
            $calendar[] = $week;
        }

        return $calendar;
    }

    /**
     * Export to PDF
     */
    private function exportToPdf($calendarData, $month, $year)
    {
        try {
            $html = View::make('exports.simple-calendar-pdf', $calendarData)->render();
            
            $pdf = Pdf::loadHTML($html)
                      ->setPaper('A4', 'landscape')
                      ->setOptions([
                          'defaultFont' => 'DejaVu Sans',
                          'isHtml5ParserEnabled' => true,
                          'isRemoteEnabled' => false
                      ]);

            $filename = "calendar-{$year}-{$month}.pdf";
            
            return $pdf->download($filename);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to generate PDF',
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }

    /**
     * Export to HTML (for PNG/JPG alternative)
     */
    private function exportToHtml($calendarData, $month, $year)
    {
        $html = View::make('exports.simple-calendar-pdf', $calendarData)->render();
        
        $filename = "calendar-{$year}-{$month}.html";
        $filePath = storage_path("app/temp/{$filename}");
        
        // Ensure temp directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }
        
        file_put_contents($filePath, $html);
        
        return response()->download($filePath, $filename)->deleteFileAfterSend(true);
    }
}
