<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Project;
use Illuminate\Http\Request;

class TaskController extends Controller
{
    /**
     * Display a listing of the tasks.
     */
    public function index()
    {
        $tasks = Task::with('project')->latest()->paginate(10);
        return view('tasks.index', compact('tasks'));
    }

    /**
     * Show the form for creating a new task.
     */
    public function create()
    {
        $projects = Project::pluck('name', 'id');
        return view('tasks.create', compact('projects'));
    }

    /**
     * Store a newly created task in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date',
            'status' => 'required|in:pending,in_progress,completed',
            'priority' => 'nullable|integer|min:0|max:10',
            'completed' => 'boolean'
        ]);

        // If completed checkbox is not present in the request, set it to false
        if (!isset($validated['completed'])) {
            $validated['completed'] = false;
        }
        
        // If status is 'completed', also set the completed flag to true
        if ($validated['status'] === 'completed') {
            $validated['completed'] = true;
        }

        Task::create($validated);

        return redirect()->route('tasks.index')
            ->with('success', 'Task created successfully.');
    }

    /**
     * Display the specified task.
     */
    public function show(Task $task)
    {
        return view('tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the specified task.
     */
    public function edit(Task $task)
    {
        $projects = Project::pluck('name', 'id');
        return view('tasks.edit', compact('task', 'projects'));
    }

    /**
     * Update the specified task in storage.
     */
    public function update(Request $request, Task $task)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date',
            'status' => 'required|in:pending,in_progress,completed',
            'priority' => 'nullable|integer|min:0|max:10',
            'completed' => 'boolean'
        ]);

        // If completed checkbox is not present in the request, set it to false
        if (!isset($validated['completed'])) {
            $validated['completed'] = false;
        }
        
        // If status is 'completed', also set the completed flag to true
        if ($validated['status'] === 'completed') {
            $validated['completed'] = true;
        }

        $task->update($validated);

        return redirect()->route('tasks.index')
            ->with('success', 'Task updated successfully.');
    }

    /**
     * Remove the specified task from storage.
     */
    public function destroy(Task $task)
    {
        $task->delete();

        return redirect()->route('tasks.index')
            ->with('success', 'Task deleted successfully.');
    }

    /**
     * Update the status of a task via AJAX.
     */
    public function updateStatus(Request $request, Task $task)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed',
        ]);

        $update = ['status' => $validated['status']];
        
        // If completed is present in the request, update it
        if (isset($request->completed)) {
            $update['completed'] = $request->completed;
        } else if ($validated['status'] === 'completed') {
            // If status is 'completed', also set the completed flag to true
            $update['completed'] = true;
        }

        $task->update($update);

        return response()->json([
            'success' => true,
            'message' => 'Task status updated successfully',
            'task' => $task,
            'project_completion' => $task->project->getTaskCompletionPercentageAttribute()
        ]);
    }
}
