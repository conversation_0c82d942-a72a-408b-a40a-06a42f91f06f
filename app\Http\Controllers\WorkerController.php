<?php

namespace App\Http\Controllers;

use App\Models\Worker;
use App\Models\Project;
use Illuminate\Http\Request;
use Carbon\Carbon;

class WorkerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $workers = Worker::with('projects')->latest()->get();
        $projects = Project::all();
        return view('workers.index', compact('workers', 'projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $projects = Project::all();
        return view('workers.create', compact('projects'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'specialization' => 'required|string|max:255',
            'email' => 'required|email|unique:workers,email',
            'phone' => 'nullable|string|max:20',
            'skills' => 'nullable|string',
            'project_ids' => 'nullable|array',
            'project_ids.*' => 'exists:projects,id'
        ]);

        $worker = Worker::create([
            'name' => $validated['name'],
            'specialization' => $validated['specialization'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'skills' => $validated['skills']
        ]);

        // Only attach projects if project_ids is provided
        if ($request->has('project_ids') && !empty($request->project_ids)) {
            $worker->projects()->attach($request->project_ids);
        }

        return redirect()
            ->route('workers.index')
            ->with('success', 'Pekerja berhasil ditambahkan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Worker $worker)
    {
        $projects = Project::all();
        return view('workers.edit', compact('worker', 'projects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Worker $worker)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'specialization' => 'required|string|max:255',
            'email' => 'required|email|unique:workers,email,' . $worker->id,
            'phone' => 'nullable|string|max:20',
            'skills' => 'nullable|string',
            'project_ids' => 'nullable|array',
            'project_ids.*' => 'exists:projects,id'
            // Date fields removed as requested
        ]);

        $worker->update([
            'name' => $validated['name'],
            'specialization' => $validated['specialization'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'skills' => $validated['skills']
        ]);

        // Only sync projects if project_ids is provided
        if ($request->has('project_ids')) {
            $worker->projects()->sync($request->project_ids);
        } else {
            // If no project_ids provided, detach all projects
            $worker->projects()->detach();
        }

        return redirect()
            ->route('workers.index')
            ->with('success', 'Pekerja berhasil diperbarui');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Worker $worker)
    {
        $worker->delete();

        return redirect()
            ->route('workers.index')
            ->with('success', 'Pekerja berhasil dihapus');
    }
    
    /**
     * Add existing workers to a project
     */
    public function addExistingWorkers(Request $request, Project $project)
    {
        $validated = $request->validate([
            'worker_ids' => 'required|array',
            'worker_ids.*' => 'exists:workers,id'
        ]);
        
        // Attach the selected workers to the project
        $project->workers()->attach($request->worker_ids);
        
        return redirect()
            ->route('projects.edit', $project)
            ->with('success', 'Pekerja berhasil ditambahkan ke proyek');
    }
    
    /**
     * Detach a worker from a project without deleting the worker
     */
    public function detachFromProject(Project $project, Worker $worker)
    {
        // Detach the worker from the project
        $project->workers()->detach($worker->id);
        
        return redirect()
            ->route('projects.edit', $project)
            ->with('success', 'Pekerja berhasil dihapus dari proyek');
    }
}
