<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Budget extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'amount',
        'estimated_profit',
        'profit_percentage',
        'description',
        'budget_date',
        'invoice_file',
        'status',
        'type' // Added type field
    ];
    
    protected $appends = [
        'used_amount',
        'remaining_amount',
        'usage_percentage'
    ];

    /**
     * Get the project that owns the budget.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
    
    /**
     * Get the used amount from materials.
     */
    public function getUsedAmountAttribute()
    {
        return $this->project ? $this->project->getTotalMaterialsCostAttribute() : 0;
    }
    
    /**
     * Get the remaining amount in the budget.
     */
    public function getRemainingAmountAttribute()
    {
        return $this->amount - $this->getUsedAmountAttribute();
    }
    
    /**
     * Get the budget usage percentage.
     */
    public function getUsagePercentageAttribute()
    {
        if ($this->amount <= 0) {
            return 0;
        }
        
        $percentage = ($this->getUsedAmountAttribute() / $this->amount) * 100;
        return round($percentage, 2);
    }
} 