<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'report_date',
        'activities_done',
        'challenges',
        'next_plan',
        'progress_percentage',
        'status'
    ];

    protected $casts = [
        'report_date' => 'date',
        'progress_percentage' => 'integer'
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }
} 