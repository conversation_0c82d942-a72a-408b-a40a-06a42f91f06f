<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Material extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'name',
        'description',
        'cost',
        'quantity',
        'unit',
        'purchase_date'
    ];

    protected $casts = [
        'purchase_date' => 'date',
    ];

    /**
     * Get the project that owns the material.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the total cost of the material.
     */
    public function getTotalCostAttribute()
    {
        return $this->cost * $this->quantity;
    }
}
