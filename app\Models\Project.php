<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'status',
        'priority',
        'start_date',
        'end_date',
        'progress',
        'budget_material',
        'budget_jasa'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function workers()
    {
        return $this->belongsToMany(Worker::class);
    }

    // Activities relationship removed

    public function dailyReports()
    {
        return $this->hasMany(DailyReport::class);
    }

    public function teams()
    {
        return $this->hasMany(Team::class);
    }

    public function budget()
    {
        return $this->hasOne(Budget::class);
    }

    public function calendarEvents()
    {
        return $this->hasMany(CalendarEvent::class);
    }
    
    /**
     * Get the daily expenses for the project.
     */
    public function dailyExpenses()
    {
        return $this->hasMany(DailyExpense::class);
    }
    
    /**
     * Get the tasks for the project.
     */
    public function tasks()
    {
        return $this->hasMany(Task::class);
    }
    
    /**
     * Get the materials for the project.
     */
    public function materials()
    {
        return $this->hasMany(Material::class);
    }
    
    /**
     * Get the total materials cost for the project.
     */
    public function getTotalMaterialsCostAttribute()
    {
        return $this->materials->sum(function($material) {
            return $material->cost * $material->quantity;
        });
    }
    
    /**
     * Get the task completion percentage for the project.
     */
    public function getTaskCompletionPercentageAttribute()
    {
        $totalTasks = $this->tasks->count();
        if ($totalTasks === 0) {
            return 0;
        }
        
        $completedTasks = $this->tasks->where('status', 'completed')->count();
        return round(($completedTasks / $totalTasks) * 100);
    }
} 