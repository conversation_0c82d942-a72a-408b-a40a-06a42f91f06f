<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Worker extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'specialization',
        'email',
        'phone',
        'skills',
        'join_date',
        'end_date'
    ];

    protected $casts = [
        'join_date' => 'date',
        'end_date' => 'date'
    ];

    public function projects()
    {
        return $this->belongsToMany(Project::class);
    }

    public function activities()
    {
        return $this->hasMany(Activity::class, 'assigned_to');
    }
}
