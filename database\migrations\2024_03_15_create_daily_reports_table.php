<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('daily_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->date('report_date');
            $table->text('activities_done')->nullable();
            $table->text('challenges')->nullable();
            $table->text('next_plan')->nullable();
            $table->integer('progress_percentage')->default(0);
            $table->string('status')->default('pending');
            $table->timestamps();

            // Memastikan satu proyek hanya memiliki satu report per hari
            $table->unique(['project_id', 'report_date']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('daily_reports');
    }
}; 