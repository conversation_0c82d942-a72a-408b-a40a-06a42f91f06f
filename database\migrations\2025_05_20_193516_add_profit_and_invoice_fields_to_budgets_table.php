<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('budgets', function (Blueprint $table) {
            $table->decimal('estimated_profit', 15, 2)->nullable()->after('amount');
            $table->decimal('profit_percentage', 5, 2)->nullable()->after('estimated_profit');
            $table->string('invoice_file')->nullable()->after('budget_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('budgets', function (Blueprint $table) {
            $table->dropColumn(['estimated_profit', 'profit_percentage', 'invoice_file']);
        });
    }
};
