<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('budgets', 'type')) {
            Schema::table('budgets', function (Blueprint $table) {
                $table->string('type')->nullable()->after('status'); // Add type column
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('budgets', 'type')) {
            Schema::table('budgets', function (Blueprint $table) {
                $table->dropColumn('type'); // Drop type column
            });
        }
    }
};
