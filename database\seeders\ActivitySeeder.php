<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\Project;
use Carbon\Carbon;

class ActivitySeeder extends Seeder
{
    public function run()
    {
        $projects = Project::all();

        foreach ($projects as $project) {
            // Aktivitas untuk setiap proyek
            $activities = [
                [
                    'name' => 'Analisis <PERSON>',
                    'description' => 'Mengumpulkan dan menganalisis kebutuhan sistem',
                    'start_date' => Carbon::parse($project->start_date),
                    'end_date' => Carbon::parse($project->start_date)->addDays(14),
                    'status' => 'planned',
                    'priority' => 'high',
                ],
                [
                    'name' => 'Desain Sistem',
                    'description' => 'Merancang arsitektur dan desain sistem',
                    'start_date' => Carbon::parse($project->start_date)->addDays(15),
                    'end_date' => Carbon::parse($project->start_date)->addDays(30),
                    'status' => 'planned',
                    'priority' => 'high',
                ],
                [
                    'name' => 'Implementasi',
                    'description' => 'Pengembangan fitur-fitur utama',
                    'start_date' => Carbon::parse($project->start_date)->addDays(31),
                    'end_date' => Carbon::parse($project->end_date)->subDays(14),
                    'status' => 'planned',
                    'priority' => 'medium',
                ],
                [
                    'name' => 'Testing',
                    'description' => 'Pengujian sistem dan perbaikan bug',
                    'start_date' => Carbon::parse($project->end_date)->subDays(13),
                    'end_date' => Carbon::parse($project->end_date)->subDays(7),
                    'status' => 'planned',
                    'priority' => 'medium',
                ],
                [
                    'name' => 'Deployment',
                    'description' => 'Persiapan dan peluncuran sistem',
                    'start_date' => Carbon::parse($project->end_date)->subDays(6),
                    'end_date' => Carbon::parse($project->end_date),
                    'status' => 'planned',
                    'priority' => 'high',
                ],
            ];

            foreach ($activities as $activity) {
                Activity::create([
                    ...$activity,
                    'project_id' => $project->id,
                ]);
            }
        }
    }
} 