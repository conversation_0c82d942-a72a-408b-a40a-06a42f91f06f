<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Hapus data yang ada
        if(User::where('email', '<EMAIL>')->exists()) {
            User::where('email', '<EMAIL>')->delete();
        }

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->call([
            ProjectSeeder::class,
            UserSeeder::class,
        ]);
    }
}
