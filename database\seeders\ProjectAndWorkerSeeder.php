<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Project;
use App\Models\Worker;
use Carbon\Carbon;

class ProjectAndWorkerSeeder extends Seeder
{
    public function run()
    {
        // Membuat pekerja dengan spesialisasi berbeda
        $workers = [
            [
                'name' => '<PERSON><PERSON>o',
                'specialization' => 'Backend Developer',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Dewi Putri',
                'specialization' => 'Frontend Developer',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Ahmad Rizki',
                'specialization' => 'Mobile Developer',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON>i <PERSON>',
                'specialization' => 'UI/UX Designer',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Eko Prasetyo',
                'specialization' => 'DevOps Engineer',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON>',
                'specialization' => 'Quality Assurance',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON><PERSON>',
                'specialization' => 'System Analyst',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Maya Indah',
                'specialization' => 'Database Administrator',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($workers as $worker) {
            Worker::create($worker);
        }

        // Membuat proyek dengan jadwal yang tidak bersinggungan
        $projects = [
            [
                'name' => 'Sistem Manajemen Inventori',
                'description' => 'Pengembangan sistem inventori terintegrasi dengan fitur tracking real-time',
                'start_date' => '2024-01-01',
                'end_date' => '2024-12-31',
                'status' => 'in_progress',
                'priority' => 'high',
                'progress' => 30
            ],
            [
                'name' => 'Aplikasi Mobile E-Commerce',
                'description' => 'Aplikasi mobile untuk platform e-commerce dengan fitur AR untuk preview produk',
                'start_date' => '2024-03-01',
                'end_date' => '2024-08-31',
                'status' => 'in_progress',
                'priority' => 'medium',
                'progress' => 20
            ],
            [
                'name' => 'Platform LMS',
                'description' => 'Learning Management System dengan fitur video conference dan tracking progress',
                'start_date' => '2024-06-01',
                'end_date' => '2024-10-31',
                'status' => 'planned',
                'priority' => 'high',
                'progress' => 0
            ],
            [
                'name' => 'Sistem Monitoring IoT',
                'description' => 'Sistem monitoring perangkat IoT dengan dashboard real-time dan analitik',
                'start_date' => '2024-09-01',
                'end_date' => '2024-12-31',
                'status' => 'planned',
                'priority' => 'medium',
                'progress' => 0
            ],
        ];

        foreach ($projects as $project) {
            Project::create($project);
        }

        // Distribusi pekerja ke proyek
        $projectAssignments = [
            1 => [1, 2, 4, 6], // Sistem Manajemen Inventori
            2 => [2, 3, 4, 7], // Aplikasi Mobile E-Commerce
            3 => [1, 2, 5, 8], // Platform LMS
            4 => [1, 3, 5, 6], // Sistem Monitoring IoT
        ];

        foreach ($projectAssignments as $projectId => $workerIds) {
            $project = Project::find($projectId);
            $project->workers()->attach($workerIds);
        }
    }
} 