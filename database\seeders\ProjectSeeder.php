<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Project;
use App\Models\DailyReport;
use Carbon\Carbon;

class ProjectSeeder extends Seeder
{
    public function run()
    {
        // Hapus data yang ada
        DailyReport::query()->delete();
        Project::query()->delete();

        // Proyek 1: Aplikasi Mobile E-Commerce
        $project1 = Project::create([
            'name' => 'Aplikasi Mobile E-Commerce',
            'description' => 'Pengembangan aplikasi mobile e-commerce dengan fitur pembayaran digital',
            'status' => 'in_progress',
            'priority' => 'high',
            'start_date' => Carbon::create(2025, 6, 1),
            'end_date' => Carbon::create(2025, 6, 28),
            'progress' => 20
        ]);

        // Proyek 2: Sistem Manajemen Inventori
        $project2 = Project::create([
            'name' => 'Sistem Manajemen Inventori',
            'description' => 'Pengembangan sistem manajemen inventori untuk toko retail',
            'status' => 'in_progress',
            'priority' => 'medium',
            'start_date' => Carbon::create(2025, 6, 15),
            'end_date' => Carbon::create(2025, 7, 15),
            'progress' => 30
        ]);

        // Buat beberapa laporan harian untuk proyek 1
        $startDate = Carbon::create(2025, 6, 1);
        $endDate = Carbon::create(2025, 6, 28);
        $totalDays = $startDate->diffInDays($endDate);
        
        while ($startDate <= $endDate) {
            if ($startDate->isWeekday()) {
                $daysPassed = Carbon::create(2025, 6, 1)->diffInDays($startDate);
                $progress = round(($daysPassed / $totalDays) * 100);
                
                DailyReport::create([
                    'project_id' => $project1->id,
                    'report_date' => $startDate->copy(),
                    'activities_done' => 'Mengerjakan fitur ' . $startDate->format('d M'),
                    'challenges' => 'Beberapa tantangan teknis',
                    'next_plan' => 'Melanjutkan pengembangan fitur',
                    'progress_percentage' => min(100, max(0, $progress)),
                    'status' => 'completed'
                ]);
            }
            $startDate->addDay();
        }

        // Buat beberapa laporan harian untuk proyek 2
        $startDate = Carbon::create(2025, 6, 15);
        $endDate = Carbon::create(2025, 7, 15);
        $totalDays = $startDate->diffInDays($endDate);
        
        while ($startDate <= $endDate) {
            if ($startDate->isWeekday()) {
                $daysPassed = Carbon::create(2025, 6, 15)->diffInDays($startDate);
                $progress = round(($daysPassed / $totalDays) * 100);
                
                DailyReport::create([
                    'project_id' => $project2->id,
                    'report_date' => $startDate->copy(),
                    'activities_done' => 'Mengerjakan modul ' . $startDate->format('d M'),
                    'challenges' => 'Beberapa kendala integrasi',
                    'next_plan' => 'Melanjutkan pengembangan modul',
                    'progress_percentage' => min(100, max(0, $progress)),
                    'status' => 'completed'
                ]);
            }
            $startDate->addDay();
        }
    }
} 