# 4.1 <PERSON><PERSON><PERSON> Fungsional dan Non Fungsional

## 4.1.1 Kebutuhan Fungsional

### A. Manajemen Proyek
1. **Pembuatan dan <PERSON>an Proyek**
   - Membuat proyek baru dengan informasi: nama, deskripsi, status, prioritas, tanggal mulai, tanggal selesai, progress, budget material, budget jasa
   - Mengubah status proyek: planned, in_progress, completed, on_hold
   - Menetapkan prioritas: low, medium, high
   - Menghitung dan menampilkan progress proyek

2. **Manajemen Timeline**
   - Menetapkan tanggal mulai dan selesai proyek
   - Tracking deadline proyek
   - Notifikasi proyek yang mendekati deadline (1 hari, 1 minggu, 1 bulan)
   - Identifikasi proyek yang melewati deadline

### B. Manajemen Pekerja
1. **Database Pekerja**
   - Menyimpan data pekerja: nama, spesialisasi, email, telepon, skills, tanggal bergabung, tanggal berakhir
   - Mengelola hubungan many-to-many antara pekerja dan proyek

2. **Penugasan Peker<PERSON>**
   - Menugaskan pekerja ke proyek tertentu
   - Tracking pekerja yang terlibat dalam setiap proyek

### C. Manajemen Tugas (Task)
1. **Pengelolaan Tugas**
   - Membuat tugas dengan: nama, deskripsi, due_date, status, prioritas, status completed
   - Menghubungkan tugas dengan proyek tertentu
   - Mengubah status tugas dan menandai sebagai completed

### D. Manajemen Anggaran
1. **Perencanaan Anggaran**
   - Membuat budget dengan: amount, estimated_profit, profit_percentage, description, budget_date, invoice_file, status, type
   - Menghitung used_amount berdasarkan total biaya material
   - Menghitung remaining_amount dan usage_percentage

2. **Tracking Pengeluaran**
   - Mencatat daily expenses dengan: project_id, user_id, expense_date, category, description, amount
   - Kategorisasi pengeluaran (makan, bensin, dll)

### E. Manajemen Material
1. **Inventory Material**
   - Menyimpan data material: nama, deskripsi, cost, quantity, unit, purchase_date
   - Menghubungkan material dengan proyek
   - Menghitung total cost per material (cost × quantity)

### F. Sistem Pelaporan
1. **Laporan Harian**
   - Membuat daily report dengan: report_date, activities_done, challenges, next_plan, progress_percentage, status
   - Constraint: satu proyek hanya boleh memiliki satu report per hari

2. **Dashboard Analytics**
   - Menampilkan total proyek aktif
   - Distribusi status proyek (planned, in_progress, completed, on_hold)
   - Proyek yang mendekati deadline
   - Notifikasi penting (urgent deadlines, stalled projects, overdue projects)

### G. Sistem Kalender
1. **Manajemen Event**
   - Membuat calendar events dengan: title, description, start_date, end_date, location
   - Menghubungkan event dengan proyek
   - Integrasi dengan FullCalendar untuk tampilan visual

### H. Manajemen Aktivitas
1. **Tracking Aktivitas**
   - Membuat aktivitas dengan: title, description, status, priority, start_date, due_date, assigned_to, created_by
   - Menugaskan aktivitas ke user tertentu
   - Tracking creator aktivitas

## 4.1.2 Kebutuhan Non Fungsional

### A. Keamanan (Security)
1. **Autentikasi dan Autorisasi**
   - Sistem login dengan email dan password
   - Session management dengan remember token
   - Password hashing menggunakan Laravel Hash
   - Hanya Project Manager yang dapat mengakses sistem

2. **Validasi Data**
   - Validasi input pada semua form
   - Sanitasi data untuk mencegah SQL injection
   - CSRF protection pada semua form

### B. Performa (Performance)
1. **Database Optimization**
   - Indexing pada foreign key
   - Eager loading untuk mengurangi N+1 query problem
   - Caching menggunakan Redis

2. **Response Time**
   - Waktu loading halaman maksimal 3 detik
   - Optimasi query database
   - Lazy loading untuk data yang besar

### C. Kegunaan (Usability)
1. **User Interface**
   - Responsive design menggunakan TailwindCSS
   - Interface yang intuitif dan user-friendly
   - Konsistensi dalam design pattern

2. **User Experience**
   - Feedback visual untuk setiap aksi user
   - Loading indicators untuk proses yang membutuhkan waktu
   - Error handling yang informatif

### D. Skalabilitas (Scalability)
1. **Arsitektur**
   - MVC pattern untuk separation of concerns
   - Service layer untuk business logic
   - Repository pattern untuk data access

2. **Database Design**
   - Normalisasi database untuk mengurangi redundansi
   - Foreign key constraints untuk data integrity
   - Soft delete untuk data yang sensitif

### E. Keandalan (Reliability)
1. **Error Handling**
   - Try-catch blocks untuk exception handling
   - Logging untuk debugging dan monitoring
   - Graceful degradation untuk fitur yang gagal

2. **Data Integrity**
   - Database transactions untuk operasi yang kompleks
   - Validation rules untuk memastikan data yang valid
   - Backup dan recovery procedures

### F. Maintainability
1. **Code Quality**
   - PSR-12 coding standards
   - Dokumentasi code yang lengkap
   - Unit testing untuk fitur kritis

2. **Deployment**
   - Environment configuration (.env)
   - Database migration untuk version control
   - Automated testing pipeline

## 4.1.3 Constraints dan Assumptions

### A. Technical Constraints
1. **Technology Stack**
   - PHP 8.2 atau lebih tinggi
   - Laravel Framework 12.0
   - MySQL Database
   - Node.js untuk asset compilation

2. **Browser Support**
   - Modern browsers (Chrome, Firefox, Safari, Edge)
   - Mobile responsive design

### B. Business Constraints
1. **User Access**
   - Hanya satu tipe user: Project Manager
   - Single tenant application

2. **Data Constraints**
   - Satu proyek hanya boleh memiliki satu daily report per hari
   - Material harus terhubung dengan proyek
   - Budget harus terhubung dengan proyek

### C. Assumptions
1. **User Behavior**
   - User memiliki pengetahuan dasar tentang manajemen proyek
   - User akan menggunakan sistem secara konsisten untuk input data

2. **Environment**
   - Koneksi internet yang stabil
   - Server dengan spesifikasi yang memadai
   - Regular backup procedures
