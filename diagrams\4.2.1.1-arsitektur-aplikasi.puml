@startuml Arsitektur Aplikasi DisaCloud05-v4

!define RECTANGLE class

title Arsitektur Aplikasi DisaCloud05-v4\nSistem Manajemen Proyek Konstruksi

' Define colors
!define PRIMARY_COLOR #2563EB
!define SECONDARY_COLOR #10B981
!define ACCENT_COLOR #F59E0B
!define NEUTRAL_COLOR #6B7280

' Client Layer
package "Client Layer" as ClientLayer {
    RECTANGLE "Web Browser" as Browser {
        + HTML5
        + CSS3 (TailwindCSS)
        + JavaScript (Vanilla JS)
        + Axios (HTTP Client)
        + Chart.js (Visualisasi)
        + FullCalendar (Kalender)
    }
}

' Presentation Layer
package "Presentation Layer" as PresentationLayer {
    RECTANGLE "Laravel Blade Templates" as BladeTemplates {
        + dashboard.index
        + projects.*
        + workers.*
        + tasks.*
        + materials.*
        + budgets.*
        + reports.*
        + calendar.*
        + auth.*
    }
    
    RECTANGLE "HTTP Routes" as Routes {
        + web.php
        + API endpoints
        + Authentication routes
        + Resource routes
    }
}

' Application Layer
package "Application Layer" as ApplicationLayer {
    RECTANGLE "Controllers" as Controllers {
        + DashboardController
        + ProjectController
        + WorkerController
        + TaskController
        + MaterialController
        + BudgetController
        + ReportController
        + CalendarController
        + AuthController
        + DailyExpenseController
    }
    
    RECTANGLE "Middleware" as Middleware {
        + Authentication
        + CSRF Protection
        + Session Management
        + Input Validation
    }
    
    RECTANGLE "Form Requests" as FormRequests {
        + Validation Rules
        + Authorization Logic
        + Error Messages
    }
}

' Business Logic Layer
package "Business Logic Layer" as BusinessLayer {
    RECTANGLE "Eloquent Models" as Models {
        + User
        + Project
        + Worker
        + Task
        + Material
        + Budget
        + DailyReport
        + DailyExpense
        + CalendarEvent
        + Activity
    }
    
    RECTANGLE "Services" as Services {
        + ProjectService
        + BudgetService
        + ReportService
        + NotificationService
    }
    
    RECTANGLE "Relationships" as Relationships {
        + One-to-Many
        + Many-to-Many
        + Polymorphic
        + Eager Loading
    }
}

' Data Access Layer
package "Data Access Layer" as DataLayer {
    RECTANGLE "Eloquent ORM" as EloquentORM {
        + Query Builder
        + Model Relationships
        + Database Migrations
        + Database Seeders
    }
    
    RECTANGLE "Database Schema" as DatabaseSchema {
        + users
        + projects
        + workers
        + project_worker (pivot)
        + tasks
        + materials
        + budgets
        + daily_reports
        + daily_expenses
        + calendar_events
        + activities
    }
}

' Infrastructure Layer
package "Infrastructure Layer" as InfrastructureLayer {
    database "MySQL Database" as MySQL {
        + Primary Data Storage
        + ACID Compliance
        + Foreign Key Constraints
        + Indexing
    }
    
    database "Redis Cache" as Redis {
        + Session Storage
        + Query Caching
        + Performance Optimization
    }
    
    folder "File System" as FileSystem {
        + Invoice Files
        + Static Assets
        + Log Files
        + Temporary Files
    }
}

' External Services
package "External Services" as ExternalServices {
    cloud "Laravel Framework" as LaravelFramework {
        + Artisan CLI
        + Service Container
        + Event System
        + Queue System
    }
    
    cloud "Composer Packages" as ComposerPackages {
        + Laravel Framework
        + Third-party Libraries
        + Development Tools
    }
}

' Define relationships with colors
Browser -down-> BladeTemplates : HTTP Request/Response
BladeTemplates -down-> Routes : Route Resolution
Routes -down-> Controllers : Request Handling
Controllers -right-> Middleware : Request Processing
Controllers -down-> Models : Data Operations
Controllers -left-> FormRequests : Input Validation
Models -down-> EloquentORM : Database Queries
EloquentORM -down-> MySQL : SQL Queries
Models -right-> Services : Business Logic
Services -down-> Redis : Caching
Controllers -down-> FileSystem : File Operations
LaravelFramework -up-> ApplicationLayer : Framework Services
ComposerPackages -up-> LaravelFramework : Dependencies

' Add notes
note right of Browser
  **Frontend Technologies:**
  - Responsive Design (TailwindCSS)
  - Interactive Charts (Chart.js)
  - Calendar Integration (FullCalendar)
  - AJAX Requests (Axios)
end note

note right of Controllers
  **Controller Responsibilities:**
  - Request Validation
  - Business Logic Coordination
  - Response Generation
  - Error Handling
end note

note right of Models
  **Model Features:**
  - Eloquent Relationships
  - Attribute Casting
  - Accessors & Mutators
  - Query Scopes
end note

note right of MySQL
  **Database Features:**
  - Foreign Key Constraints
  - Unique Constraints
  - Indexing for Performance
  - Transaction Support
end note

@enduml
