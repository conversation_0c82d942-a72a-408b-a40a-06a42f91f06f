@startuml Entity Relationship Diagram - DisaCloud05-v4

!define TABLE entity
!define PK_COLOR #FFE4B5
!define FK_COLOR #E6F3FF
!define ATTR_COLOR #F0F8FF

title Entity Relationship Diagram\nDisaCloud05-v4 - Sistem Manajemen Proyek Konstruksi

' Define entities with their attributes
TABLE users {
    + id : BIGINT <<PK>>
    --
    name : VARCHA<PERSON>(255)
    email : VARCHAR(255) <<UNIQUE>>
    email_verified_at : TIMESTAMP
    password : VARCHAR(255)
    remember_token : VARCHAR(100)
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE projects {
    + id : BIGINT <<PK>>
    --
    name : VA<PERSON>HAR(255)
    description : TEXT
    status : ENUM('planned', 'in_progress', 'completed', 'on_hold')
    priority : ENUM('low', 'medium', 'high')
    start_date : DATETIME
    end_date : DATETIME
    progress : INTEGER
    budget_material : DECIMAL(15,2)
    budget_jasa : DECIMAL(15,2)
    created_at : <PERSON><PERSON><PERSON><PERSON><PERSON>
    updated_at : TIMESTAMP
}

TABLE workers {
    + id : BIGINT <<PK>>
    --
    name : <PERSON><PERSON>HA<PERSON>(255)
    specialization : VARCHAR(255)
    email : VARCHAR(255)
    phone : VARCHAR(255)
    skills : TEXT
    join_date : DATE
    end_date : DATE
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE project_worker {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    # worker_id : BIGINT <<FK>>
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE tasks {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    name : VARCHAR(255)
    description : TEXT
    due_date : DATE
    status : VARCHAR(255)
    priority : VARCHAR(255)
    completed : BOOLEAN
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE materials {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    name : VARCHAR(255)
    description : TEXT
    cost : DECIMAL(15,2)
    quantity : INTEGER
    unit : VARCHAR(50)
    purchase_date : DATE
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE budgets {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    amount : DECIMAL(15,2)
    estimated_profit : DECIMAL(15,2)
    profit_percentage : DECIMAL(5,2)
    description : TEXT
    budget_date : DATE
    invoice_file : VARCHAR(255)
    status : VARCHAR(255)
    type : VARCHAR(255)
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE daily_reports {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    report_date : DATE
    activities_done : TEXT
    challenges : TEXT
    next_plan : TEXT
    progress_percentage : INTEGER
    status : VARCHAR(255)
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
    --
    UNIQUE(project_id, report_date)
}

TABLE daily_expenses {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    # user_id : BIGINT <<FK>>
    expense_date : DATE
    category : VARCHAR(255)
    description : TEXT
    amount : DECIMAL(15,2)
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE calendar_events {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    title : VARCHAR(255)
    description : TEXT
    start_date : DATETIME
    end_date : DATETIME
    location : VARCHAR(255)
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

TABLE activities {
    + id : BIGINT <<PK>>
    --
    # project_id : BIGINT <<FK>>
    # assigned_to : BIGINT <<FK>>
    # created_by : BIGINT <<FK>>
    title : VARCHAR(255)
    description : TEXT
    status : ENUM('planned', 'in_progress', 'completed', 'on_hold')
    priority : ENUM('low', 'medium', 'high')
    start_date : DATETIME
    due_date : DATETIME
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

' Define relationships
projects ||--o{ tasks : "has many"
projects ||--o{ materials : "has many"
projects ||--o{ budgets : "has many"
projects ||--o{ daily_reports : "has many"
projects ||--o{ daily_expenses : "has many"
projects ||--o{ calendar_events : "has many"
projects ||--o{ activities : "has many"

projects }o--o{ workers : "employs"
project_worker }o--|| projects : "belongs to"
project_worker }o--|| workers : "belongs to"

users ||--o{ daily_expenses : "creates"
users ||--o{ activities : "assigned to"
users ||--o{ activities : "created by"

' Add notes for business rules
note right of daily_reports
  **Business Rule:**
  Satu proyek hanya boleh memiliki
  satu daily report per hari
  (UNIQUE constraint pada 
  project_id + report_date)
end note

note right of budgets
  **Calculated Fields:**
  - used_amount (dari total materials cost)
  - remaining_amount (amount - used_amount)
  - usage_percentage
end note

note right of materials
  **Calculated Field:**
  total_cost = cost × quantity
end note

note right of project_worker
  **Pivot Table:**
  Many-to-Many relationship
  antara Projects dan Workers
end note

note right of activities
  **Assignment:**
  - assigned_to: User yang ditugaskan
  - created_by: User yang membuat activity
end note

' Add legend
legend right
  **Legend:**
  + Primary Key (PK)
  # Foreign Key (FK)
  
  **Relationship Types:**
  ||--o{ : One to Many
  }o--o{ : Many to Many
  }o--|| : Many to One
  
  **Status Enums:**
  Projects: planned, in_progress, completed, on_hold
  Activities: planned, in_progress, completed, on_hold
  Priority: low, medium, high
end legend

@enduml
