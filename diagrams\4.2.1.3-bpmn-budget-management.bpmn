<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_BudgetManagement" targetNamespace="http://bpmn.io/schema/bpmn" exporter="bpmn-js" exporterVersion="8.7.2">
  <bpmn:process id="BudgetManagement" name="Budget Management Process" isExecutable="false">
    
    <!-- Start Event -->
    <bpmn:startEvent id="StartEvent_Budget" name="Budget Planning Required">
      <bpmn:outgoing>Flow_B1</bpmn:outgoing>
    </bpmn:startEvent>
    
    <!-- Task: Create Initial Budget -->
    <bpmn:userTask id="Task_CreateBudget" name="Create Project Budget">
      <bpmn:documentation>Project Manager creates budget with:
- Total budget amount
- Estimated profit and percentage
- Budget type (material/jasa)
- Budget description
- Target date</bpmn:documentation>
      <bpmn:incoming>Flow_B1</bpmn:incoming>
      <bpmn:outgoing>Flow_B2</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Task: Upload Invoice -->
    <bpmn:userTask id="Task_UploadInvoice" name="Upload Invoice File">
      <bpmn:documentation>Upload supporting invoice documents</bpmn:documentation>
      <bpmn:incoming>Flow_B2</bpmn:incoming>
      <bpmn:outgoing>Flow_B3</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Gateway: Budget Approved -->
    <bpmn:exclusiveGateway id="Gateway_BudgetApproval" name="Budget Approved?">
      <bpmn:incoming>Flow_B3</bpmn:incoming>
      <bpmn:outgoing>Flow_B4</bpmn:outgoing>
      <bpmn:outgoing>Flow_B5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- Task: Activate Budget -->
    <bpmn:serviceTask id="Task_ActivateBudget" name="Activate Budget">
      <bpmn:documentation>Set budget status to active</bpmn:documentation>
      <bpmn:incoming>Flow_B4</bpmn:incoming>
      <bpmn:outgoing>Flow_B6</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- Subprocess: Budget Tracking -->
    <bpmn:subProcess id="SubProcess_BudgetTracking" name="Budget Tracking">
      <bpmn:incoming>Flow_B6</bpmn:incoming>
      <bpmn:outgoing>Flow_B7</bpmn:outgoing>
      
      <!-- Material Purchase -->
      <bpmn:startEvent id="StartEvent_Purchase" name="Material Purchase">
        <bpmn:outgoing>Flow_T1</bpmn:outgoing>
      </bpmn:startEvent>
      
      <bpmn:userTask id="Task_RecordMaterial" name="Record Material Purchase">
        <bpmn:documentation>Record material with:
- Material name and description
- Cost and quantity
- Unit and purchase date</bpmn:documentation>
        <bpmn:incoming>Flow_T1</bpmn:incoming>
        <bpmn:outgoing>Flow_T2</bpmn:outgoing>
      </bpmn:userTask>
      
      <bpmn:serviceTask id="Task_UpdateBudgetUsage" name="Update Budget Usage">
        <bpmn:documentation>Automatically calculate:
- Used amount from materials
- Remaining amount
- Usage percentage</bpmn:documentation>
        <bpmn:incoming>Flow_T2</bpmn:incoming>
        <bpmn:outgoing>Flow_T3</bpmn:outgoing>
      </bpmn:serviceTask>
      
      <!-- Daily Expenses -->
      <bpmn:userTask id="Task_RecordDailyExpense" name="Record Daily Expenses">
        <bpmn:documentation>Record daily operational expenses:
- Expense category (makan, bensin, etc.)
- Amount and description
- Expense date</bpmn:documentation>
        <bpmn:incoming>Flow_T3</bpmn:incoming>
        <bpmn:outgoing>Flow_T4</bpmn:outgoing>
      </bpmn:userTask>
      
      <!-- Budget Check -->
      <bpmn:exclusiveGateway id="Gateway_BudgetCheck" name="Budget Exceeded?">
        <bpmn:incoming>Flow_T4</bpmn:incoming>
        <bpmn:outgoing>Flow_T5</bpmn:outgoing>
        <bpmn:outgoing>Flow_T6</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      
      <!-- Alert -->
      <bpmn:serviceTask id="Task_BudgetAlert" name="Send Budget Alert">
        <bpmn:documentation>Notify Project Manager about budget overrun</bpmn:documentation>
        <bpmn:incoming>Flow_T5</bpmn:incoming>
        <bpmn:outgoing>Flow_T7</bpmn:outgoing>
      </bpmn:serviceTask>
      
      <!-- Continue or End -->
      <bpmn:exclusiveGateway id="Gateway_Continue" name="Continue Tracking?">
        <bpmn:incoming>Flow_T6</bpmn:incoming>
        <bpmn:incoming>Flow_T7</bpmn:incoming>
        <bpmn:outgoing>Flow_T8</bpmn:outgoing>
        <bpmn:outgoing>Flow_T9</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      
      <bpmn:endEvent id="EndEvent_Continue" name="Continue Tracking">
        <bpmn:incoming>Flow_T8</bpmn:incoming>
      </bpmn:endEvent>
      
      <bpmn:endEvent id="EndEvent_Complete" name="Tracking Complete">
        <bpmn:incoming>Flow_T9</bpmn:incoming>
      </bpmn:endEvent>
      
      <!-- Flows within subprocess -->
      <bpmn:sequenceFlow id="Flow_T1" sourceRef="StartEvent_Purchase" targetRef="Task_RecordMaterial" />
      <bpmn:sequenceFlow id="Flow_T2" sourceRef="Task_RecordMaterial" targetRef="Task_UpdateBudgetUsage" />
      <bpmn:sequenceFlow id="Flow_T3" sourceRef="Task_UpdateBudgetUsage" targetRef="Task_RecordDailyExpense" />
      <bpmn:sequenceFlow id="Flow_T4" sourceRef="Task_RecordDailyExpense" targetRef="Gateway_BudgetCheck" />
      <bpmn:sequenceFlow id="Flow_T5" name="Yes" sourceRef="Gateway_BudgetCheck" targetRef="Task_BudgetAlert" />
      <bpmn:sequenceFlow id="Flow_T6" name="No" sourceRef="Gateway_BudgetCheck" targetRef="Gateway_Continue" />
      <bpmn:sequenceFlow id="Flow_T7" sourceRef="Task_BudgetAlert" targetRef="Gateway_Continue" />
      <bpmn:sequenceFlow id="Flow_T8" name="Yes" sourceRef="Gateway_Continue" targetRef="EndEvent_Continue" />
      <bpmn:sequenceFlow id="Flow_T9" name="No" sourceRef="Gateway_Continue" targetRef="EndEvent_Complete" />
    </bpmn:subProcess>
    
    <!-- Task: Calculate Final Profit -->
    <bpmn:serviceTask id="Task_CalculateProfit" name="Calculate Final Profit">
      <bpmn:documentation>Calculate actual profit:
- Total budget vs actual spending
- Profit percentage achieved
- Variance analysis</bpmn:documentation>
      <bpmn:incoming>Flow_B7</bpmn:incoming>
      <bpmn:outgoing>Flow_B8</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- Task: Generate Budget Report -->
    <bpmn:userTask id="Task_GenerateBudgetReport" name="Generate Budget Report">
      <bpmn:documentation>Create comprehensive budget report with:
- Budget vs actual comparison
- Material cost breakdown
- Daily expenses summary
- Profit analysis</bpmn:documentation>
      <bpmn:incoming>Flow_B8</bpmn:incoming>
      <bpmn:outgoing>Flow_B9</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- End Event -->
    <bpmn:endEvent id="EndEvent_Budget" name="Budget Management Complete">
      <bpmn:incoming>Flow_B9</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- Task: Revise Budget -->
    <bpmn:userTask id="Task_ReviseBudget" name="Revise Budget">
      <bpmn:documentation>Make necessary budget adjustments:
- Adjust budget amounts
- Modify profit expectations
- Update budget type or description</bpmn:documentation>
      <bpmn:incoming>Flow_B5</bpmn:incoming>
      <bpmn:outgoing>Flow_B10</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Sequence Flows -->
    <bpmn:sequenceFlow id="Flow_B1" sourceRef="StartEvent_Budget" targetRef="Task_CreateBudget" />
    <bpmn:sequenceFlow id="Flow_B2" sourceRef="Task_CreateBudget" targetRef="Task_UploadInvoice" />
    <bpmn:sequenceFlow id="Flow_B3" sourceRef="Task_UploadInvoice" targetRef="Gateway_BudgetApproval" />
    <bpmn:sequenceFlow id="Flow_B4" name="Yes" sourceRef="Gateway_BudgetApproval" targetRef="Task_ActivateBudget" />
    <bpmn:sequenceFlow id="Flow_B5" name="No" sourceRef="Gateway_BudgetApproval" targetRef="Task_ReviseBudget" />
    <bpmn:sequenceFlow id="Flow_B6" sourceRef="Task_ActivateBudget" targetRef="SubProcess_BudgetTracking" />
    <bpmn:sequenceFlow id="Flow_B7" sourceRef="SubProcess_BudgetTracking" targetRef="Task_CalculateProfit" />
    <bpmn:sequenceFlow id="Flow_B8" sourceRef="Task_CalculateProfit" targetRef="Task_GenerateBudgetReport" />
    <bpmn:sequenceFlow id="Flow_B9" sourceRef="Task_GenerateBudgetReport" targetRef="EndEvent_Budget" />
    <bpmn:sequenceFlow id="Flow_B10" sourceRef="Task_ReviseBudget" targetRef="Task_CreateBudget" />
    
  </bpmn:process>
</bpmn:definitions>
