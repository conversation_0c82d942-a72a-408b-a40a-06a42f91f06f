<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_ProjectLifecycle" targetNamespace="http://bpmn.io/schema/bpmn" exporter="bpmn-js" exporterVersion="8.7.2">
  <bpmn:process id="ProjectLifecycle" name="Project Lifecycle Management" isExecutable="false">
    
    <!-- Start Event -->
    <bpmn:startEvent id="StartEvent_1" name="Project Request">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    
    <!-- Task: Create Project -->
    <bpmn:userTask id="Task_CreateProject" name="Create New Project">
      <bpmn:documentation>Project Manager creates new project with:
- Project name and description
- Start and end dates
- Priority level
- Initial budget (material and jasa)
- Status: planned</bpmn:documentation>
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Task: Setup Project Budget -->
    <bpmn:userTask id="Task_SetupBudget" name="Setup Project Budget">
      <bpmn:documentation>Create detailed budget with:
- Total amount
- Estimated profit and percentage
- Budget type
- Invoice file upload</bpmn:documentation>
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Task: Assign Workers -->
    <bpmn:userTask id="Task_AssignWorkers" name="Assign Workers to Project">
      <bpmn:documentation>Assign workers based on:
- Required specialization
- Worker availability
- Project requirements</bpmn:documentation>
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Task: Create Tasks -->
    <bpmn:userTask id="Task_CreateTasks" name="Create Project Tasks">
      <bpmn:documentation>Break down project into tasks:
- Task name and description
- Due dates
- Priority levels
- Status tracking</bpmn:documentation>
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Gateway: Start Project -->
    <bpmn:exclusiveGateway id="Gateway_StartProject" name="Ready to Start?">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- Task: Update Project Status -->
    <bpmn:serviceTask id="Task_UpdateStatus" name="Update Project Status to In Progress">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- Subprocess: Project Execution -->
    <bpmn:subProcess id="SubProcess_Execution" name="Project Execution">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
      
      <!-- Daily Activities -->
      <bpmn:startEvent id="StartEvent_Daily" name="Daily Work">
        <bpmn:outgoing>Flow_Daily_1</bpmn:outgoing>
      </bpmn:startEvent>
      
      <bpmn:userTask id="Task_DailyWork" name="Execute Daily Tasks">
        <bpmn:incoming>Flow_Daily_1</bpmn:incoming>
        <bpmn:outgoing>Flow_Daily_2</bpmn:outgoing>
      </bpmn:userTask>
      
      <bpmn:userTask id="Task_RecordExpenses" name="Record Daily Expenses">
        <bpmn:incoming>Flow_Daily_2</bpmn:incoming>
        <bpmn:outgoing>Flow_Daily_3</bpmn:outgoing>
      </bpmn:userTask>
      
      <bpmn:userTask id="Task_UpdateMaterials" name="Update Material Usage">
        <bpmn:incoming>Flow_Daily_3</bpmn:incoming>
        <bpmn:outgoing>Flow_Daily_4</bpmn:outgoing>
      </bpmn:userTask>
      
      <bpmn:userTask id="Task_DailyReport" name="Create Daily Report">
        <bpmn:incoming>Flow_Daily_4</bpmn:incoming>
        <bpmn:outgoing>Flow_Daily_5</bpmn:outgoing>
      </bpmn:userTask>
      
      <bpmn:exclusiveGateway id="Gateway_ProjectComplete" name="Project Complete?">
        <bpmn:incoming>Flow_Daily_5</bpmn:incoming>
        <bpmn:outgoing>Flow_Daily_6</bpmn:outgoing>
        <bpmn:outgoing>Flow_Daily_7</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      
      <bpmn:endEvent id="EndEvent_Daily" name="Continue Next Day">
        <bpmn:incoming>Flow_Daily_6</bpmn:incoming>
      </bpmn:endEvent>
      
      <bpmn:endEvent id="EndEvent_Complete" name="Project Execution Complete">
        <bpmn:incoming>Flow_Daily_7</bpmn:incoming>
      </bpmn:endEvent>
      
      <!-- Flows within subprocess -->
      <bpmn:sequenceFlow id="Flow_Daily_1" sourceRef="StartEvent_Daily" targetRef="Task_DailyWork" />
      <bpmn:sequenceFlow id="Flow_Daily_2" sourceRef="Task_DailyWork" targetRef="Task_RecordExpenses" />
      <bpmn:sequenceFlow id="Flow_Daily_3" sourceRef="Task_RecordExpenses" targetRef="Task_UpdateMaterials" />
      <bpmn:sequenceFlow id="Flow_Daily_4" sourceRef="Task_UpdateMaterials" targetRef="Task_DailyReport" />
      <bpmn:sequenceFlow id="Flow_Daily_5" sourceRef="Task_DailyReport" targetRef="Gateway_ProjectComplete" />
      <bpmn:sequenceFlow id="Flow_Daily_6" name="No" sourceRef="Gateway_ProjectComplete" targetRef="EndEvent_Daily" />
      <bpmn:sequenceFlow id="Flow_Daily_7" name="Yes" sourceRef="Gateway_ProjectComplete" targetRef="EndEvent_Complete" />
    </bpmn:subProcess>
    
    <!-- Task: Project Completion -->
    <bpmn:userTask id="Task_ProjectCompletion" name="Complete Project">
      <bpmn:documentation>Final project activities:
- Update project status to completed
- Generate final reports
- Calculate final profit
- Archive project data</bpmn:documentation>
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:outgoing>Flow_10</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Task: Generate Reports -->
    <bpmn:serviceTask id="Task_GenerateReports" name="Generate Final Reports">
      <bpmn:incoming>Flow_10</bpmn:incoming>
      <bpmn:outgoing>Flow_11</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- End Event -->
    <bpmn:endEvent id="EndEvent_1" name="Project Completed">
      <bpmn:incoming>Flow_11</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- Task: Revise Project -->
    <bpmn:userTask id="Task_ReviseProject" name="Revise Project Setup">
      <bpmn:documentation>Make necessary changes to:
- Project details
- Budget allocation
- Worker assignments
- Task breakdown</bpmn:documentation>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_12</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- Sequence Flows -->
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_CreateProject" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_CreateProject" targetRef="Task_SetupBudget" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_SetupBudget" targetRef="Task_AssignWorkers" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Task_AssignWorkers" targetRef="Task_CreateTasks" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_CreateTasks" targetRef="Gateway_StartProject" />
    <bpmn:sequenceFlow id="Flow_6" name="Yes" sourceRef="Gateway_StartProject" targetRef="Task_UpdateStatus" />
    <bpmn:sequenceFlow id="Flow_7" name="No" sourceRef="Gateway_StartProject" targetRef="Task_ReviseProject" />
    <bpmn:sequenceFlow id="Flow_8" sourceRef="Task_UpdateStatus" targetRef="SubProcess_Execution" />
    <bpmn:sequenceFlow id="Flow_9" sourceRef="SubProcess_Execution" targetRef="Task_ProjectCompletion" />
    <bpmn:sequenceFlow id="Flow_10" sourceRef="Task_ProjectCompletion" targetRef="Task_GenerateReports" />
    <bpmn:sequenceFlow id="Flow_11" sourceRef="Task_GenerateReports" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_12" sourceRef="Task_ReviseProject" targetRef="Task_CreateProject" />
    
  </bpmn:process>
</bpmn:definitions>
