@startuml Activity Diagram - Project Management Flow

!define PRIMARY_COLOR #2563EB
!define SUCCESS_COLOR #10B981
!define WARNING_COLOR #F59E0B
!define ERROR_COLOR #EF4444

title Activity Diagram\nProject Management Flow - DisaCloud05-v4

|Project Manager|
start

:Login to System;
note right: Authentication required

:Access Dashboard;
note right: View project overview and analytics

if (Create New Project?) then (yes)
    :Navigate to Create Project;
    :Fill Project Details;
    note right
        - Project name and description
        - Start and end dates
        - Priority level
        - Initial budget estimates
    end note
    
    :Submit Project Form;
    
    if (Validation Successful?) then (yes)
        :Save Project to Database;
        :Set Project Status to "Planned";
        
        ' Budget Setup
        :Create Project Budget;
        note right
            - Set total budget amount
            - Define estimated profit
            - Upload invoice files
            - Set budget type
        end note
        
        :Save Budget Information;
        
        ' Worker Assignment
        :Assign Workers to Project;
        note right
            - Select workers based on specialization
            - Consider worker availability
            - Create many-to-many relationships
        end note
        
        ' Task Creation
        :Create Project Tasks;
        note right
            - Break down project into tasks
            - Set task priorities and due dates
            - Define task descriptions
        end note
        
        ' Calendar Events
        :Create Calendar Events;
        note right
            - Schedule important milestones
            - Set project timeline
            - Add event descriptions and locations
        end note
        
        :Update Project Status to "In Progress";
        
        ' Daily Operations Loop
        repeat
            :Execute Daily Work;
            
            ' Material Management
            if (Material Purchase?) then (yes)
                :Record Material Purchase;
                note right
                    - Material name and description
                    - Cost, quantity, and unit
                    - Purchase date
                end note
                
                :Update Material Inventory;
                :Calculate Total Material Cost;
                :Update Budget Usage;
                
                if (Budget Exceeded?) then (yes)
                    :Send Budget Alert;
                    note right: Notify about budget overrun
                else (no)
                    :Continue Operations;
                endif
            endif
            
            ' Daily Expenses
            :Record Daily Expenses;
            note right
                - Expense category (makan, bensin, etc.)
                - Amount and description
                - Expense date
            end note
            
            ' Task Updates
            :Update Task Status;
            if (Task Completed?) then (yes)
                :Mark Task as Completed;
            endif
            
            ' Daily Reporting
            :Create Daily Report;
            note right
                - Activities completed
                - Challenges faced
                - Next day plans
                - Progress percentage
            end note
            
            :Save Daily Report;
            note right: One report per project per day
            
            ' Progress Check
            if (Project Completed?) then (yes)
                break
            endif
            
        repeat while (Continue Daily Operations?)
        
        ' Project Completion
        :Update Project Status to "Completed";
        :Calculate Final Profit;
        note right
            - Compare budget vs actual spending
            - Calculate profit percentage
            - Generate variance analysis
        end note
        
        :Generate Final Reports;
        note right
            - Project summary report
            - Budget analysis report
            - Material usage report
            - Daily expenses summary
        end note
        
        :Archive Project Data;
        
    else (no)
        :Display Validation Errors;
        :Return to Project Form;
        stop
    endif
    
else (no)
    ' View Existing Projects
    :View Project List;
    
    if (Select Project?) then (yes)
        :View Project Details;
        
        if (Update Project?) then (yes)
            :Edit Project Information;
            :Save Changes;
        endif
        
        if (View Reports?) then (yes)
            :Display Project Reports;
            :Generate Analytics;
        endif
        
        if (Manage Budget?) then (yes)
            :View Budget Details;
            :Track Budget Usage;
            :Monitor Expenses;
        endif
        
        if (Manage Workers?) then (yes)
            :View Assigned Workers;
            :Add/Remove Workers;
        endif
        
        if (Manage Tasks?) then (yes)
            :View Task List;
            :Update Task Status;
            :Create New Tasks;
        endif
        
        if (View Calendar?) then (yes)
            :Display Project Calendar;
            :View Scheduled Events;
            :Create New Events;
        endif
    endif
endif

:Return to Dashboard;

if (Logout?) then (yes)
    :Logout from System;
    stop
else (no)
    :Continue Using System;
    stop
endif

' Error Handling
note right of start
    **Error Handling:**
    - All forms include validation
    - Database errors are logged
    - User receives appropriate feedback
    - System maintains data integrity
end note

' Business Rules
note left of start
    **Business Rules:**
    - Only Project Manager can access system
    - One daily report per project per day
    - Budget alerts when exceeded
    - Material costs auto-update budget usage
    - Tasks can have multiple priorities
    - Workers can be assigned to multiple projects
end note

@enduml
