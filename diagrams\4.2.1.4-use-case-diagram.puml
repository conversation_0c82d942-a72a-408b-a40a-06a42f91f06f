@startuml Use Case Diagram - DisaCloud05-v4

!define ACTOR_COLOR #4F46E5
!define USECASE_COLOR #10B981
!define SYSTEM_COLOR #F59E0B

title Use Case Diagram\nDisaCloud05-v4 - Sistem Manajemen Proyek Ko<PERSON>ruksi

' Define the system boundary
rectangle "DisaCloud05-v4 System" as System {
    
    ' Project Management Use Cases
    package "Project Management" as ProjectMgmt {
        usecase "Create New Project" as UC1
        usecase "Update Project Details" as UC2
        usecase "View Project List" as UC3
        usecase "Delete Project" as UC4
        usecase "Set Project Status" as UC5
        usecase "Set Project Priority" as UC6
        usecase "Track Project Progress" as UC7
        usecase "View Project Dashboard" as UC8
    }
    
    ' Worker Management Use Cases
    package "Worker Management" as WorkerMgmt {
        usecase "Add New Worker" as UC9
        usecase "Update Worker Info" as UC10
        usecase "View Worker List" as UC11
        usecase "Assign Worker to Project" as UC12
        usecase "Remove Worker from Project" as UC13
        usecase "View Worker Specialization" as UC14
    }
    
    ' Task Management Use Cases
    package "Task Management" as TaskMgmt {
        usecase "Create Project Task" as UC15
        usecase "Update Task Status" as UC16
        usecase "Set Task Priority" as UC17
        usecase "Mark Task as Completed" as UC18
        usecase "View Task List" as UC19
        usecase "Delete Task" as UC20
    }
    
    ' Budget Management Use Cases
    package "Budget Management" as BudgetMgmt {
        usecase "Create Project Budget" as UC21
        usecase "Update Budget Details" as UC22
        usecase "Upload Invoice File" as UC23
        usecase "Track Budget Usage" as UC24
        usecase "View Budget Report" as UC25
        usecase "Calculate Profit" as UC26
        usecase "Monitor Budget Alerts" as UC27
    }
    
    ' Material Management Use Cases
    package "Material Management" as MaterialMgmt {
        usecase "Add Material to Project" as UC28
        usecase "Update Material Info" as UC29
        usecase "Record Material Purchase" as UC30
        usecase "View Material List" as UC31
        usecase "Calculate Material Cost" as UC32
        usecase "Delete Material" as UC33
    }
    
    ' Expense Management Use Cases
    package "Expense Management" as ExpenseMgmt {
        usecase "Record Daily Expense" as UC34
        usecase "Categorize Expenses" as UC35
        usecase "View Expense History" as UC36
        usecase "Update Expense Details" as UC37
        usecase "Delete Expense Record" as UC38
    }
    
    ' Reporting Use Cases
    package "Reporting" as Reporting {
        usecase "Create Daily Report" as UC39
        usecase "View Daily Reports" as UC40
        usecase "Update Report Status" as UC41
        usecase "Generate Project Report" as UC42
        usecase "Export Reports" as UC43
        usecase "View Analytics Dashboard" as UC44
    }
    
    ' Calendar Management Use Cases
    package "Calendar Management" as CalendarMgmt {
        usecase "Create Calendar Event" as UC45
        usecase "Update Event Details" as UC46
        usecase "View Project Calendar" as UC47
        usecase "Delete Calendar Event" as UC48
        usecase "Set Event Reminders" as UC49
    }
    
    ' Activity Management Use Cases
    package "Activity Management" as ActivityMgmt {
        usecase "Create Project Activity" as UC50
        usecase "Assign Activity to User" as UC51
        usecase "Update Activity Status" as UC52
        usecase "Set Activity Priority" as UC53
        usecase "Track Activity Progress" as UC54
        usecase "View Activity List" as UC55
    }
    
    ' Authentication Use Cases
    package "Authentication" as Auth {
        usecase "Login to System" as UC56
        usecase "Logout from System" as UC57
        usecase "Manage User Profile" as UC58
    }
}

' Define the actor
actor "Project Manager" as PM <<ACTOR_COLOR>>

' Connect actor to use cases
PM --> UC1
PM --> UC2
PM --> UC3
PM --> UC4
PM --> UC5
PM --> UC6
PM --> UC7
PM --> UC8

PM --> UC9
PM --> UC10
PM --> UC11
PM --> UC12
PM --> UC13
PM --> UC14

PM --> UC15
PM --> UC16
PM --> UC17
PM --> UC18
PM --> UC19
PM --> UC20

PM --> UC21
PM --> UC22
PM --> UC23
PM --> UC24
PM --> UC25
PM --> UC26
PM --> UC27

PM --> UC28
PM --> UC29
PM --> UC30
PM --> UC31
PM --> UC32
PM --> UC33

PM --> UC34
PM --> UC35
PM --> UC36
PM --> UC37
PM --> UC38

PM --> UC39
PM --> UC40
PM --> UC41
PM --> UC42
PM --> UC43
PM --> UC44

PM --> UC45
PM --> UC46
PM --> UC47
PM --> UC48
PM --> UC49

PM --> UC50
PM --> UC51
PM --> UC52
PM --> UC53
PM --> UC54
PM --> UC55

PM --> UC56
PM --> UC57
PM --> UC58

' Define relationships between use cases
UC1 ..> UC21 : <<include>>
UC1 ..> UC12 : <<include>>
UC7 ..> UC24 : <<include>>
UC7 ..> UC32 : <<include>>
UC25 ..> UC26 : <<include>>
UC42 ..> UC43 : <<extend>>
UC27 ..> UC22 : <<extend>>

' Add notes
note right of PM
  **Primary User:**
  Project Manager adalah satu-satunya
  tipe user yang dapat mengakses
  semua fitur dalam sistem
end note

note bottom of System
  **System Scope:**
  Sistem manajemen proyek konstruksi
  yang terintegrasi dengan fitur lengkap
  untuk mengelola semua aspek proyek
end note

@enduml
