@startuml Wireframe Dashboard - DisaCloud05-v4

!define WIREFRAME_COLOR #E5E7EB
!define PRIMARY_COLOR #4F46E5
!define SECONDARY_COLOR #10B981
!define ACCENT_COLOR #F59E0B
!define TEXT_COLOR #374151

title Wireframe Dashboard\nDisaCloud05-v4 - Sistem Manajemen Proyek Konstruksi

' Main container
rectangle "Browser Window" as Browser {
    
    ' Header/Navigation
    rectangle "Header Navigation" as Header <<WIREFRAME_COLOR>> {
        rectangle "DisaCloud Logo" as Logo <<PRIMARY_COLOR>>
        rectangle "User Profile" as UserProfile <<TEXT_COLOR>>
        rectangle "Logout" as Logout <<TEXT_COLOR>>
    }
    
    ' Main layout with sidebar and content
    rectangle "Main Layout" as MainLayout {
        
        ' Sidebar Navigation
        rectangle "Sidebar Navigation (w-64)" as Sidebar <<PRIMARY_COLOR>> {
            rectangle "DisaCloud" as SidebarLogo <<#FFFFFF>>
            rectangle "Dashboard" as NavDashboard <<#FFFFFF>>
            rectangle "Semua Proyek" as NavProjects <<#FFFFFF>>
            rectangle "Workers" as NavWorkers <<#FFFFFF>>
            rectangle "Budgets ▼" as NavBudgets <<#FFFFFF>> {
                rectangle "- Semua Budget" as SubBudgets1 <<#FFFFFF>>
                rectangle "- Tambah Budget" as SubBudgets2 <<#FFFFFF>>
                rectangle "- Bandingkan Budget" as SubBudgets3 <<#FFFFFF>>
            }
            rectangle "Calendar" as NavCalendar <<#FFFFFF>>
            rectangle "Laporan" as NavReports <<#FFFFFF>>
        }
        
        ' Main Content Area
        rectangle "Main Content Area (flex-1)" as MainContent <<WIREFRAME_COLOR>> {
            
            ' Page Title
            rectangle "Page Title: Dashboard" as PageTitle <<TEXT_COLOR>>
            
            ' Statistics Cards Row
            rectangle "Statistics Cards (grid-cols-4)" as StatsCards {
                rectangle "Card 1" as Card1 <<SECONDARY_COLOR>> {
                    rectangle "📋 Icon" as Icon1
                    rectangle "Total Proyek Aktif" as Title1
                    rectangle "{{ count }}" as Value1
                }
                rectangle "Card 2" as Card2 <<ACCENT_COLOR>> {
                    rectangle "⏰ Icon" as Icon2
                    rectangle "Mendekati Deadline" as Title2
                    rectangle "{{ count }}" as Value2
                }
                rectangle "Card 3" as Card3 <<SECONDARY_COLOR>> {
                    rectangle "✅ Icon" as Icon3
                    rectangle "Proyek Selesai" as Title3
                    rectangle "{{ count }}" as Value3
                }
                rectangle "Card 4" as Card4 <<PRIMARY_COLOR>> {
                    rectangle "💰 Icon" as Icon4
                    rectangle "Total Budget" as Title4
                    rectangle "Rp {{ amount }}" as Value4
                }
            }
            
            ' Charts and Data Row
            rectangle "Charts Section (grid-cols-2)" as ChartsSection {
                
                ' Project Status Chart
                rectangle "Project Status Chart" as StatusChart <<#FFFFFF>> {
                    rectangle "Chart Title: Distribusi Status Proyek" as ChartTitle1
                    rectangle "🍩 Doughnut Chart" as DoughnutChart {
                        rectangle "Direncanakan" as Status1 <<#60A5FA>>
                        rectangle "Sedang Berjalan" as Status2 <<#34D399>>
                        rectangle "Selesai" as Status3 <<#F472B6>>
                        rectangle "Ditunda" as Status4 <<#FBBF24>>
                    }
                    rectangle "Chart.js Integration" as ChartJS
                }
                
                ' Recent Projects List
                rectangle "Recent Projects" as RecentProjects <<#FFFFFF>> {
                    rectangle "Title: Proyek Terbaru" as RecentTitle
                    rectangle "Project List" as ProjectList {
                        rectangle "Project 1" as Proj1 {
                            rectangle "Project Name" as ProjName1
                            rectangle "Status Badge" as ProjStatus1
                            rectangle "Progress Bar" as ProjProgress1
                        }
                        rectangle "Project 2" as Proj2 {
                            rectangle "Project Name" as ProjName2
                            rectangle "Status Badge" as ProjStatus2
                            rectangle "Progress Bar" as ProjProgress2
                        }
                        rectangle "Project 3" as Proj3 {
                            rectangle "Project Name" as ProjName3
                            rectangle "Status Badge" as ProjStatus3
                            rectangle "Progress Bar" as ProjProgress3
                        }
                    }
                    rectangle "View All Button" as ViewAllBtn <<PRIMARY_COLOR>>
                }
            }
            
            ' Notifications and Alerts
            rectangle "Notifications Section" as NotificationsSection {
                
                ' Urgent Deadlines
                rectangle "Urgent Deadlines Alert" as UrgentAlert <<#FEF2F2>> {
                    rectangle "⚠️ Warning Icon" as WarningIcon
                    rectangle "Proyek Mendekati Deadline (3 hari)" as UrgentTitle
                    rectangle "Project List with Deadlines" as UrgentList
                }
                
                ' Stalled Projects
                rectangle "Stalled Projects Alert" as StalledAlert <<#FFFBEB>> {
                    rectangle "⏸️ Pause Icon" as PauseIcon
                    rectangle "Proyek Tidak Ada Progress (7 hari)" as StalledTitle
                    rectangle "Stalled Project List" as StalledList
                }
                
                ' Overdue Projects
                rectangle "Overdue Projects Alert" as OverdueAlert <<#FEF2F2>> {
                    rectangle "🚨 Alert Icon" as AlertIcon
                    rectangle "Proyek Melewati Deadline" as OverdueTitle
                    rectangle "Overdue Project List" as OverdueList
                }
            }
            
            ' Quick Actions
            rectangle "Quick Actions" as QuickActions <<#FFFFFF>> {
                rectangle "Quick Action Buttons" as ActionButtons {
                    rectangle "+ Proyek Baru" as NewProject <<PRIMARY_COLOR>>
                    rectangle "+ Tambah Worker" as NewWorker <<SECONDARY_COLOR>>
                    rectangle "+ Buat Budget" as NewBudget <<ACCENT_COLOR>>
                    rectangle "📊 Lihat Laporan" as ViewReports <<TEXT_COLOR>>
                }
            }
        }
    }
}

' Layout relationships
Header -down-> MainLayout
MainLayout -right-> Sidebar
MainLayout -down-> MainContent
MainContent -down-> StatsCards
StatsCards -down-> ChartsSection
ChartsSection -down-> NotificationsSection
NotificationsSection -down-> QuickActions

' Add notes for responsive design
note right of Browser
    **Responsive Design:**
    - Mobile: Stack cards vertically
    - Tablet: 2-column grid
    - Desktop: 4-column grid
    - Sidebar collapses on mobile
end note

note right of Sidebar
    **Navigation Features:**
    - Active state highlighting
    - Collapsible sub-menus
    - Hover effects
    - Mobile hamburger menu
end note

note right of StatsCards
    **Statistics Cards:**
    - Real-time data updates
    - Color-coded by category
    - Click to drill down
    - Responsive grid layout
end note

note right of DoughnutChart
    **Chart.js Integration:**
    - Interactive doughnut chart
    - Color-coded segments
    - Hover tooltips
    - Responsive sizing
end note

note right of NotificationsSection
    **Alert System:**
    - Color-coded by urgency
    - Dismissible alerts
    - Click to view details
    - Auto-refresh data
end note

' Add technology stack info
note bottom of Browser
    **Technology Stack:**
    - Laravel Blade Templates
    - TailwindCSS for styling
    - Chart.js for visualizations
    - Alpine.js for interactions
    - Responsive grid system
    - Component-based architecture
end note

@enduml

' Additional wireframes for other key pages can be created separately:
' - *******-wireframe-projects.puml (Project Management)
' - *******-wireframe-budget.puml (Budget Management)
' - *******-wireframe-calendar.puml (Calendar View)
' - *******-wireframe-reports.puml (Reports)
