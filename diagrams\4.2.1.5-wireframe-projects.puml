@startuml Wireframe Project Management - DisaCloud05-v4

!define WIREFRAME_COLOR #E5E7EB
!define PRIMARY_COLOR #4F46E5
!define SECONDARY_COLOR #10B981
!define ACCENT_COLOR #F59E0B
!define TEXT_COLOR #374151

title Wireframe Project Management\nDisaCloud05-v4 - <PERSON><PERSON> Semua Proyek

' Main container
rectangle "Browser Window" as Browser {
    
    ' Header with breadcrumb
    rectangle "Header Section" as Header <<WIREFRAME_COLOR>> {
        rectangle "Breadcrumb: Dashboard > Semua Proyek" as Breadcrumb <<TEXT_COLOR>>
        rectangle "Page Title: Semua Proyek" as PageTitle <<TEXT_COLOR>>
    }
    
    ' Action Bar
    rectangle "Action Bar" as ActionBar <<#FFFFFF>> {
        rectangle "Filter Section" as FilterSection {
            rectangle "Status Filter Dropdown" as StatusFilter <<WIREFRAME_COLOR>>
            rectangle "Priority Filter" as PriorityFilter <<WIREFRAME_COLOR>>
            rectangle "Date Range Picker" as DateFilter <<WIREFRAME_COLOR>>
        }
        rectangle "Search Box" as SearchBox <<WIREFRAME_COLOR>>
        rectangle "+ Proyek Baru" as NewProjectBtn <<PRIMARY_COLOR>>
    }
    
    ' Project Grid/List View
    rectangle "Project Display Area" as ProjectArea {
        
        ' View Toggle
        rectangle "View Toggle" as ViewToggle <<WIREFRAME_COLOR>> {
            rectangle "Grid View 🔲" as GridView
            rectangle "List View ☰" as ListView
        }
        
        ' Project Cards Grid (3 columns)
        rectangle "Project Cards Grid (grid-cols-3)" as ProjectGrid {
            
            ' Project Card 1
            rectangle "Project Card 1" as Card1 <<#FFFFFF>> {
                rectangle "Project Header" as Header1 {
                    rectangle "Project Name" as Name1 <<TEXT_COLOR>>
                    rectangle "Status Badge" as Status1 <<SECONDARY_COLOR>>
                }
                rectangle "Project Details" as Details1 {
                    rectangle "Description (truncated)" as Desc1
                    rectangle "Start Date - End Date" as Dates1
                    rectangle "Priority: High/Medium/Low" as Priority1
                }
                rectangle "Progress Section" as Progress1 {
                    rectangle "Progress Bar ({{ progress }}%)" as ProgressBar1 <<SECONDARY_COLOR>>
                    rectangle "Progress Text" as ProgressText1
                }
                rectangle "Budget Info" as Budget1 {
                    rectangle "Budget Material: Rp {{ amount }}" as BudgetMat1
                    rectangle "Budget Jasa: Rp {{ amount }}" as BudgetJasa1
                }
                rectangle "Assigned Workers" as Workers1 {
                    rectangle "👥 {{ count }} Workers" as WorkerCount1
                    rectangle "Worker Avatars" as WorkerAvatars1
                }
                rectangle "Action Buttons" as Actions1 {
                    rectangle "View" as ViewBtn1 <<PRIMARY_COLOR>>
                    rectangle "Edit" as EditBtn1 <<ACCENT_COLOR>>
                    rectangle "Delete" as DeleteBtn1 <<#EF4444>>
                }
            }
            
            ' Project Card 2
            rectangle "Project Card 2" as Card2 <<#FFFFFF>> {
                rectangle "Project Header" as Header2 {
                    rectangle "Project Name" as Name2 <<TEXT_COLOR>>
                    rectangle "Status Badge" as Status2 <<PRIMARY_COLOR>>
                }
                rectangle "Project Details" as Details2 {
                    rectangle "Description (truncated)" as Desc2
                    rectangle "Start Date - End Date" as Dates2
                    rectangle "Priority: High/Medium/Low" as Priority2
                }
                rectangle "Progress Section" as Progress2 {
                    rectangle "Progress Bar ({{ progress }}%)" as ProgressBar2 <<PRIMARY_COLOR>>
                    rectangle "Progress Text" as ProgressText2
                }
                rectangle "Budget Info" as Budget2 {
                    rectangle "Budget Material: Rp {{ amount }}" as BudgetMat2
                    rectangle "Budget Jasa: Rp {{ amount }}" as BudgetJasa2
                }
                rectangle "Assigned Workers" as Workers2 {
                    rectangle "👥 {{ count }} Workers" as WorkerCount2
                    rectangle "Worker Avatars" as WorkerAvatars2
                }
                rectangle "Action Buttons" as Actions2 {
                    rectangle "View" as ViewBtn2 <<PRIMARY_COLOR>>
                    rectangle "Edit" as EditBtn2 <<ACCENT_COLOR>>
                    rectangle "Delete" as DeleteBtn2 <<#EF4444>>
                }
            }
            
            ' Project Card 3
            rectangle "Project Card 3" as Card3 <<#FFFFFF>> {
                rectangle "Project Header" as Header3 {
                    rectangle "Project Name" as Name3 <<TEXT_COLOR>>
                    rectangle "Status Badge" as Status3 <<ACCENT_COLOR>>
                }
                rectangle "Project Details" as Details3 {
                    rectangle "Description (truncated)" as Desc3
                    rectangle "Start Date - End Date" as Dates3
                    rectangle "Priority: High/Medium/Low" as Priority3
                }
                rectangle "Progress Section" as Progress3 {
                    rectangle "Progress Bar ({{ progress }}%)" as ProgressBar3 <<ACCENT_COLOR>>
                    rectangle "Progress Text" as ProgressText3
                }
                rectangle "Budget Info" as Budget3 {
                    rectangle "Budget Material: Rp {{ amount }}" as BudgetMat3
                    rectangle "Budget Jasa: Rp {{ amount }}" as BudgetJasa3
                }
                rectangle "Assigned Workers" as Workers3 {
                    rectangle "👥 {{ count }} Workers" as WorkerCount3
                    rectangle "Worker Avatars" as WorkerAvatars3
                }
                rectangle "Action Buttons" as Actions3 {
                    rectangle "View" as ViewBtn3 <<PRIMARY_COLOR>>
                    rectangle "Edit" as EditBtn3 <<ACCENT_COLOR>>
                    rectangle "Delete" as DeleteBtn3 <<#EF4444>>
                }
            }
        }
        
        ' Pagination
        rectangle "Pagination" as Pagination <<WIREFRAME_COLOR>> {
            rectangle "Previous" as PrevBtn
            rectangle "1 2 3 ... 10" as PageNumbers
            rectangle "Next" as NextBtn
        }
    }
    
    ' Empty State (when no projects)
    rectangle "Empty State" as EmptyState <<WIREFRAME_COLOR>> {
        rectangle "📋 Empty Icon" as EmptyIcon
        rectangle "Belum ada proyek" as EmptyTitle
        rectangle "Mulai dengan membuat proyek pertama Anda" as EmptyDesc
        rectangle "+ Buat Proyek Pertama" as FirstProjectBtn <<PRIMARY_COLOR>>
    }
}

' Layout relationships
Header -down-> ActionBar
ActionBar -down-> ProjectArea
ProjectArea -down-> ViewToggle
ViewToggle -down-> ProjectGrid
ProjectGrid -down-> Pagination

' Add notes for functionality
note right of Browser
    **Page Features:**
    - Responsive grid layout
    - Real-time search filtering
    - Status-based filtering
    - Sortable columns
    - Bulk actions
end note

note right of ActionBar
    **Filter & Search:**
    - Status: All, Planned, In Progress, Completed, On Hold
    - Priority: All, High, Medium, Low
    - Date range picker for start/end dates
    - Real-time search by project name
end note

note right of Card1
    **Project Card Features:**
    - Hover effects
    - Status color coding
    - Progress visualization
    - Worker assignment display
    - Quick action buttons
    - Click to view details
end note

note right of ViewToggle
    **View Options:**
    - Grid view (default): 3 columns on desktop
    - List view: Table format with more details
    - Mobile: Single column stack
    - Tablet: 2 columns
end note

note right of Pagination
    **Pagination:**
    - Laravel pagination
    - Configurable items per page
    - Jump to page functionality
    - Total count display
end note

' Add responsive behavior notes
note bottom of Browser
    **Responsive Behavior:**
    - Desktop: 3-column grid
    - Tablet: 2-column grid  
    - Mobile: Single column stack
    - Sidebar collapses to hamburger menu
    - Touch-friendly buttons on mobile
end note

@enduml
