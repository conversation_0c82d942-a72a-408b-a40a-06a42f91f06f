# Dokumentasi DisaCloud05-v4

## Daftar Isi
1. [Pendahuluan](#pendahuluan)
2. [Arsitektur Sistem](#arsitektur-sistem)
3. [Tech Stack](#tech-stack)
4. [Struktur Database](#struktur-database)
5. [API Endpoints](#api-endpoints)
6. [Frontend](#frontend)
7. [Backend](#backend)
8. [<PERSON><PERSON>](#alur-kerja)
9. [Panduan Instalasi](#panduan-instalasi)
10. [Panduan Pengembangan](#panduan-pengembangan)

## Pendahuluan
DisaCloud05-v4 adalah aplikasi web yang dibangun menggunakan framework Laravel. Dokumentasi ini akan membantu Anda memahami struktur, arsitektur, dan cara kerja aplikasi secara keseluruhan.

## Tech Stack
### Backend
- PHP 8.2
- Laravel Framework 12.0
- MySQL Database
- <PERSON><PERSON> Tinker
- <PERSON><PERSON> Sail (untuk development)

### Frontend
- TailwindCSS 4.0
- Vite 6.2.4
- Chart.js 4.4.9
- Axios 1.8.2

### Development Tools
- <PERSON><PERSON>
- <PERSON><PERSON>il
- PHPUnit
- Faker
- Mockery

## Panduan Instalasi
1. Clone repository
2. Install dependencies PHP:
   ```bash
   composer install
   ```
3. Install dependencies Node.js:
   ```bash
   npm install
   ```
4. Copy file .env.example ke .env
5. Generate application key:
   ```bash
   php artisan key:generate
   ```
6. Jalankan migrasi database:
   ```bash
   php artisan migrate
   ```
7. Jalankan aplikasi:
   ```bash
   npm run dev
   ```

## Panduan Pengembangan
Untuk memulai development, gunakan perintah:
```bash
composer run dev
```
Perintah ini akan menjalankan:
- Laravel development server
- Queue listener
- Log watcher
- Vite development server 