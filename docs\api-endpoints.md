# API Endpoints

## Autentikasi
- `GET /` - <PERSON><PERSON> landing
- `GET /login` - Form login
- `POST /login` - Proses login
- `GET /register` - Form registrasi
- `POST /register` - Proses registrasi
- `POST /logout` - Logout

## Dashboard
- `GET /dashboard` - Halaman dashboard utama

## Manajemen Proyek
- `GET /projects` - Daftar proyek
- `GET /projects/create` - Form pembuatan proyek
- `POST /projects` - Simpan proyek baru
- `GET /projects/{project}/edit` - Form edit proyek
- `PUT /projects/{project}` - Update proyek
- `DELETE /projects/{project}` - Hapus proyek
- `POST /projects/{project}/mark-completed` - Tandai proyek selesai
- `GET /projects/calendar` - Kalender proyek
- `GET /projects/calendar/export` - Export kalender

## Manajemen Pekerja
- `GET /workers` - Daftar pekerja
- `GET /workers/create` - Form tambah pekerja
- `POST /workers` - Simpan pekerja baru
- `GET /workers/{worker}/edit` - Form edit pekerja
- `PUT /workers/{worker}` - Update pekerja
- `DELETE /workers/{worker}` - Hapus pekerja

### Pekerja dalam Proyek
- `GET /projects/{project}/workers` - Daftar pekerja dalam proyek
- `GET /projects/{project}/workers/create` - Form tambah pekerja ke proyek
- `POST /projects/{project}/workers` - Simpan pekerja ke proyek
- `POST /projects/{project}/workers/add-existing` - Tambah pekerja yang sudah ada
- `GET /projects/{project}/workers/{worker}/edit` - Form edit pekerja dalam proyek
- `PUT /projects/{project}/workers/{worker}` - Update pekerja dalam proyek
- `DELETE /projects/{project}/workers/{worker}` - Hapus pekerja dari proyek
- `DELETE /projects/{project}/workers/{worker}/detach` - Lepaskan pekerja dari proyek

## Manajemen Anggaran
- `GET /budgets` - Daftar anggaran
- `GET /budgets/create` - Form pembuatan anggaran
- `GET /budgets/compare` - Perbandingan anggaran
- `POST /budgets` - Simpan anggaran baru
- `GET /budgets/{budget}/download-invoice` - Download invoice
- `GET /budgets/{budget}/edit` - Form edit anggaran
- `PUT /budgets/{budget}` - Update anggaran
- `DELETE /budgets/{budget}` - Hapus anggaran
- `GET /budgets/{budget}` - Detail anggaran

## Manajemen Laporan
- `GET /reports` - Daftar laporan
- `GET /reports/{project}` - Laporan proyek
- `GET /reports/{project}/create` - Form pembuatan laporan
- `POST /reports/{project}` - Simpan laporan baru
- `GET /reports/{project}/reports/{report}/edit` - Form edit laporan
- `PUT /reports/{project}/reports/{report}` - Update laporan
- `GET /reports/{project}/dashboard` - Dashboard laporan
- `PATCH /reports/{project}/tasks/{task}/status` - Update status tugas

## Manajemen Tugas
- `GET /tasks` - Daftar tugas
- `GET /tasks/create` - Form pembuatan tugas
- `POST /tasks` - Simpan tugas baru
- `GET /tasks/{task}` - Detail tugas
- `GET /tasks/{task}/edit` - Form edit tugas
- `PUT /tasks/{task}` - Update tugas
- `DELETE /tasks/{task}` - Hapus tugas
- `PATCH /tasks/{task}/status` - Update status tugas

## Manajemen Material
- `GET /materials` - Daftar material
- `GET /materials/create` - Form pembuatan material
- `POST /materials` - Simpan material baru
- `GET /materials/{material}` - Detail material
- `GET /materials/{material}/edit` - Form edit material
- `PUT /materials/{material}` - Update material
- `DELETE /materials/{material}` - Hapus material
- `GET /materials/project/{projectId}` - Material dalam proyek

## Manajemen Pengeluaran Harian
- `GET /daily-expenses` - Daftar pengeluaran harian
- `GET /daily-expenses/create` - Form pembuatan pengeluaran
- `POST /daily-expenses` - Simpan pengeluaran baru
- `GET /daily-expenses/{dailyExpense}` - Detail pengeluaran
- `GET /daily-expenses/{dailyExpense}/edit` - Form edit pengeluaran
- `PUT /daily-expenses/{dailyExpense}` - Update pengeluaran
- `DELETE /daily-expenses/{dailyExpense}` - Hapus pengeluaran

## Kalender
- `GET /calendar` - Halaman kalender
- `GET /calendar/events` - Data event kalender

## Profil Pengguna
- `GET /profile` - Halaman profil
- `GET /user-create` - Form pembuatan user
- `POST /user-create` - Simpan user baru 