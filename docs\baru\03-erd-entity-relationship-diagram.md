# ERD (Entity Relationship Diagram) DisaCloud05-v4

## 1. Overview ERD

Entity Relationship Diagram (ERD) DisaCloud05-v4 menggambarkan struktur database lengkap dengan semua entitas, atribut, dan relasi yang ada dalam sistem manajemen proyek konstruksi. ERD ini mencerminkan implementasi aktual database MySQL dengan foreign key constraints dan business rules yang diterapkan.

## 2. ERD Lengkap Sistem

```mermaid
erDiagram
    USERS {
        bigint id PK
        varchar name
        varchar email UK
        timestamp email_verified_at
        varchar password
        varchar remember_token
        timestamp created_at
        timestamp updated_at
    }

    PROJECTS {
        bigint id PK
        varchar name
        text description
        varchar status
        varchar priority
        datetime start_date
        datetime end_date
        integer progress
        decimal budget_material
        decimal budget_jasa
        timestamp created_at
        timestamp updated_at
    }

    WORKERS {
        bigint id PK
        varchar name
        varchar specialization
        varchar email UK
        varchar phone
        text skills
        date join_date
        date end_date
        timestamp created_at
        timestamp updated_at
    }

    PROJECT_WORKER {
        bigint id PK
        bigint project_id FK
        bigint worker_id FK
        timestamp created_at
        timestamp updated_at
    }

    TASKS {
        bigint id PK
        bigint project_id FK
        varchar name
        text description
        date due_date
        enum status
        integer priority
        boolean completed
        timestamp created_at
        timestamp updated_at
    }

    MATERIALS {
        bigint id PK
        bigint project_id FK
        varchar name
        text description
        decimal cost
        integer quantity
        varchar unit
        date purchase_date
        timestamp created_at
        timestamp updated_at
    }

    BUDGETS {
        bigint id PK
        bigint project_id FK
        decimal amount
        decimal estimated_profit
        decimal profit_percentage
        text description
        date budget_date
        varchar invoice_file
        varchar status
        varchar type
        timestamp created_at
        timestamp updated_at
    }

    DAILY_REPORTS {
        bigint id PK
        bigint project_id FK
        date report_date
        text activities_done
        text challenges
        text next_plan
        integer progress_percentage
        varchar status
        timestamp created_at
        timestamp updated_at
    }

    DAILY_EXPENSES {
        bigint id PK
        bigint project_id FK
        bigint user_id FK
        date expense_date
        varchar category
        text description
        decimal amount
        timestamp created_at
        timestamp updated_at
    }

    CALENDAR_EVENTS {
        bigint id PK
        bigint project_id FK
        varchar title
        text description
        datetime start_date
        datetime end_date
        varchar location
        timestamp created_at
        timestamp updated_at
    }

    ACTIVITIES {
        bigint id PK
        bigint project_id FK
        varchar title
        text description
        varchar status
        varchar priority
        datetime start_date
        datetime due_date
        bigint assigned_to FK
        bigint created_by FK
        timestamp created_at
        timestamp updated_at
    }

    %% Relationships
    PROJECTS ||--o{ TASKS : "has many"
    PROJECTS ||--o{ MATERIALS : "has many"
    PROJECTS ||--o{ BUDGETS : "has many"
    PROJECTS ||--o{ DAILY_REPORTS : "has many"
    PROJECTS ||--o{ DAILY_EXPENSES : "has many"
    PROJECTS ||--o{ CALENDAR_EVENTS : "has many"
    PROJECTS ||--o{ ACTIVITIES : "has many"

    PROJECTS }o--o{ WORKERS : "employs"
    PROJECT_WORKER }o--|| PROJECTS : "belongs to"
    PROJECT_WORKER }o--|| WORKERS : "belongs to"

    USERS ||--o{ DAILY_EXPENSES : "creates"
    USERS ||--o{ ACTIVITIES : "assigned to"
    USERS ||--o{ ACTIVITIES : "created by"
```

## 3. Penjelasan Entitas dan Atribut

### 3.1 USERS (Pengguna Sistem)
**Deskripsi**: Tabel untuk menyimpan data pengguna sistem (Project Manager)

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `name` (VARCHAR): Nama lengkap pengguna
- `email` (VARCHAR, UNIQUE): Email pengguna untuk login
- `email_verified_at` (TIMESTAMP): Waktu verifikasi email
- `password` (VARCHAR): Password yang di-hash menggunakan bcrypt
- `remember_token` (VARCHAR): Token untuk "remember me" functionality
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Email harus unique dalam sistem
- Password di-hash menggunakan Laravel's bcrypt
- Hanya satu jenis user (Project Manager)

### 3.2 PROJECTS (Proyek)
**Deskripsi**: Tabel utama untuk menyimpan data proyek konstruksi

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `name` (VARCHAR): Nama proyek
- `description` (TEXT): Deskripsi detail proyek
- `status` (VARCHAR): Status proyek (planned, in_progress, completed, on_hold)
- `priority` (VARCHAR): Prioritas proyek (low, medium, high)
- `start_date` (DATETIME): Tanggal mulai proyek
- `end_date` (DATETIME): Tanggal berakhir proyek
- `progress` (INTEGER): Persentase progress proyek (0-100)
- `budget_material` (DECIMAL): Anggaran untuk material
- `budget_jasa` (DECIMAL): Anggaran untuk jasa
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Status harus salah satu dari: planned, in_progress, completed, on_hold
- Priority harus salah satu dari: low, medium, high
- Progress antara 0-100
- end_date harus >= start_date
- Budget otomatis sync dengan tabel BUDGETS

### 3.3 WORKERS (Pekerja)
**Deskripsi**: Tabel untuk menyimpan data pekerja konstruksi

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `name` (VARCHAR): Nama lengkap pekerja
- `specialization` (VARCHAR): Spesialisasi/keahlian utama
- `email` (VARCHAR, UNIQUE): Email pekerja
- `phone` (VARCHAR): Nomor telepon
- `skills` (TEXT): Deskripsi keahlian detail
- `join_date` (DATE): Tanggal bergabung
- `end_date` (DATE): Tanggal berakhir kontrak (nullable)
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Email harus unique jika diisi
- end_date nullable untuk pekerja aktif
- Dapat ditugaskan ke multiple projects (many-to-many)

### 3.4 PROJECT_WORKER (Pivot Table)
**Deskripsi**: Tabel pivot untuk relasi many-to-many antara Projects dan Workers

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `worker_id` (BIGINT, FK): Foreign key ke WORKERS
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project atau worker dihapus
- Kombinasi project_id + worker_id harus unique

### 3.5 TASKS (Tugas)
**Deskripsi**: Tabel untuk menyimpan tugas-tugas dalam proyek

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `name` (VARCHAR): Nama tugas
- `description` (TEXT): Deskripsi detail tugas
- `due_date` (DATE): Tanggal deadline tugas
- `status` (ENUM): Status tugas (pending, in_progress, completed)
- `priority` (INTEGER): Prioritas tugas (0-10)
- `completed` (BOOLEAN): Flag completion status
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project dihapus
- Status completed otomatis set completed = true
- Priority default = 0

### 3.6 MATERIALS (Material)
**Deskripsi**: Tabel untuk menyimpan data material proyek

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `name` (VARCHAR): Nama material
- `description` (TEXT): Deskripsi material
- `cost` (DECIMAL): Harga per unit material
- `quantity` (INTEGER): Jumlah material
- `unit` (VARCHAR): Satuan material (kg, m3, pcs, dll)
- `purchase_date` (DATE): Tanggal pembelian
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project dihapus
- Total cost = cost × quantity (computed attribute)
- Quantity minimal 1

### 3.7 BUDGETS (Anggaran)
**Deskripsi**: Tabel untuk menyimpan data anggaran proyek

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `amount` (DECIMAL): Jumlah anggaran
- `estimated_profit` (DECIMAL): Estimasi keuntungan
- `profit_percentage` (DECIMAL): Persentase keuntungan
- `description` (TEXT): Deskripsi anggaran
- `budget_date` (DATE): Tanggal anggaran
- `invoice_file` (VARCHAR): Path file invoice
- `status` (VARCHAR): Status anggaran (pending, approved, rejected)
- `type` (VARCHAR): Tipe anggaran (material, jasa)
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project dihapus
- profit_percentage = (estimated_profit / amount) × 100
- Status default = pending
- Type untuk membedakan anggaran material dan jasa

### 3.8 DAILY_REPORTS (Laporan Harian)
**Deskripsi**: Tabel untuk menyimpan laporan harian progress proyek

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `report_date` (DATE): Tanggal laporan
- `activities_done` (TEXT): Aktivitas yang telah dilakukan
- `challenges` (TEXT): Tantangan yang dihadapi
- `next_plan` (TEXT): Rencana selanjutnya
- `progress_percentage` (INTEGER): Persentase progress (0-100)
- `status` (VARCHAR): Status laporan (pending, approved)
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project dihapus
- Unique constraint: project_id + report_date (satu laporan per hari per proyek)
- progress_percentage antara 0-100
- Status default = pending

### 3.9 DAILY_EXPENSES (Pengeluaran Harian)
**Deskripsi**: Tabel untuk menyimpan pengeluaran harian proyek

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `user_id` (BIGINT, FK): Foreign key ke USERS (yang mencatat)
- `expense_date` (DATE): Tanggal pengeluaran
- `category` (VARCHAR): Kategori pengeluaran (makan, bensin, dll)
- `description` (TEXT): Deskripsi pengeluaran
- `amount` (DECIMAL): Jumlah pengeluaran
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project atau user dihapus
- Amount harus > 0
- Category untuk kategorisasi pengeluaran

### 3.10 CALENDAR_EVENTS (Event Kalender)
**Deskripsi**: Tabel untuk menyimpan event dan milestone proyek

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `title` (VARCHAR): Judul event
- `description` (TEXT): Deskripsi event
- `start_date` (DATETIME): Tanggal dan waktu mulai
- `end_date` (DATETIME): Tanggal dan waktu berakhir
- `location` (VARCHAR): Lokasi event
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project dihapus
- end_date harus >= start_date jika diisi
- Location nullable untuk event virtual

### 3.11 ACTIVITIES (Aktivitas)
**Deskripsi**: Tabel untuk menyimpan aktivitas proyek (legacy/future use)

**Atribut**:
- `id` (BIGINT, PK): Primary key auto-increment
- `project_id` (BIGINT, FK): Foreign key ke PROJECTS
- `title` (VARCHAR): Judul aktivitas
- `description` (TEXT): Deskripsi aktivitas
- `status` (VARCHAR): Status aktivitas
- `priority` (VARCHAR): Prioritas aktivitas
- `start_date` (DATETIME): Tanggal mulai
- `due_date` (DATETIME): Tanggal deadline
- `assigned_to` (BIGINT, FK): Foreign key ke USERS (assigned user)
- `created_by` (BIGINT, FK): Foreign key ke USERS (creator)
- `created_at`, `updated_at` (TIMESTAMP): Timestamp Laravel

**Business Rules**:
- Cascade delete jika project dihapus
- assigned_to dan created_by reference ke USERS
- due_date harus >= start_date jika diisi

## 4. Relasi Antar Entitas

### 4.1 One-to-Many Relationships

#### PROJECTS → TASKS
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak tugas
- **Foreign Key**: tasks.project_id → projects.id
- **Cascade**: DELETE CASCADE

#### PROJECTS → MATERIALS
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak material
- **Foreign Key**: materials.project_id → projects.id
- **Cascade**: DELETE CASCADE

#### PROJECTS → BUDGETS
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak anggaran
- **Foreign Key**: budgets.project_id → projects.id
- **Cascade**: DELETE CASCADE

#### PROJECTS → DAILY_REPORTS
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak laporan harian
- **Foreign Key**: daily_reports.project_id → projects.id
- **Cascade**: DELETE CASCADE
- **Constraint**: UNIQUE(project_id, report_date)

#### PROJECTS → DAILY_EXPENSES
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak pengeluaran harian
- **Foreign Key**: daily_expenses.project_id → projects.id
- **Cascade**: DELETE CASCADE

#### PROJECTS → CALENDAR_EVENTS
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak event kalender
- **Foreign Key**: calendar_events.project_id → projects.id
- **Cascade**: DELETE CASCADE

#### PROJECTS → ACTIVITIES
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu proyek dapat memiliki banyak aktivitas
- **Foreign Key**: activities.project_id → projects.id
- **Cascade**: DELETE CASCADE

#### USERS → DAILY_EXPENSES
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu user dapat mencatat banyak pengeluaran harian
- **Foreign Key**: daily_expenses.user_id → users.id
- **Cascade**: DELETE CASCADE

#### USERS → ACTIVITIES (assigned_to)
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu user dapat ditugaskan ke banyak aktivitas
- **Foreign Key**: activities.assigned_to → users.id
- **Cascade**: SET NULL

#### USERS → ACTIVITIES (created_by)
- **Kardinalitas**: 1:N
- **Deskripsi**: Satu user dapat membuat banyak aktivitas
- **Foreign Key**: activities.created_by → users.id
- **Cascade**: SET NULL

### 4.2 Many-to-Many Relationships

#### PROJECTS ↔ WORKERS
- **Kardinalitas**: M:N
- **Deskripsi**: Banyak proyek dapat memiliki banyak pekerja, dan sebaliknya
- **Pivot Table**: project_worker
- **Foreign Keys**:
  - project_worker.project_id → projects.id
  - project_worker.worker_id → workers.id
- **Cascade**: DELETE CASCADE pada kedua foreign key

## 5. Constraints dan Indexes

### 5.1 Primary Keys
- Semua tabel menggunakan `id` sebagai primary key dengan tipe BIGINT AUTO_INCREMENT

### 5.2 Foreign Key Constraints
- Semua foreign key menggunakan constraint dengan CASCADE DELETE
- Referential integrity dijaga oleh database engine

### 5.3 Unique Constraints
- `users.email` - Email harus unique
- `workers.email` - Email pekerja harus unique
- `daily_reports(project_id, report_date)` - Satu laporan per hari per proyek

### 5.4 Indexes (Recommended)
- Index pada semua foreign key columns untuk performance
- Index pada frequently queried columns:
  - `projects.status`
  - `projects.start_date`, `projects.end_date`
  - `tasks.status`, `tasks.due_date`
  - `daily_expenses.expense_date`
  - `calendar_events.start_date`

## 6. Data Integrity Rules

### 6.1 Business Logic Constraints
- Project status harus valid (planned, in_progress, completed, on_hold)
- Task status harus valid (pending, in_progress, completed)
- Budget status harus valid (pending, approved, rejected)
- Progress percentage antara 0-100
- Dates validation (end_date >= start_date)

### 6.2 Data Validation
- Email format validation
- Decimal precision untuk monetary values (15,2)
- Required fields validation
- Length constraints untuk VARCHAR fields

## 7. Performance Considerations

### 7.1 Query Optimization
- Proper indexing pada frequently queried columns
- Eager loading untuk menghindari N+1 queries
- Query optimization untuk dashboard metrics

### 7.2 Data Archiving
- Consideration untuk archiving completed projects
- Soft delete implementation untuk audit trail
- Data retention policies untuk historical data

---

ERD DisaCloud05-v4 ini mencerminkan struktur database yang robust dan scalable untuk mendukung sistem manajemen proyek konstruksi yang komprehensif. Dengan relasi yang jelas dan constraints yang tepat, database ini mampu menjaga integritas data sambil memberikan performance yang optimal.