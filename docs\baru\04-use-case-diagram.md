# Use Case Diagram DisaCloud05-v4

## 1. Overview Use Case Diagram

Use Case Diagram DisaCloud05-v4 menggambarkan semua fungsionalitas yang dapat dilakukan oleh Project Manager dalam sistem manajemen proyek konstruksi. Diagram ini mencerminkan implementasi aktual aplikasi dengan satu jenis pengguna yang memiliki akses penuh ke semua fitur sistem.

## 2. Use Case Diagram Lengkap

```mermaid
graph TB
    subgraph "DisaCloud05-v4 System"
        subgraph "Authentication Management"
            UC1[Login to System]
            UC2[Register Account]
            UC3[Logout from System]
        end

        subgraph "Dashboard & Analytics"
            UC4[View Dashboard]
            UC5[Monitor Project Status]
            UC6[View Analytics Charts]
            UC7[Check Notifications]
        end

        subgraph "Project Management"
            UC8[Create New Project]
            UC9[View Project List]
            UC10[Edit Project Details]
            UC11[Delete Project]
            UC12[Mark Project Completed]
            UC13[Update Project Progress]
            UC14[Set Project Priority]
        end

        subgraph "Worker Management"
            UC15[Create Worker Profile]
            UC16[View Worker List]
            UC17[Edit Worker Details]
            UC18[Delete Worker]
            UC19[Assign Worker to Project]
            UC20[Remove Worker from Project]
            UC21[View Worker Skills]
        end

        subgraph "Task Management"
            UC22[Create Project Task]
            UC23[View Task List]
            UC24[Edit Task Details]
            UC25[Delete Task]
            UC26[Update Task Status]
            UC27[Set Task Priority]
            UC28[Mark Task Completed]
        end

        subgraph "Budget Management"
            UC29[Create Project Budget]
            UC30[View Budget List]
            UC31[Edit Budget Details]
            UC32[Delete Budget]
            UC33[Calculate Profit Percentage]
            UC34[Upload Invoice File]
            UC35[Compare Budget vs Actual]
            UC36[Download Invoice]
        end

        subgraph "Material Management"
            UC37[Add Project Material]
            UC38[View Material List]
            UC39[Edit Material Details]
            UC40[Delete Material]
            UC41[Calculate Material Cost]
            UC42[Track Material Usage]
        end

        subgraph "Daily Operations"
            UC43[Record Daily Expense]
            UC44[View Expense List]
            UC45[Edit Daily Expense]
            UC46[Delete Daily Expense]
            UC47[Categorize Expenses]
            UC48[Create Daily Report]
            UC49[View Daily Reports]
            UC50[Edit Daily Report]
            UC51[Update Progress Percentage]
        end

        subgraph "Calendar & Events"
            UC52[Create Calendar Event]
            UC53[View Calendar]
            UC54[Edit Event Details]
            UC55[Delete Event]
            UC56[Set Event Location]
            UC57[Export Calendar]
        end

        subgraph "Reporting & Analytics"
            UC58[Generate Project Reports]
            UC59[View Report Dashboard]
            UC60[Create Custom Reports]
            UC61[Export Report Data]
            UC62[Analyze Project Performance]
        end

        subgraph "User Profile Management"
            UC63[View User Profile]
            UC64[Create New User Account]
        end
    end

    %% Actor
    PM[Project Manager]

    %% Relationships
    PM --> UC1
    PM --> UC2
    PM --> UC3
    PM --> UC4
    PM --> UC5
    PM --> UC6
    PM --> UC7
    PM --> UC8
    PM --> UC9
    PM --> UC10
    PM --> UC11
    PM --> UC12
    PM --> UC13
    PM --> UC14
    PM --> UC15
    PM --> UC16
    PM --> UC17
    PM --> UC18
    PM --> UC19
    PM --> UC20
    PM --> UC21
    PM --> UC22
    PM --> UC23
    PM --> UC24
    PM --> UC25
    PM --> UC26
    PM --> UC27
    PM --> UC28
    PM --> UC29
    PM --> UC30
    PM --> UC31
    PM --> UC32
    PM --> UC33
    PM --> UC34
    PM --> UC35
    PM --> UC36
    PM --> UC37
    PM --> UC38
    PM --> UC39
    PM --> UC40
    PM --> UC41
    PM --> UC42
    PM --> UC43
    PM --> UC44
    PM --> UC45
    PM --> UC46
    PM --> UC47
    PM --> UC48
    PM --> UC49
    PM --> UC50
    PM --> UC51
    PM --> UC52
    PM --> UC53
    PM --> UC54
    PM --> UC55
    PM --> UC56
    PM --> UC57
    PM --> UC58
    PM --> UC59
    PM --> UC60
    PM --> UC61
    PM --> UC62
    PM --> UC63
    PM --> UC64
```

## 3. Actor Description

### 3.1 Project Manager
**Deskripsi**: Satu-satunya jenis pengguna dalam sistem DisaCloud05-v4

**Karakteristik**:
- Memiliki akses penuh ke semua fitur sistem
- Bertanggung jawab atas manajemen proyek konstruksi
- Dapat melakukan semua operasi CRUD pada semua entitas
- Memiliki otoritas untuk membuat keputusan strategis
- Menggunakan sistem untuk monitoring dan controlling proyek

**Tanggung Jawab**:
- Mengelola seluruh siklus hidup proyek
- Mengawasi kinerja tim dan progress proyek
- Mengelola anggaran dan resources
- Membuat laporan dan analisis
- Mengambil keputusan berdasarkan data

## 4. Use Case Descriptions

### 4.1 Authentication Management

#### UC1: Login to System
**Deskripsi**: Project Manager melakukan login ke sistem
**Precondition**: User memiliki akun yang valid
**Main Flow**:
1. User mengakses halaman login
2. User memasukkan email dan password
3. Sistem memvalidasi kredensial
4. Sistem membuat session
5. User diarahkan ke dashboard

**Postcondition**: User berhasil login dan dapat mengakses sistem

#### UC2: Register Account
**Deskripsi**: Membuat akun pengguna baru
**Precondition**: User belum memiliki akun
**Main Flow**:
1. User mengakses halaman registrasi
2. User mengisi form registrasi (nama, email, password)
3. Sistem memvalidasi data
4. Sistem membuat akun baru
5. User diarahkan ke halaman login

**Postcondition**: Akun baru berhasil dibuat

#### UC3: Logout from System
**Deskripsi**: Project Manager keluar dari sistem
**Precondition**: User sudah login
**Main Flow**:
1. User mengklik tombol logout
2. Sistem menghapus session
3. User diarahkan ke halaman landing

**Postcondition**: User berhasil logout
### 4.2 Dashboard & Analytics

#### UC4: View Dashboard
**Deskripsi**: Project Manager melihat dashboard utama dengan ringkasan proyek
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses dashboard
2. Sistem menampilkan metrics utama (total proyek, progress, deadline)
3. Sistem menampilkan chart dan visualisasi data
4. User dapat melihat notifikasi penting

**Postcondition**: Dashboard ditampilkan dengan data terkini

#### UC5: Monitor Project Status
**Deskripsi**: Monitoring status semua proyek aktif
**Precondition**: User sudah login dan ada proyek dalam sistem
**Main Flow**:
1. User mengakses monitoring proyek
2. Sistem menampilkan status semua proyek
3. User dapat melihat progress dan deadline
4. User dapat mengidentifikasi proyek yang memerlukan perhatian

**Postcondition**: Status proyek berhasil dimonitor

#### UC6: View Analytics Charts
**Deskripsi**: Melihat chart dan analisis data proyek
**Precondition**: User sudah login dan ada data proyek
**Main Flow**:
1. User mengakses halaman analytics
2. Sistem menampilkan berbagai chart (progress, budget, timeline)
3. User dapat menganalisis trend dan pattern
4. User dapat menggunakan data untuk decision making

**Postcondition**: Analytics charts berhasil ditampilkan

#### UC7: Check Notifications
**Deskripsi**: Memeriksa notifikasi sistem tentang proyek
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses notifikasi
2. Sistem menampilkan notifikasi urgent (deadline, stalled projects)
3. User dapat melihat detail notifikasi
4. User dapat mengambil action berdasarkan notifikasi

**Postcondition**: Notifikasi berhasil diperiksa

### 4.3 Project Management

#### UC8: Create New Project
**Deskripsi**: Membuat proyek konstruksi baru
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses form create project
2. User mengisi detail proyek (nama, deskripsi, tanggal, budget)
3. Sistem memvalidasi data
4. Sistem membuat proyek baru
5. Sistem otomatis membuat budget entries

**Postcondition**: Proyek baru berhasil dibuat

#### UC9: View Project List
**Deskripsi**: Melihat daftar semua proyek
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses halaman projects
2. Sistem menampilkan list semua proyek
3. User dapat melihat status, progress, dan deadline
4. User dapat melakukan filtering dan sorting

**Postcondition**: Daftar proyek berhasil ditampilkan

#### UC10: Edit Project Details
**Deskripsi**: Mengubah detail proyek yang sudah ada
**Precondition**: User sudah login dan proyek exists
**Main Flow**:
1. User memilih proyek untuk diedit
2. User mengubah detail proyek
3. Sistem memvalidasi perubahan
4. Sistem menyimpan perubahan
5. Sistem update related data jika diperlukan

**Postcondition**: Detail proyek berhasil diupdate

#### UC11: Delete Project
**Deskripsi**: Menghapus proyek dari sistem
**Precondition**: User sudah login dan proyek exists
**Main Flow**:
1. User memilih proyek untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus proyek dan related data

**Postcondition**: Proyek berhasil dihapus

#### UC12: Mark Project Completed
**Deskripsi**: Menandai proyek sebagai completed
**Precondition**: User sudah login dan proyek exists
**Main Flow**:
1. User memilih proyek untuk di-complete
2. User mengubah status menjadi completed
3. Sistem update status proyek
4. Sistem update progress menjadi 100%

**Postcondition**: Proyek berhasil ditandai completed

#### UC13: Update Project Progress
**Deskripsi**: Mengupdate persentase progress proyek
**Precondition**: User sudah login dan proyek exists
**Main Flow**:
1. User mengakses project detail
2. User mengubah persentase progress
3. Sistem memvalidasi nilai progress (0-100)
4. Sistem menyimpan perubahan

**Postcondition**: Progress proyek berhasil diupdate

#### UC14: Set Project Priority
**Deskripsi**: Mengatur prioritas proyek
**Precondition**: User sudah login dan proyek exists
**Main Flow**:
1. User mengakses project detail
2. User memilih prioritas (low, medium, high)
3. Sistem menyimpan perubahan prioritas
4. Sistem update tampilan berdasarkan prioritas

**Postcondition**: Prioritas proyek berhasil diset

### 4.4 Worker Management

#### UC15: Create Worker Profile
**Deskripsi**: Membuat profil pekerja baru
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses form create worker
2. User mengisi data pekerja (nama, spesialisasi, kontak, skills)
3. Sistem memvalidasi data
4. Sistem membuat profil pekerja baru

**Postcondition**: Profil pekerja baru berhasil dibuat

#### UC16: View Worker List
**Deskripsi**: Melihat daftar semua pekerja
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses halaman workers
2. Sistem menampilkan list semua pekerja
3. User dapat melihat spesialisasi dan status
4. User dapat melakukan search dan filter

**Postcondition**: Daftar pekerja berhasil ditampilkan

#### UC17: Edit Worker Details
**Deskripsi**: Mengubah detail profil pekerja
**Precondition**: User sudah login dan worker exists
**Main Flow**:
1. User memilih pekerja untuk diedit
2. User mengubah detail pekerja
3. Sistem memvalidasi perubahan
4. Sistem menyimpan perubahan

**Postcondition**: Detail pekerja berhasil diupdate

#### UC18: Delete Worker
**Deskripsi**: Menghapus pekerja dari sistem
**Precondition**: User sudah login dan worker exists
**Main Flow**:
1. User memilih pekerja untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus pekerja dan assignment

**Postcondition**: Pekerja berhasil dihapus

#### UC19: Assign Worker to Project
**Deskripsi**: Menugaskan pekerja ke proyek tertentu
**Precondition**: User sudah login, worker dan project exists
**Main Flow**:
1. User mengakses project atau worker detail
2. User memilih worker/project untuk assignment
3. Sistem membuat relasi many-to-many
4. Sistem menyimpan assignment

**Postcondition**: Pekerja berhasil ditugaskan ke proyek

#### UC20: Remove Worker from Project
**Deskripsi**: Menghapus assignment pekerja dari proyek
**Precondition**: User sudah login dan assignment exists
**Main Flow**:
1. User mengakses assignment detail
2. User menghapus assignment
3. Sistem menghapus relasi many-to-many
4. Sistem update project data

**Postcondition**: Assignment pekerja berhasil dihapus

#### UC21: View Worker Skills
**Deskripsi**: Melihat keahlian dan spesialisasi pekerja
**Precondition**: User sudah login dan worker exists
**Main Flow**:
1. User mengakses worker detail
2. Sistem menampilkan skills dan spesialisasi
3. User dapat melihat riwayat proyek
4. User dapat mengevaluasi kompetensi

**Postcondition**: Skills pekerja berhasil ditampilkan
### 4.5 Task Management

#### UC22: Create Project Task
**Deskripsi**: Membuat tugas baru dalam proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses project detail
2. User mengakses form create task
3. User mengisi detail task (nama, deskripsi, deadline, prioritas)
4. Sistem memvalidasi data
5. Sistem membuat task baru

**Postcondition**: Task baru berhasil dibuat

#### UC23: View Task List
**Deskripsi**: Melihat daftar semua task dalam proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses project detail
2. Sistem menampilkan list semua task
3. User dapat melihat status, deadline, dan prioritas
4. User dapat melakukan filtering berdasarkan status

**Postcondition**: Daftar task berhasil ditampilkan

#### UC24: Edit Task Details
**Deskripsi**: Mengubah detail task yang sudah ada
**Precondition**: User sudah login dan task exists
**Main Flow**:
1. User memilih task untuk diedit
2. User mengubah detail task
3. Sistem memvalidasi perubahan
4. Sistem menyimpan perubahan

**Postcondition**: Detail task berhasil diupdate

#### UC25: Delete Task
**Deskripsi**: Menghapus task dari proyek
**Precondition**: User sudah login dan task exists
**Main Flow**:
1. User memilih task untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus task

**Postcondition**: Task berhasil dihapus

#### UC26: Update Task Status
**Deskripsi**: Mengubah status task (pending, in_progress, completed)
**Precondition**: User sudah login dan task exists
**Main Flow**:
1. User mengakses task detail
2. User mengubah status task
3. Sistem update status task
4. Sistem update completed flag jika status = completed

**Postcondition**: Status task berhasil diupdate

#### UC27: Set Task Priority
**Deskripsi**: Mengatur prioritas task (0-10)
**Precondition**: User sudah login dan task exists
**Main Flow**:
1. User mengakses task detail
2. User mengubah prioritas task
3. Sistem memvalidasi nilai prioritas
4. Sistem menyimpan perubahan

**Postcondition**: Prioritas task berhasil diset

#### UC28: Mark Task Completed
**Deskripsi**: Menandai task sebagai completed
**Precondition**: User sudah login dan task exists
**Main Flow**:
1. User memilih task untuk di-complete
2. User mengubah status menjadi completed
3. Sistem set completed = true
4. Sistem update project progress jika diperlukan

**Postcondition**: Task berhasil ditandai completed

### 4.6 Budget Management

#### UC29: Create Project Budget
**Deskripsi**: Membuat anggaran untuk proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses project budget
2. User mengakses form create budget
3. User mengisi detail budget (amount, type, description)
4. Sistem menghitung profit percentage
5. Sistem membuat budget entry

**Postcondition**: Budget baru berhasil dibuat

#### UC30: View Budget List
**Deskripsi**: Melihat daftar semua budget dalam proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses project budget
2. Sistem menampilkan list semua budget
3. User dapat melihat amount, profit, dan status
4. User dapat melihat total budget

**Postcondition**: Daftar budget berhasil ditampilkan

#### UC31: Edit Budget Details
**Deskripsi**: Mengubah detail budget yang sudah ada
**Precondition**: User sudah login dan budget exists
**Main Flow**:
1. User memilih budget untuk diedit
2. User mengubah detail budget
3. Sistem recalculate profit percentage
4. Sistem menyimpan perubahan

**Postcondition**: Detail budget berhasil diupdate

#### UC32: Delete Budget
**Deskripsi**: Menghapus budget dari proyek
**Precondition**: User sudah login dan budget exists
**Main Flow**:
1. User memilih budget untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus budget

**Postcondition**: Budget berhasil dihapus

#### UC33: Calculate Profit Percentage
**Deskripsi**: Sistem menghitung persentase keuntungan otomatis
**Precondition**: Budget dengan amount dan estimated_profit exists
**Main Flow**:
1. User mengisi estimated_profit
2. Sistem menghitung profit_percentage = (estimated_profit / amount) × 100
3. Sistem menyimpan hasil perhitungan
4. Sistem menampilkan hasil ke user

**Postcondition**: Profit percentage berhasil dihitung

#### UC34: Upload Invoice File
**Deskripsi**: Upload file invoice untuk budget
**Precondition**: User sudah login dan budget exists
**Main Flow**:
1. User mengakses budget detail
2. User memilih file invoice untuk diupload
3. Sistem memvalidasi file (format, size)
4. Sistem menyimpan file dan path

**Postcondition**: Invoice file berhasil diupload

#### UC35: Compare Budget vs Actual
**Deskripsi**: Membandingkan anggaran dengan pengeluaran aktual
**Precondition**: User sudah login, budget dan expenses exists
**Main Flow**:
1. User mengakses budget analysis
2. Sistem menghitung total expenses
3. Sistem membandingkan dengan budget
4. Sistem menampilkan variance analysis

**Postcondition**: Perbandingan budget vs actual berhasil ditampilkan

#### UC36: Download Invoice
**Deskripsi**: Download file invoice yang sudah diupload
**Precondition**: User sudah login dan invoice file exists
**Main Flow**:
1. User mengakses budget detail
2. User mengklik link download invoice
3. Sistem memvalidasi akses file
4. Sistem mengirim file untuk download

**Postcondition**: Invoice file berhasil didownload

### 4.7 Material Management

#### UC37: Add Project Material
**Deskripsi**: Menambahkan material ke proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses project materials
2. User mengakses form add material
3. User mengisi detail material (nama, cost, quantity, unit)
4. Sistem menghitung total cost
5. Sistem menyimpan material

**Postcondition**: Material baru berhasil ditambahkan

#### UC38: View Material List
**Deskripsi**: Melihat daftar semua material dalam proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses project materials
2. Sistem menampilkan list semua material
3. User dapat melihat cost, quantity, dan total
4. User dapat melihat total cost semua material

**Postcondition**: Daftar material berhasil ditampilkan

#### UC39: Edit Material Details
**Deskripsi**: Mengubah detail material yang sudah ada
**Precondition**: User sudah login dan material exists
**Main Flow**:
1. User memilih material untuk diedit
2. User mengubah detail material
3. Sistem recalculate total cost
4. Sistem menyimpan perubahan

**Postcondition**: Detail material berhasil diupdate

#### UC40: Delete Material
**Deskripsi**: Menghapus material dari proyek
**Precondition**: User sudah login dan material exists
**Main Flow**:
1. User memilih material untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus material

**Postcondition**: Material berhasil dihapus

#### UC41: Calculate Material Cost
**Deskripsi**: Sistem menghitung total cost material otomatis
**Precondition**: Material dengan cost dan quantity exists
**Main Flow**:
1. User mengisi cost dan quantity
2. Sistem menghitung total_cost = cost × quantity
3. Sistem menyimpan hasil perhitungan
4. Sistem update total project material cost

**Postcondition**: Material cost berhasil dihitung

#### UC42: Track Material Usage
**Deskripsi**: Melacak penggunaan material dalam proyek
**Precondition**: User sudah login dan material exists
**Main Flow**:
1. User mengakses material tracking
2. Sistem menampilkan usage history
3. User dapat melihat purchase date dan quantity
4. User dapat menganalisis material consumption

**Postcondition**: Material usage berhasil dilacak
### 4.8 Daily Operations

#### UC43: Record Daily Expense
**Deskripsi**: Mencatat pengeluaran harian proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses daily expenses
2. User mengakses form record expense
3. User mengisi detail expense (tanggal, kategori, amount, deskripsi)
4. Sistem memvalidasi data
5. Sistem menyimpan expense record

**Postcondition**: Daily expense berhasil dicatat

#### UC44: View Expense List
**Deskripsi**: Melihat daftar semua pengeluaran harian
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses daily expenses
2. Sistem menampilkan list semua expenses
3. User dapat melihat kategori, amount, dan tanggal
4. User dapat melakukan filtering berdasarkan kategori/tanggal

**Postcondition**: Daftar expense berhasil ditampilkan

#### UC45: Edit Daily Expense
**Deskripsi**: Mengubah detail pengeluaran harian
**Precondition**: User sudah login dan expense exists
**Main Flow**:
1. User memilih expense untuk diedit
2. User mengubah detail expense
3. Sistem memvalidasi perubahan
4. Sistem menyimpan perubahan

**Postcondition**: Daily expense berhasil diupdate

#### UC46: Delete Daily Expense
**Deskripsi**: Menghapus pengeluaran harian
**Precondition**: User sudah login dan expense exists
**Main Flow**:
1. User memilih expense untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus expense

**Postcondition**: Daily expense berhasil dihapus

#### UC47: Categorize Expenses
**Deskripsi**: Mengkategorikan pengeluaran (makan, bensin, dll)
**Precondition**: User sudah login
**Main Flow**:
1. User mengisi kategori saat record expense
2. Sistem menyimpan kategori
3. User dapat filter berdasarkan kategori
4. Sistem menampilkan summary per kategori

**Postcondition**: Expense berhasil dikategorikan

#### UC48: Create Daily Report
**Deskripsi**: Membuat laporan harian progress proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses daily reports
2. User mengakses form create report
3. User mengisi activities done, challenges, next plan, progress
4. Sistem memvalidasi data (unique per tanggal per proyek)
5. Sistem menyimpan daily report

**Postcondition**: Daily report berhasil dibuat

#### UC49: View Daily Reports
**Deskripsi**: Melihat daftar semua laporan harian
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses daily reports
2. Sistem menampilkan list semua reports
3. User dapat melihat tanggal, progress, dan status
4. User dapat melakukan filtering berdasarkan tanggal

**Postcondition**: Daftar daily reports berhasil ditampilkan

#### UC50: Edit Daily Report
**Deskripsi**: Mengubah detail laporan harian
**Precondition**: User sudah login dan report exists
**Main Flow**:
1. User memilih report untuk diedit
2. User mengubah detail report
3. Sistem memvalidasi perubahan
4. Sistem menyimpan perubahan

**Postcondition**: Daily report berhasil diupdate

#### UC51: Update Progress Percentage
**Deskripsi**: Mengupdate persentase progress dalam daily report
**Precondition**: User sudah login dan report exists
**Main Flow**:
1. User mengakses daily report
2. User mengubah progress percentage (0-100)
3. Sistem memvalidasi nilai progress
4. Sistem update project progress jika diperlukan

**Postcondition**: Progress percentage berhasil diupdate

### 4.9 Calendar & Events

#### UC52: Create Calendar Event
**Deskripsi**: Membuat event dalam kalender proyek
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses calendar
2. User mengakses form create event
3. User mengisi detail event (title, tanggal, lokasi, deskripsi)
4. Sistem memvalidasi data
5. Sistem membuat calendar event

**Postcondition**: Calendar event berhasil dibuat

#### UC53: View Calendar
**Deskripsi**: Melihat kalender proyek dengan semua events
**Precondition**: User sudah login dan project exists
**Main Flow**:
1. User mengakses calendar view
2. Sistem menampilkan calendar dengan events
3. User dapat melihat events berdasarkan tanggal
4. User dapat navigate antar bulan/minggu

**Postcondition**: Calendar berhasil ditampilkan

#### UC54: Edit Event Details
**Deskripsi**: Mengubah detail event yang sudah ada
**Precondition**: User sudah login dan event exists
**Main Flow**:
1. User memilih event untuk diedit
2. User mengubah detail event
3. Sistem memvalidasi perubahan
4. Sistem menyimpan perubahan

**Postcondition**: Event details berhasil diupdate

#### UC55: Delete Event
**Deskripsi**: Menghapus event dari kalender
**Precondition**: User sudah login dan event exists
**Main Flow**:
1. User memilih event untuk dihapus
2. Sistem menampilkan konfirmasi
3. User mengkonfirmasi penghapusan
4. Sistem menghapus event

**Postcondition**: Event berhasil dihapus

#### UC56: Set Event Location
**Deskripsi**: Mengatur lokasi untuk event
**Precondition**: User sudah login dan event exists
**Main Flow**:
1. User mengakses event detail
2. User mengisi lokasi event
3. Sistem menyimpan lokasi
4. Sistem menampilkan lokasi di calendar view

**Postcondition**: Event location berhasil diset

#### UC57: Export Calendar
**Deskripsi**: Export kalender untuk integrasi eksternal
**Precondition**: User sudah login dan events exist
**Main Flow**:
1. User mengakses export calendar
2. User memilih format export (iCal, CSV)
3. Sistem generate file export
4. Sistem mengirim file untuk download

**Postcondition**: Calendar berhasil diexport

### 4.10 Reporting & Analytics

#### UC58: Generate Project Reports
**Deskripsi**: Generate berbagai jenis laporan proyek
**Precondition**: User sudah login dan project data exists
**Main Flow**:
1. User mengakses reporting module
2. User memilih jenis report (progress, budget, timeline)
3. User mengatur parameter report
4. Sistem generate report
5. Sistem menampilkan report

**Postcondition**: Project report berhasil digenerate

#### UC59: View Report Dashboard
**Deskripsi**: Melihat dashboard khusus untuk reporting
**Precondition**: User sudah login dan project data exists
**Main Flow**:
1. User mengakses report dashboard
2. Sistem menampilkan key metrics dan charts
3. User dapat melihat summary semua proyek
4. User dapat drill-down ke detail

**Postcondition**: Report dashboard berhasil ditampilkan

#### UC60: Create Custom Reports
**Deskripsi**: Membuat laporan custom sesuai kebutuhan
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses custom report builder
2. User memilih data fields dan filters
3. User mengatur format dan layout
4. Sistem generate custom report

**Postcondition**: Custom report berhasil dibuat

#### UC61: Export Report Data
**Deskripsi**: Export data report ke berbagai format
**Precondition**: User sudah login dan report exists
**Main Flow**:
1. User mengakses report
2. User memilih format export (PDF, Excel, CSV)
3. Sistem generate file export
4. Sistem mengirim file untuk download

**Postcondition**: Report data berhasil diexport

#### UC62: Analyze Project Performance
**Deskripsi**: Menganalisis performance proyek dengan metrics
**Precondition**: User sudah login dan project data exists
**Main Flow**:
1. User mengakses performance analysis
2. Sistem menampilkan KPI dan metrics
3. User dapat melihat trend dan comparison
4. User dapat mengidentifikasi improvement areas

**Postcondition**: Project performance berhasil dianalisis

### 4.11 User Profile Management

#### UC63: View User Profile
**Deskripsi**: Melihat profil pengguna yang sedang login
**Precondition**: User sudah login
**Main Flow**:
1. User mengakses user profile
2. Sistem menampilkan informasi user (nama, email)
3. User dapat melihat activity history
4. User dapat melihat project assignments

**Postcondition**: User profile berhasil ditampilkan

#### UC64: Create New User Account
**Deskripsi**: Membuat akun pengguna baru (admin function)
**Precondition**: User sudah login dengan privilege
**Main Flow**:
1. User mengakses user management
2. User mengakses form create user
3. User mengisi data user baru
4. Sistem membuat akun baru

**Postcondition**: User account baru berhasil dibuat

## 5. Use Case Relationships

### 5.1 Include Relationships
- **Login** includes **Session Validation**
- **Create Project** includes **Budget Sync**
- **Calculate Material Cost** includes **Update Project Total**
- **Calculate Profit Percentage** includes **Budget Validation**

### 5.2 Extend Relationships
- **View Dashboard** extends **Check Notifications**
- **Create Daily Report** extends **Update Project Progress**
- **Mark Task Completed** extends **Update Project Progress**

### 5.3 Generalization Relationships
- **CRUD Operations** generalize to specific entity operations
- **File Operations** generalize to Upload/Download functions

## 6. Business Rules dan Constraints

### 6.1 Authentication Rules
- User harus login untuk mengakses semua fitur
- Session timeout setelah periode inaktif
- Password harus memenuhi kriteria keamanan

### 6.2 Data Validation Rules
- Email harus unique dalam sistem
- Progress percentage antara 0-100
- End date harus >= start date
- Budget amount harus > 0

### 6.3 Business Logic Rules
- Satu daily report per hari per proyek
- Project status workflow: planned → in_progress → completed
- Task completion otomatis update project progress
- Budget sync otomatis saat project dibuat

## 7. Non-Functional Requirements

### 7.1 Performance Requirements
- Dashboard load time < 3 detik
- Report generation < 10 detik
- File upload max 10MB
- Concurrent users support

### 7.2 Security Requirements
- HTTPS untuk semua komunikasi
- Input validation dan sanitization
- CSRF protection
- Session security

### 7.3 Usability Requirements
- Responsive design untuk mobile
- Intuitive navigation
- Clear error messages
- Consistent UI/UX

---

Use Case Diagram DisaCloud05-v4 ini mencakup semua fungsionalitas yang tersedia untuk Project Manager dalam mengelola proyek konstruksi. Dengan 64 use case yang terorganisir dalam 11 kategori utama, sistem ini menyediakan solusi komprehensif untuk manajemen proyek yang efisien dan efektif.
