# Class Diagram DisaCloud05-v4

## 1. Overview Class Diagram

Class Diagram DisaCloud05-v4 menggambarkan struktur kelas dan relasi antar kelas yang benar-benar terjadi dalam setiap fungsional dan halaman aplikasi web. Diagram ini mencerminkan implementasi aktual dari model <PERSON><PERSON>, controller, dan komponen sistem yang saling berinteraksi dalam mengelola proyek konstruksi.

## 2. Class Diagram Lengkap

```mermaid
classDiagram
    %% Model Classes
    class User {
        -id: bigint
        -name: string
        -email: string
        -email_verified_at: timestamp
        -password: string
        -remember_token: string
        -created_at: timestamp
        -updated_at: timestamp
        +dailyExpenses(): HasMany
        +assignedActivities(): HasMany
        +createdActivities(): HasMany
        +getName(): string
        +getEmail(): string
        +hasVerifiedEmail(): bool
    }

    class Project {
        -id: bigint
        -name: string
        -description: text
        -status: string
        -priority: string
        -start_date: datetime
        -end_date: datetime
        -progress: integer
        -budget_material: decimal
        -budget_jasa: decimal
        -created_at: timestamp
        -updated_at: timestamp
        +tasks(): HasMany
        +materials(): HasMany
        +budgets(): HasMany
        +dailyReports(): HasMany
        +dailyExpenses(): HasMany
        +calendarEvents(): HasMany
        +activities(): HasMany
        +workers(): BelongsToMany
        +isCompleted(): bool
        +isOverdue(): bool
        +getProgressPercentage(): int
        +getTotalBudget(): decimal
        +getRemainingDays(): int
        +getStatusColor(): string
        +getPriorityColor(): string
    }

    class Worker {
        -id: bigint
        -name: string
        -specialization: string
        -email: string
        -phone: string
        -skills: text
        -join_date: date
        -end_date: date
        -created_at: timestamp
        -updated_at: timestamp
        +projects(): BelongsToMany
        +isActive(): bool
        +getSkillsArray(): array
        +getExperience(): int
        +getProjectCount(): int
    }

    class Task {
        -id: bigint
        -project_id: bigint
        -name: string
        -description: text
        -due_date: date
        -status: enum
        -priority: integer
        -completed: boolean
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +isCompleted(): bool
        +isOverdue(): bool
        +getStatusColor(): string
        +getPriorityLevel(): string
        +markAsCompleted(): void
        +getDaysUntilDue(): int
    }

    class Material {
        -id: bigint
        -project_id: bigint
        -name: string
        -description: text
        -cost: decimal
        -quantity: integer
        -unit: string
        -purchase_date: date
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +getTotalCost(): decimal
        +getFormattedCost(): string
        +isExpensive(): bool
        +getCostPerUnit(): decimal
    }

    class Budget {
        -id: bigint
        -project_id: bigint
        -amount: decimal
        -estimated_profit: decimal
        -profit_percentage: decimal
        -description: text
        -budget_date: date
        -invoice_file: string
        -status: string
        -type: string
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +calculateProfitPercentage(): decimal
        +getFormattedAmount(): string
        +hasInvoice(): bool
        +getInvoiceUrl(): string
        +isApproved(): bool
        +getStatusColor(): string
    }

    class DailyReport {
        -id: bigint
        -project_id: bigint
        -report_date: date
        -activities_done: text
        -challenges: text
        -next_plan: text
        -progress_percentage: integer
        -status: string
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +getFormattedDate(): string
        +isApproved(): bool
        +getProgressColor(): string
        +hasActivities(): bool
        +hasChallenges(): bool
    }

    class DailyExpense {
        -id: bigint
        -project_id: bigint
        -user_id: bigint
        -expense_date: date
        -category: string
        -description: text
        -amount: decimal
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +user(): BelongsTo
        +getFormattedAmount(): string
        +getFormattedDate(): string
        +getCategoryColor(): string
        +isExpensive(): bool
    }

    class CalendarEvent {
        -id: bigint
        -project_id: bigint
        -title: string
        -description: text
        -start_date: datetime
        -end_date: datetime
        -location: string
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +isUpcoming(): bool
        +isToday(): bool
        +getDuration(): int
        +getFormattedStartDate(): string
        +getFormattedEndDate(): string
        +hasLocation(): bool
    }

    class Activity {
        -id: bigint
        -project_id: bigint
        -title: string
        -description: text
        -status: string
        -priority: string
        -start_date: datetime
        -due_date: datetime
        -assigned_to: bigint
        -created_by: bigint
        -created_at: timestamp
        -updated_at: timestamp
        +project(): BelongsTo
        +assignedUser(): BelongsTo
        +createdByUser(): BelongsTo
        +isCompleted(): bool
        +isOverdue(): bool
        +getDaysUntilDue(): int
        +getPriorityColor(): string
        +getStatusColor(): string
    }

    %% Controller Classes
    class DashboardController {
        +index(): View
        +getProjectStats(): array
        +getUpcomingDeadlines(): Collection
        +getRecentActivities(): Collection
        +getNotifications(): array
        -calculateProjectMetrics(): array
        -getOverdueProjects(): Collection
        -getStalledProjects(): Collection
    }

    class ProjectController {
        +index(): View
        +create(): View
        +store(ProjectRequest): RedirectResponse
        +show(Project): View
        +edit(Project): View
        +update(ProjectRequest, Project): RedirectResponse
        +destroy(Project): RedirectResponse
        +complete(Project): RedirectResponse
        +updateProgress(Project, Request): JsonResponse
        -syncBudgetEntries(Project): void
        -updateProjectMetrics(Project): void
    }

    class WorkerController {
        +index(): View
        +create(): View
        +store(WorkerRequest): RedirectResponse
        +show(Worker): View
        +edit(Worker): View
        +update(WorkerRequest, Worker): RedirectResponse
        +destroy(Worker): RedirectResponse
        +assignToProject(Worker, Project): RedirectResponse
        +removeFromProject(Worker, Project): RedirectResponse
        -validateWorkerData(array): array
    }

    class TaskController {
        +index(Project): View
        +create(Project): View
        +store(TaskRequest, Project): RedirectResponse
        +show(Task): View
        +edit(Task): View
        +update(TaskRequest, Task): RedirectResponse
        +destroy(Task): RedirectResponse
        +complete(Task): RedirectResponse
        +updateStatus(Task, Request): JsonResponse
        -updateProjectProgress(Project): void
    }

    class MaterialController {
        +index(Project): View
        +create(Project): View
        +store(MaterialRequest, Project): RedirectResponse
        +show(Material): View
        +edit(Material): View
        +update(MaterialRequest, Material): RedirectResponse
        +destroy(Material): RedirectResponse
        +calculateCost(Material): JsonResponse
        -updateProjectMaterialBudget(Project): void
    }

    class BudgetController {
        +index(Project): View
        +create(Project): View
        +store(BudgetRequest, Project): RedirectResponse
        +show(Budget): View
        +edit(Budget): View
        +update(BudgetRequest, Budget): RedirectResponse
        +destroy(Budget): RedirectResponse
        +uploadInvoice(Budget, Request): RedirectResponse
        +downloadInvoice(Budget): Response
        +calculateProfit(Budget): JsonResponse
        -validateInvoiceFile(UploadedFile): bool
    }

    class DailyReportController {
        +index(Project): View
        +create(Project): View
        +store(DailyReportRequest, Project): RedirectResponse
        +show(DailyReport): View
        +edit(DailyReport): View
        +update(DailyReportRequest, DailyReport): RedirectResponse
        +destroy(DailyReport): RedirectResponse
        +approve(DailyReport): RedirectResponse
        -updateProjectProgressFromReport(Project, DailyReport): void
    }

    class DailyExpenseController {
        +index(Project): View
        +create(Project): View
        +store(DailyExpenseRequest, Project): RedirectResponse
        +show(DailyExpense): View
        +edit(DailyExpense): View
        +update(DailyExpenseRequest, DailyExpense): RedirectResponse
        +destroy(DailyExpense): RedirectResponse
        +getExpensesByCategory(Project): JsonResponse
        -validateExpenseAmount(decimal): bool
    }

    class CalendarEventController {
        +index(Project): View
        +create(Project): View
        +store(CalendarEventRequest, Project): RedirectResponse
        +show(CalendarEvent): View
        +edit(CalendarEvent): View
        +update(CalendarEventRequest, CalendarEvent): RedirectResponse
        +destroy(CalendarEvent): RedirectResponse
        +export(Project): Response
        +getEventsJson(Project): JsonResponse
        -generateCalendarData(Collection): array
    }

    %% Request Validation Classes
    class ProjectRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateDateRange(): bool
        -validateBudgetAmounts(): bool
    }

    class WorkerRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateEmailUniqueness(): bool
        -validateSkillsFormat(): bool
    }

    class TaskRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateDueDate(): bool
        -validatePriorityRange(): bool
    }

    class MaterialRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateCostAmount(): bool
        -validateQuantity(): bool
    }

    class BudgetRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateBudgetAmount(): bool
        -validateProfitCalculation(): bool
    }

    class DailyReportRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateReportDate(): bool
        -validateProgressPercentage(): bool
    }

    class DailyExpenseRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateExpenseDate(): bool
        -validateExpenseAmount(): bool
    }

    class CalendarEventRequest {
        +authorize(): bool
        +rules(): array
        +messages(): array
        -validateEventDates(): bool
        -validateEventDuration(): bool
    }
    %% Service Classes
    class ProjectService {
        +createProject(array): Project
        +updateProjectProgress(Project, int): void
        +calculateProjectMetrics(Project): array
        +syncBudgetEntries(Project): void
        +getProjectStatistics(): array
        +getOverdueProjects(): Collection
        +getStalledProjects(): Collection
        -validateProjectData(array): bool
        -updateRelatedEntities(Project): void
    }

    class BudgetService {
        +calculateProfitPercentage(decimal, decimal): decimal
        +generateBudgetReport(Project): array
        +compareBudgetVsActual(Project): array
        +uploadInvoiceFile(Budget, UploadedFile): string
        +validateBudgetConstraints(Budget): bool
        -calculateTotalExpenses(Project): decimal
        -generateInvoicePath(Budget): string
    }

    class ReportService {
        +generateProjectReport(Project, string): array
        +generateDashboardMetrics(): array
        +exportReportData(array, string): Response
        +createCustomReport(array): array
        +getAnalyticsData(Project): array
        -formatReportData(array): array
        -generateChartData(Collection): array
    }

    %% Middleware Classes
    class AuthMiddleware {
        +handle(Request, Closure): Response
        -checkAuthentication(Request): bool
        -redirectToLogin(): RedirectResponse
    }

    class ProjectAccessMiddleware {
        +handle(Request, Closure): Response
        -checkProjectAccess(User, Project): bool
        -handleUnauthorizedAccess(): Response
    }

    %% Relationships
    User ||--o{ DailyExpense : "creates"
    User ||--o{ Activity : "assigned_to"
    User ||--o{ Activity : "created_by"

    Project ||--o{ Task : "has many"
    Project ||--o{ Material : "has many"
    Project ||--o{ Budget : "has many"
    Project ||--o{ DailyReport : "has many"
    Project ||--o{ DailyExpense : "has many"
    Project ||--o{ CalendarEvent : "has many"
    Project ||--o{ Activity : "has many"
    Project }o--o{ Worker : "employs"

    %% Controller Dependencies
    DashboardController ..> Project : "uses"
    DashboardController ..> ProjectService : "uses"
    DashboardController ..> ReportService : "uses"

    ProjectController ..> Project : "manages"
    ProjectController ..> ProjectRequest : "validates"
    ProjectController ..> ProjectService : "uses"

    WorkerController ..> Worker : "manages"
    WorkerController ..> WorkerRequest : "validates"
    WorkerController ..> Project : "uses"

    TaskController ..> Task : "manages"
    TaskController ..> TaskRequest : "validates"
    TaskController ..> Project : "uses"

    MaterialController ..> Material : "manages"
    MaterialController ..> MaterialRequest : "validates"
    MaterialController ..> Project : "uses"

    BudgetController ..> Budget : "manages"
    BudgetController ..> BudgetRequest : "validates"
    BudgetController ..> BudgetService : "uses"

    DailyReportController ..> DailyReport : "manages"
    DailyReportController ..> DailyReportRequest : "validates"
    DailyReportController ..> Project : "uses"

    DailyExpenseController ..> DailyExpense : "manages"
    DailyExpenseController ..> DailyExpenseRequest : "validates"
    DailyExpenseController ..> Project : "uses"

    CalendarEventController ..> CalendarEvent : "manages"
    CalendarEventController ..> CalendarEventRequest : "validates"
    CalendarEventController ..> Project : "uses"

    %% Service Dependencies
    ProjectService ..> Project : "operates on"
    ProjectService ..> Budget : "creates"
    ProjectService ..> Task : "updates"

    BudgetService ..> Budget : "operates on"
    BudgetService ..> DailyExpense : "analyzes"
    BudgetService ..> Project : "updates"

    ReportService ..> Project : "analyzes"
    ReportService ..> Task : "reports on"
    ReportService ..> Budget : "reports on"
    ReportService ..> DailyReport : "aggregates"
```

## 3. Penjelasan Detail Kelas

### 3.1 Model Classes (Domain Layer)

#### User Class
**Deskripsi**: Representasi pengguna sistem (Project Manager)
**Tanggung Jawab**:
- Menyimpan informasi autentikasi dan profil pengguna
- Mengelola relasi dengan daily expenses dan activities
- Menyediakan method untuk validasi email dan status verifikasi

**Key Methods**:
- `dailyExpenses()`: Relasi one-to-many dengan DailyExpense
- `assignedActivities()`: Activities yang ditugaskan ke user
- `createdActivities()`: Activities yang dibuat oleh user
- `hasVerifiedEmail()`: Cek status verifikasi email

#### Project Class
**Deskripsi**: Entitas utama yang merepresentasikan proyek konstruksi
**Tanggung Jawab**:
- Mengelola informasi proyek (nama, status, timeline, budget)
- Mengkoordinasi semua entitas terkait proyek
- Menyediakan computed properties untuk analytics dan reporting

**Key Methods**:
- `isCompleted()`: Cek apakah proyek sudah selesai
- `isOverdue()`: Cek apakah proyek melewati deadline
- `getTotalBudget()`: Hitung total anggaran material + jasa
- `getRemainingDays()`: Hitung sisa hari hingga deadline
- `getStatusColor()`: Warna untuk status display
- `getPriorityColor()`: Warna untuk priority display

**Relationships**:
- One-to-Many: Tasks, Materials, Budgets, Daily Reports, Daily Expenses, Calendar Events, Activities
- Many-to-Many: Workers (via pivot table)

#### Worker Class
**Deskripsi**: Representasi pekerja konstruksi
**Tanggung Jawab**:
- Menyimpan informasi pekerja (nama, spesialisasi, kontak)
- Mengelola skills dan experience tracking
- Mengatur assignment ke multiple projects

**Key Methods**:
- `isActive()`: Cek status aktif pekerja (end_date null)
- `getSkillsArray()`: Parse skills text menjadi array
- `getExperience()`: Hitung pengalaman berdasarkan join_date
- `getProjectCount()`: Jumlah proyek yang pernah dikerjakan

#### Task Class
**Deskripsi**: Representasi tugas dalam proyek
**Tanggung Jawab**:
- Mengelola informasi tugas (nama, deskripsi, deadline, status)
- Tracking completion dan priority
- Integration dengan project progress

**Key Methods**:
- `isCompleted()`: Cek status completion
- `isOverdue()`: Cek apakah melewati due date
- `markAsCompleted()`: Set status completed dan update flag
- `getDaysUntilDue()`: Hitung hari hingga deadline
- `getStatusColor()`: Warna untuk status display

#### Material Class
**Deskripsi**: Representasi material proyek
**Tanggung Jawab**:
- Mengelola informasi material (nama, cost, quantity, unit)
- Kalkulasi total cost otomatis
- Tracking purchase date dan usage

**Key Methods**:
- `getTotalCost()`: Hitung cost × quantity
- `getFormattedCost()`: Format currency display
- `isExpensive()`: Cek apakah material mahal (threshold-based)
- `getCostPerUnit()`: Hitung cost per unit

#### Budget Class
**Deskripsi**: Representasi anggaran proyek
**Tanggung Jawab**:
- Mengelola informasi anggaran (amount, profit, status)
- Kalkulasi profit percentage otomatis
- File management untuk invoice

**Key Methods**:
- `calculateProfitPercentage()`: Hitung (estimated_profit / amount) × 100
- `hasInvoice()`: Cek apakah ada file invoice
- `getInvoiceUrl()`: Generate URL untuk download invoice
- `isApproved()`: Cek status approval
- `getStatusColor()`: Warna untuk status display

#### DailyReport Class
**Deskripsi**: Representasi laporan harian proyek
**Tanggung Jawab**:
- Menyimpan progress harian (activities, challenges, next plan)
- Tracking progress percentage
- Status approval workflow

**Key Methods**:
- `getFormattedDate()`: Format tanggal untuk display
- `isApproved()`: Cek status approval
- `getProgressColor()`: Warna berdasarkan progress percentage
- `hasActivities()`: Cek apakah ada activities yang dicatat
- `hasChallenges()`: Cek apakah ada challenges yang dicatat

#### DailyExpense Class
**Deskripsi**: Representasi pengeluaran harian proyek
**Tanggung Jawab**:
- Mencatat pengeluaran harian dengan kategori
- Tracking user yang mencatat dan tanggal
- Integration dengan budget analysis

**Key Methods**:
- `getFormattedAmount()`: Format currency display
- `getFormattedDate()`: Format tanggal untuk display
- `getCategoryColor()`: Warna berdasarkan kategori
- `isExpensive()`: Cek apakah pengeluaran besar

#### CalendarEvent Class
**Deskripsi**: Representasi event dalam kalender proyek
**Tanggung Jawab**:
- Mengelola event dan milestone proyek
- Tracking tanggal, durasi, dan lokasi
- Integration dengan project timeline

**Key Methods**:
- `isUpcoming()`: Cek apakah event akan datang
- `isToday()`: Cek apakah event hari ini
- `getDuration()`: Hitung durasi event dalam jam
- `hasLocation()`: Cek apakah ada lokasi yang diset

#### Activity Class
**Deskripsi**: Representasi aktivitas proyek (future enhancement)
**Tanggung Jawab**:
- Mengelola aktivitas dengan assignment ke user
- Tracking status, priority, dan deadline
- Workflow management

**Key Methods**:
- `isCompleted()`: Cek status completion
- `isOverdue()`: Cek apakah melewati deadline
- `getDaysUntilDue()`: Hitung hari hingga deadline
- `getPriorityColor()`: Warna berdasarkan priority
- `getStatusColor()`: Warna berdasarkan status

### 3.2 Controller Classes (Application Layer)

#### DashboardController
**Deskripsi**: Mengelola dashboard utama dan analytics
**Tanggung Jawab**:
- Aggregasi data untuk dashboard metrics
- Kalkulasi project statistics
- Notification management
- Performance analytics

**Key Methods**:
- `index()`: Render dashboard view dengan metrics
- `getProjectStats()`: Statistik proyek (total, completed, in progress)
- `getUpcomingDeadlines()`: Proyek dengan deadline mendekat
- `getNotifications()`: Notifikasi urgent dan alerts

#### ProjectController
**Deskripsi**: CRUD operations untuk Project entity
**Tanggung Jawab**:
- Mengelola lifecycle proyek dari create hingga completion
- Integration dengan budget sync
- Progress tracking dan updates
- Project metrics calculation

**Key Methods**:
- Standard CRUD: `index()`, `create()`, `store()`, `show()`, `edit()`, `update()`, `destroy()`
- `complete()`: Mark proyek sebagai completed
- `updateProgress()`: AJAX endpoint untuk update progress
- `syncBudgetEntries()`: Sync budget saat project dibuat

#### WorkerController
**Deskripsi**: CRUD operations untuk Worker entity
**Tanggung Jawab**:
- Mengelola data pekerja dan skills
- Assignment/removal dari proyek
- Worker performance tracking

**Key Methods**:
- Standard CRUD operations
- `assignToProject()`: Assign worker ke proyek
- `removeFromProject()`: Remove assignment
- `validateWorkerData()`: Custom validation logic

### 3.3 Request Validation Classes

#### ProjectRequest
**Deskripsi**: Validasi input untuk Project operations
**Validation Rules**:
- Name: required, max 255 characters
- Start/End dates: required, end >= start
- Budget amounts: numeric, min 0
- Status: in allowed values
- Priority: in allowed values

#### WorkerRequest
**Deskripsi**: Validasi input untuk Worker operations
**Validation Rules**:
- Name: required, max 255 characters
- Email: email format, unique
- Specialization: required
- Skills: text format validation

### 3.4 Service Classes (Business Logic Layer)

#### ProjectService
**Deskripsi**: Business logic untuk Project operations
**Tanggung Jawab**:
- Complex project creation dengan budget sync
- Progress calculation dan metrics
- Project analytics dan reporting
- Cross-entity updates

#### BudgetService
**Deskripsi**: Business logic untuk Budget operations
**Tanggung Jawab**:
- Profit percentage calculations
- Budget vs actual analysis
- Invoice file management
- Financial reporting

#### ReportService
**Deskripsi**: Reporting dan analytics service
**Tanggung Jawab**:
- Generate berbagai jenis report
- Data aggregation dan analysis
- Export functionality
- Dashboard metrics calculation

### 3.5 Middleware Classes (Security Layer)

#### AuthMiddleware
**Deskripsi**: Authentication middleware
**Tanggung Jawab**:
- Verify user authentication
- Session validation
- Redirect unauthenticated users

#### ProjectAccessMiddleware
**Deskripsi**: Project-level access control
**Tanggung Jawab**:
- Verify user access to specific projects
- Resource-level authorization
- Handle unauthorized access attempts
## 4. Relasi dan Interaksi Antar Kelas

### 4.1 Domain Model Relationships

#### One-to-Many Relationships
1. **Project → Task**: Satu proyek memiliki banyak tugas
   - Foreign Key: `tasks.project_id`
   - Cascade Delete: Ya
   - Business Rule: Task tidak bisa exist tanpa project

2. **Project → Material**: Satu proyek memiliki banyak material
   - Foreign Key: `materials.project_id`
   - Cascade Delete: Ya
   - Business Rule: Material cost terakumulasi ke project budget

3. **Project → Budget**: Satu proyek memiliki banyak budget entries
   - Foreign Key: `budgets.project_id`
   - Cascade Delete: Ya
   - Business Rule: Budget otomatis dibuat saat project creation

4. **Project → DailyReport**: Satu proyek memiliki banyak laporan harian
   - Foreign Key: `daily_reports.project_id`
   - Cascade Delete: Ya
   - Business Rule: Satu report per hari per project (unique constraint)

5. **Project → DailyExpense**: Satu proyek memiliki banyak pengeluaran harian
   - Foreign Key: `daily_expenses.project_id`
   - Cascade Delete: Ya
   - Business Rule: Expense terakumulasi untuk budget analysis

6. **User → DailyExpense**: Satu user dapat mencatat banyak pengeluaran
   - Foreign Key: `daily_expenses.user_id`
   - Cascade Delete: Ya
   - Business Rule: Tracking siapa yang mencatat expense

#### Many-to-Many Relationships
1. **Project ↔ Worker**: Banyak proyek dapat memiliki banyak pekerja
   - Pivot Table: `project_worker`
   - Business Rule: Worker dapat ditugaskan ke multiple projects
   - Timestamps: Tracking assignment date

### 4.2 Controller-Model Interactions

#### Standard CRUD Pattern
Semua controller mengikuti pattern yang konsisten:
```
Controller → Request Validation → Model Operation → Response
```

**Flow Example (ProjectController)**:
1. `ProjectController::store(ProjectRequest $request)`
2. `ProjectRequest` validates input data
3. `Project::create()` creates new project
4. `ProjectService::syncBudgetEntries()` creates related budgets
5. Return redirect with success message

#### Service Layer Integration
Controllers menggunakan service classes untuk complex business logic:
- **ProjectController** → **ProjectService** untuk project creation dan metrics
- **BudgetController** → **BudgetService** untuk profit calculations
- **DashboardController** → **ReportService** untuk analytics

### 4.3 Data Flow Patterns

#### Create Project Flow
```
User Input → ProjectRequest → ProjectController → ProjectService → Project Model → Database
                                                      ↓
                                              BudgetService → Budget Model → Database
```

#### Dashboard Analytics Flow
```
DashboardController → ProjectService → Project Model → Database
                   → ReportService → Multiple Models → Aggregated Data → View
```

#### Daily Operations Flow
```
User Input → DailyExpenseRequest → DailyExpenseController → DailyExpense Model → Database
                                                              ↓
                                                    BudgetService (for analysis)
```

## 5. Design Patterns yang Diimplementasikan

### 5.1 MVC (Model-View-Controller) Pattern
**Implementasi**:
- **Model**: Eloquent models untuk data dan business logic
- **View**: Blade templates untuk presentation
- **Controller**: HTTP controllers untuk request handling

**Keuntungan**:
- Separation of concerns yang jelas
- Maintainability yang tinggi
- Testability yang baik

### 5.2 Repository Pattern (via Eloquent ORM)
**Implementasi**:
- Eloquent models sebagai repositories
- Relationship methods untuk data access
- Query scopes untuk reusable queries

**Keuntungan**:
- Abstraksi database access
- Consistent data access patterns
- Easy testing dengan model factories

### 5.3 Service Layer Pattern
**Implementasi**:
- `ProjectService` untuk complex project operations
- `BudgetService` untuk financial calculations
- `ReportService` untuk analytics dan reporting

**Keuntungan**:
- Business logic centralization
- Reusable across controllers
- Single responsibility principle

### 5.4 Request-Response Pattern
**Implementasi**:
- Form Request classes untuk validation
- Consistent validation rules
- Custom error messages

**Keuntungan**:
- Input validation centralization
- Consistent error handling
- Security best practices

### 5.5 Observer Pattern (Laravel Events)
**Implementasi**:
- Model events untuk automatic actions
- Budget sync saat project creation
- Progress update saat task completion

**Keuntungan**:
- Loose coupling
- Automatic side effects
- Event-driven architecture

## 6. Class Interaction Scenarios

### 6.1 Scenario: Create New Project
**Participating Classes**:
- `ProjectController`, `ProjectRequest`, `ProjectService`, `Project`, `BudgetService`, `Budget`

**Interaction Flow**:
1. User submits project form
2. `ProjectRequest` validates input
3. `ProjectController::store()` receives validated data
4. `ProjectService::createProject()` creates project
5. `ProjectService::syncBudgetEntries()` creates budget entries
6. `BudgetService::calculateProfitPercentage()` calculates profits
7. Response redirects to project detail

### 6.2 Scenario: Dashboard Analytics
**Participating Classes**:
- `DashboardController`, `ProjectService`, `ReportService`, `Project`, `Task`, `Budget`

**Interaction Flow**:
1. User accesses dashboard
2. `DashboardController::index()` loads dashboard
3. `ProjectService::getProjectStatistics()` calculates metrics
4. `ReportService::generateDashboardMetrics()` aggregates data
5. Multiple model queries for different metrics
6. View renders with aggregated data

### 6.3 Scenario: Daily Expense Recording
**Participating Classes**:
- `DailyExpenseController`, `DailyExpenseRequest`, `DailyExpense`, `Project`, `User`

**Interaction Flow**:
1. User records daily expense
2. `DailyExpenseRequest` validates input
3. `DailyExpenseController::store()` creates expense
4. `DailyExpense` model saves to database
5. Relationships update project and user associations
6. Response confirms successful recording

## 7. Performance Considerations

### 7.1 Eager Loading Strategy
**Implementation**:
- Controller methods use `with()` untuk eager loading
- Menghindari N+1 query problems
- Optimized relationship loading

**Example**:
```php
Project::with(['tasks', 'materials', 'budgets'])->get()
```

### 7.2 Caching Strategy
**Implementation**:
- Dashboard metrics caching
- Expensive calculations caching
- Query result caching

### 7.3 Database Optimization
**Implementation**:
- Proper indexing pada foreign keys
- Composite indexes untuk complex queries
- Query optimization dalam service classes

## 8. Security Considerations

### 8.1 Input Validation
**Implementation**:
- Form Request classes untuk semua input
- Server-side validation rules
- Custom validation methods

### 8.2 Authorization
**Implementation**:
- Middleware untuk authentication
- Resource-level authorization
- CSRF protection

### 8.3 Data Protection
**Implementation**:
- Password hashing dalam User model
- File upload validation
- SQL injection prevention via ORM

## 9. Extensibility dan Maintainability

### 9.1 Adding New Features
**Strategy**:
- Follow existing patterns (Controller → Request → Service → Model)
- Use service classes untuk complex logic
- Maintain consistent naming conventions

### 9.2 Modifying Existing Features
**Strategy**:
- Service layer modifications untuk business logic changes
- Model method updates untuk data access changes
- Controller updates untuk new endpoints

### 9.3 Testing Strategy
**Implementation**:
- Unit tests untuk model methods
- Feature tests untuk controller actions
- Service class testing untuk business logic

## 10. Future Enhancements

### 10.1 API Layer Addition
**Planned Classes**:
- `ApiController` base class
- `ProjectApiController`, `WorkerApiController`, etc.
- `ApiResource` classes untuk response formatting

### 10.2 Advanced Reporting
**Planned Classes**:
- `AdvancedReportService`
- `ChartService` untuk data visualization
- `ExportService` untuk multiple format exports

### 10.3 Notification System
**Planned Classes**:
- `NotificationService`
- `EmailNotification`, `PushNotification` classes
- `NotificationController` untuk management

---

Class Diagram DisaCloud05-v4 ini menggambarkan arsitektur yang solid dan scalable untuk sistem manajemen proyek konstruksi. Dengan separation of concerns yang jelas, design patterns yang tepat, dan struktur yang konsisten, sistem ini siap untuk pengembangan dan maintenance jangka panjang. Setiap kelas memiliki tanggung jawab yang spesifik dan berinteraksi dengan kelas lain melalui interface yang well-defined, memastikan maintainability dan extensibility yang optimal.
