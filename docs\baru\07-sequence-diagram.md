# Sequence Diagram DisaCloud05-v4

## 1. Overview Sequence Diagram

Sequence Diagram DisaCloud05-v4 menggambarkan interaksi antar objek dalam setiap aktivitas yang terjadi di activity diagram. Diagram ini menunjukkan urutan pesan yang dikirim antar objek yang relevan dengan class diagram, men<PERSON>kup User, Controller, Service, Model, dan Database dalam timeline yang berurutan untuk setiap fungsionalitas aplikasi.

## 2. Sequence Diagram: User Authentication

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant Auth<PERSON>ontroller as AuthController
    participant AuthMiddleware as AuthMiddleware
    participant UserModel as User Model
    participant Database as MySQL Database
    participant Session as Session Storage

    %% Login Process
    Note over User, Session: User Login Process
    User->>Browser: Access Application URL
    Browser->>AuthMiddleware: HTTP Request
    AuthMiddleware->>Session: Check Authentication
    Session-->>AuthMiddleware: Not Authenticated
    AuthMiddleware->>Browser: Redirect to Login Page
    Browser-->>User: Display Login Form

    User->>Browser: Enter Credentials (email, password)
    Browser->>AuthController: POST /login
    AuthController->>AuthController: Validate Input
    AuthController->>UserModel: findByEmail(email)
    UserModel->>Database: SELECT * FROM users WHERE email = ?
    Database-->>UserModel: User Record
    UserModel-->>AuthController: User Object

    AuthController->>AuthController: verifyPassword(password, hashedPassword)
    alt Password Valid
        AuthController->>Session: createSession(user)
        Session-->>AuthController: Session Created
        AuthController->>Browser: Redirect to Dashboard
        Browser-->>User: Dashboard Page
    else Password Invalid
        AuthController->>Browser: Redirect with Error
        Browser-->>User: Login Form with Error Message
    end

    %% Registration Process
    Note over User, Session: User Registration Process
    User->>Browser: Click Register Link
    Browser->>AuthController: GET /register
    AuthController->>Browser: Registration Form
    Browser-->>User: Display Registration Form

    User->>Browser: Fill Registration Data
    Browser->>AuthController: POST /register
    AuthController->>AuthController: Validate Registration Data
    AuthController->>UserModel: create(userData)
    UserModel->>Database: INSERT INTO users (name, email, password)
    Database-->>UserModel: User Created
    UserModel-->>AuthController: New User Object
    AuthController->>Browser: Redirect to Login with Success
    Browser-->>User: Login Form with Success Message

    %% Logout Process
    Note over User, Session: User Logout Process
    User->>Browser: Click Logout
    Browser->>AuthController: POST /logout
    AuthController->>Session: destroySession()
    Session-->>AuthController: Session Destroyed
    AuthController->>Browser: Redirect to Landing Page
    Browser-->>User: Landing Page
```

**Penjelasan Sequence Diagram User Authentication**:

**Objek yang Terlibat**:
- **Project Manager**: User yang menggunakan sistem
- **Web Browser**: Interface client-side
- **AuthController**: Controller untuk handling authentication
- **AuthMiddleware**: Middleware untuk checking authentication status
- **User Model**: Model untuk user data operations
- **MySQL Database**: Database storage
- **Session Storage**: Session management

**Alur Interaksi**:
1. **Login Flow**: User access → Authentication check → Credential validation → Session creation
2. **Registration Flow**: Registration form → Data validation → User creation → Success redirect
3. **Logout Flow**: Logout request → Session destruction → Landing page redirect

**Key Messages**:
- `findByEmail()`: Query user by email
- `verifyPassword()`: Validate password hash
- `createSession()`: Create user session
- `destroySession()`: Destroy user session

## 3. Sequence Diagram: Dashboard Loading

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant DashboardController as DashboardController
    participant ProjectService as ProjectService
    participant ReportService as ReportService
    participant ProjectModel as Project Model
    participant TaskModel as Task Model
    participant BudgetModel as Budget Model
    participant Database as MySQL Database

    Note over User, Database: Dashboard Data Loading Process
    User->>Browser: Access Dashboard
    Browser->>DashboardController: GET /dashboard
    DashboardController->>DashboardController: checkAuthentication()

    %% Parallel Data Loading
    par Project Statistics
        DashboardController->>ProjectService: getProjectStatistics()
        ProjectService->>ProjectModel: count() by status
        ProjectModel->>Database: SELECT COUNT(*) FROM projects GROUP BY status
        Database-->>ProjectModel: Project Counts
        ProjectModel-->>ProjectService: Statistics Data
        ProjectService-->>DashboardController: Project Stats
    and Upcoming Deadlines
        DashboardController->>ProjectService: getUpcomingDeadlines()
        ProjectService->>ProjectModel: whereDate('end_date', '<=', now()+3days)
        ProjectModel->>Database: SELECT * FROM projects WHERE end_date <= DATE_ADD(NOW(), INTERVAL 3 DAY)
        Database-->>ProjectModel: Upcoming Projects
        ProjectModel-->>ProjectService: Deadline Data
        ProjectService-->>DashboardController: Upcoming Deadlines
    and Recent Activities
        DashboardController->>ReportService: getRecentActivities()
        ReportService->>TaskModel: latest()->limit(10)
        TaskModel->>Database: SELECT * FROM tasks ORDER BY updated_at DESC LIMIT 10
        Database-->>TaskModel: Recent Tasks
        TaskModel-->>ReportService: Activity Data
        ReportService-->>DashboardController: Recent Activities
    and Budget Analysis
        DashboardController->>ProjectService: getBudgetAnalysis()
        ProjectService->>BudgetModel: sum('amount') by project
        BudgetModel->>Database: SELECT project_id, SUM(amount) FROM budgets GROUP BY project_id
        Database-->>BudgetModel: Budget Totals
        BudgetModel-->>ProjectService: Budget Data
        ProjectService-->>DashboardController: Budget Analysis
    end

    DashboardController->>DashboardController: aggregateData()
    DashboardController->>DashboardController: generateNotifications()
    DashboardController->>Browser: Dashboard View with Data
    Browser-->>User: Display Dashboard
```

**Penjelasan Sequence Diagram Dashboard Loading**:

**Objek yang Terlibat**:
- **DashboardController**: Main controller untuk dashboard
- **ProjectService**: Service untuk project-related operations
- **ReportService**: Service untuk reporting operations
- **Project Model, Task Model, Budget Model**: Data models
- **MySQL Database**: Data storage

**Parallel Processing**:
- Multiple data queries dijalankan secara bersamaan untuk performance
- Each service handles specific domain logic
- Data aggregation dilakukan setelah semua data ready

**Key Messages**:
- `getProjectStatistics()`: Ambil statistik proyek
- `getUpcomingDeadlines()`: Ambil deadline yang mendekat
- `getRecentActivities()`: Ambil aktivitas terbaru
- `getBudgetAnalysis()`: Analisis budget

## 4. Sequence Diagram: Project Creation

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant ProjectController as ProjectController
    participant ProjectRequest as ProjectRequest
    participant ProjectService as ProjectService
    participant BudgetService as BudgetService
    participant ProjectModel as Project Model
    participant BudgetModel as Budget Model
    participant Database as MySQL Database

    Note over User, Database: Project Creation Process
    User->>Browser: Click Create Project
    Browser->>ProjectController: GET /projects/create
    ProjectController->>Browser: Create Project Form
    Browser-->>User: Display Form

    User->>Browser: Fill Project Data & Submit
    Browser->>ProjectController: POST /projects
    ProjectController->>ProjectRequest: validate(requestData)
    ProjectRequest->>ProjectRequest: validateDateRange()
    ProjectRequest->>ProjectRequest: validateBudgetAmounts()

    alt Validation Successful
        ProjectRequest-->>ProjectController: Validation Passed
        ProjectController->>ProjectService: createProject(validatedData)

        %% Create Project
        ProjectService->>ProjectModel: create(projectData)
        ProjectModel->>Database: INSERT INTO projects (name, description, start_date, end_date, budget_material, budget_jasa)
        Database-->>ProjectModel: Project Created (ID: 1)
        ProjectModel-->>ProjectService: Project Object

        %% Auto-create Budget Entries
        ProjectService->>BudgetService: syncBudgetEntries(project)
        BudgetService->>BudgetModel: create(materialBudget)
        BudgetModel->>Database: INSERT INTO budgets (project_id, amount, type) VALUES (1, budget_material, 'material')
        Database-->>BudgetModel: Material Budget Created

        BudgetService->>BudgetModel: create(jasaBudget)
        BudgetModel->>Database: INSERT INTO budgets (project_id, amount, type) VALUES (1, budget_jasa, 'jasa')
        Database-->>BudgetModel: Jasa Budget Created

        BudgetModel-->>BudgetService: Budget Entries Created
        BudgetService-->>ProjectService: Budget Sync Complete

        ProjectService->>ProjectService: updateProjectMetrics(project)
        ProjectService-->>ProjectController: Project Created Successfully
        ProjectController->>Browser: Redirect to Projects List with Success
        Browser-->>User: Projects List with Success Message

    else Validation Failed
        ProjectRequest-->>ProjectController: Validation Errors
        ProjectController->>Browser: Create Form with Errors
        Browser-->>User: Form with Validation Messages
    end
```

**Penjelasan Sequence Diagram Project Creation**:

**Objek yang Terlibat**:
- **ProjectController**: Main controller untuk project operations
- **ProjectRequest**: Form request validation
- **ProjectService**: Business logic untuk project creation
- **BudgetService**: Service untuk budget operations
- **Project Model, Budget Model**: Data models

**Business Logic Flow**:
1. **Form Display**: Show create project form
2. **Data Validation**: Validate input data dengan business rules
3. **Project Creation**: Create project record dalam database
4. **Budget Sync**: Auto-create budget entries berdasarkan project budget
5. **Metrics Update**: Update project metrics
6. **Success Response**: Redirect dengan success message

**Key Messages**:
- `validate()`: Validate form input
- `createProject()`: Create new project
- `syncBudgetEntries()`: Auto-create budget entries
- `updateProjectMetrics()`: Update project calculations

## 5. Sequence Diagram: Task Management

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant TaskController as TaskController
    participant TaskRequest as TaskRequest
    participant ProjectService as ProjectService
    participant TaskModel as Task Model
    participant ProjectModel as Project Model
    participant Database as MySQL Database

    Note over User, Database: Task Creation and Completion Process

    %% Task Creation
    User->>Browser: Select Project & Click Create Task
    Browser->>TaskController: GET /projects/1/tasks/create
    TaskController->>Browser: Create Task Form
    Browser-->>User: Display Task Form

    User->>Browser: Fill Task Data & Submit
    Browser->>TaskController: POST /projects/1/tasks
    TaskController->>TaskRequest: validate(requestData)
    TaskRequest->>TaskRequest: validateDueDate()
    TaskRequest->>TaskRequest: validatePriorityRange()

    alt Validation Successful
        TaskRequest-->>TaskController: Validation Passed
        TaskController->>TaskModel: create(taskData)
        TaskModel->>Database: INSERT INTO tasks (project_id, name, description, due_date, priority)
        Database-->>TaskModel: Task Created (ID: 1)
        TaskModel-->>TaskController: Task Object
        TaskController->>Browser: Redirect to Task List with Success
        Browser-->>User: Task List with Success Message
    else Validation Failed
        TaskRequest-->>TaskController: Validation Errors
        TaskController->>Browser: Create Form with Errors
        Browser-->>User: Form with Validation Messages
    end

    %% Task Completion
    Note over User, Database: Task Completion Process
    User->>Browser: Click Complete Task
    Browser->>TaskController: POST /tasks/1/complete
    TaskController->>TaskModel: findOrFail(1)
    TaskModel->>Database: SELECT * FROM tasks WHERE id = 1
    Database-->>TaskModel: Task Record
    TaskModel-->>TaskController: Task Object

    TaskController->>TaskModel: markAsCompleted()
    TaskModel->>TaskModel: status = 'completed'
    TaskModel->>TaskModel: completed = true
    TaskModel->>Database: UPDATE tasks SET status = 'completed', completed = true WHERE id = 1
    Database-->>TaskModel: Task Updated

    %% Update Project Progress
    TaskController->>ProjectService: updateProjectProgress(project)
    ProjectService->>TaskModel: where('project_id', projectId)->count()
    TaskModel->>Database: SELECT COUNT(*) FROM tasks WHERE project_id = 1
    Database-->>TaskModel: Total Tasks Count

    ProjectService->>TaskModel: where('project_id', projectId)->where('completed', true)->count()
    TaskModel->>Database: SELECT COUNT(*) FROM tasks WHERE project_id = 1 AND completed = true
    Database-->>TaskModel: Completed Tasks Count

    TaskModel-->>ProjectService: Task Counts
    ProjectService->>ProjectService: calculateProgress(completedTasks, totalTasks)
    ProjectService->>ProjectModel: update(['progress' => calculatedProgress])
    ProjectModel->>Database: UPDATE projects SET progress = ? WHERE id = 1
    Database-->>ProjectModel: Project Updated

    ProjectModel-->>ProjectService: Project Progress Updated
    ProjectService-->>TaskController: Progress Update Complete
    TaskController->>Browser: Redirect with Success
    Browser-->>User: Task List with Completion Success
```

**Penjelasan Sequence Diagram Task Management**:

**Objek yang Terlibat**:
- **TaskController**: Controller untuk task operations
- **TaskRequest**: Validation untuk task input
- **ProjectService**: Service untuk project-related calculations
- **Task Model, Project Model**: Data models

**Business Logic Flow**:
1. **Task Creation**: Form validation → Task creation → Success response
2. **Task Completion**: Mark task completed → Calculate project progress → Update project

**Automatic Progress Calculation**:
- System otomatis menghitung progress project berdasarkan completed tasks
- Progress = (completed tasks / total tasks) × 100
- Project progress diupdate secara real-time

**Key Messages**:
- `markAsCompleted()`: Mark task sebagai completed
- `updateProjectProgress()`: Update progress project
- `calculateProgress()`: Hitung persentase progress
## 6. Sequence Diagram: Budget Management with Invoice

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant BudgetController as BudgetController
    participant BudgetRequest as BudgetRequest
    participant BudgetService as BudgetService
    participant BudgetModel as Budget Model
    participant FileSystem as File Storage
    participant Database as MySQL Database

    Note over User, Database: Budget Creation with Profit Calculation

    %% Budget Creation
    User->>Browser: Select Project & Click Create Budget
    Browser->>BudgetController: GET /projects/1/budgets/create
    BudgetController->>Browser: Create Budget Form
    Browser-->>User: Display Budget Form

    User->>Browser: Fill Budget Data (amount, estimated_profit) & Submit
    Browser->>BudgetController: POST /projects/1/budgets
    BudgetController->>BudgetRequest: validate(requestData)
    BudgetRequest->>BudgetRequest: validateBudgetAmount()
    BudgetRequest->>BudgetRequest: validateProfitCalculation()

    alt Validation Successful
        BudgetRequest-->>BudgetController: Validation Passed
        BudgetController->>BudgetService: createBudget(validatedData)
        BudgetService->>BudgetService: calculateProfitPercentage(amount, estimatedProfit)
        BudgetService->>BudgetModel: create(budgetDataWithProfit)
        BudgetModel->>Database: INSERT INTO budgets (project_id, amount, estimated_profit, profit_percentage)
        Database-->>BudgetModel: Budget Created (ID: 1)
        BudgetModel-->>BudgetService: Budget Object
        BudgetService-->>BudgetController: Budget Created Successfully
        BudgetController->>Browser: Redirect to Budget List with Success
        Browser-->>User: Budget List with Success Message
    else Validation Failed
        BudgetRequest-->>BudgetController: Validation Errors
        BudgetController->>Browser: Create Form with Errors
        Browser-->>User: Form with Validation Messages
    end

    %% Invoice Upload Process
    Note over User, Database: Invoice File Upload Process
    User->>Browser: Click Upload Invoice for Budget
    Browser->>BudgetController: GET /budgets/1/upload-invoice
    BudgetController->>Browser: File Upload Form
    Browser-->>User: Display Upload Form

    User->>Browser: Select Invoice File & Submit
    Browser->>BudgetController: POST /budgets/1/upload-invoice (with file)
    BudgetController->>BudgetController: validateInvoiceFile(uploadedFile)

    alt File Valid
        BudgetController->>BudgetService: uploadInvoiceFile(budget, file)
        BudgetService->>BudgetService: generateInvoicePath(budget)
        BudgetService->>FileSystem: store(file, invoicePath)
        FileSystem-->>BudgetService: File Stored Successfully
        BudgetService->>BudgetModel: update(['invoice_file' => filePath])
        BudgetModel->>Database: UPDATE budgets SET invoice_file = ? WHERE id = 1
        Database-->>BudgetModel: Budget Updated
        BudgetModel-->>BudgetService: Invoice File Updated
        BudgetService-->>BudgetController: Upload Successful
        BudgetController->>Browser: Redirect with Upload Success
        Browser-->>User: Budget List with Upload Success Message
    else File Invalid
        BudgetController->>Browser: Upload Form with File Error
        Browser-->>User: Form with File Validation Error
    end

    %% Budget vs Actual Comparison
    Note over User, Database: Budget vs Actual Expense Comparison
    User->>Browser: Click Compare Budget vs Actual
    Browser->>BudgetController: GET /projects/1/budget-comparison
    BudgetController->>BudgetService: compareBudgetVsActual(project)

    par Budget Calculation
        BudgetService->>BudgetModel: where('project_id', 1)->sum('amount')
        BudgetModel->>Database: SELECT SUM(amount) FROM budgets WHERE project_id = 1
        Database-->>BudgetModel: Total Budget Amount
        BudgetModel-->>BudgetService: Budget Total
    and Expense Calculation
        BudgetService->>BudgetService: calculateTotalExpenses(project)
        BudgetService->>Database: SELECT SUM(amount) FROM daily_expenses WHERE project_id = 1
        Database-->>BudgetService: Total Expenses
    end

    BudgetService->>BudgetService: generateVarianceAnalysis(budgetTotal, expenseTotal)
    BudgetService-->>BudgetController: Comparison Data
    BudgetController->>Browser: Budget Comparison View
    Browser-->>User: Display Budget vs Actual Analysis
```

**Penjelasan Sequence Diagram Budget Management**:

**Objek yang Terlibat**:
- **BudgetController**: Controller untuk budget operations
- **BudgetRequest**: Validation untuk budget input
- **BudgetService**: Business logic untuk budget calculations
- **Budget Model**: Data model untuk budget
- **File Storage**: System untuk file management

**Business Logic Flow**:
1. **Budget Creation**: Form validation → Profit calculation → Budget creation
2. **Invoice Upload**: File validation → File storage → Database update
3. **Budget Comparison**: Parallel calculation → Variance analysis → Report display

**Key Features**:
- **Automatic Profit Calculation**: System menghitung profit percentage otomatis
- **File Management**: Secure file upload dan storage untuk invoice
- **Variance Analysis**: Comparison antara budget dan actual expenses

**Key Messages**:
- `calculateProfitPercentage()`: Hitung profit percentage
- `uploadInvoiceFile()`: Upload dan store invoice file
- `compareBudgetVsActual()`: Compare budget dengan actual expenses

## 7. Sequence Diagram: Daily Operations (Expense & Report)

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant DailyExpenseController as DailyExpenseController
    participant DailyReportController as DailyReportController
    participant DailyExpenseRequest as DailyExpenseRequest
    participant DailyReportRequest as DailyReportRequest
    participant DailyExpenseModel as DailyExpense Model
    participant DailyReportModel as DailyReport Model
    participant ProjectService as ProjectService
    participant Database as MySQL Database

    Note over User, Database: Daily Expense Recording Process

    %% Daily Expense Recording
    User->>Browser: Select Project & Click Record Expense
    Browser->>DailyExpenseController: GET /projects/1/daily-expenses/create
    DailyExpenseController->>Browser: Record Expense Form
    Browser-->>User: Display Expense Form

    User->>Browser: Fill Expense Data (date, category, amount, description) & Submit
    Browser->>DailyExpenseController: POST /projects/1/daily-expenses
    DailyExpenseController->>DailyExpenseRequest: validate(requestData)
    DailyExpenseRequest->>DailyExpenseRequest: validateExpenseDate()
    DailyExpenseRequest->>DailyExpenseRequest: validateExpenseAmount()

    alt Validation Successful
        DailyExpenseRequest-->>DailyExpenseController: Validation Passed
        DailyExpenseController->>DailyExpenseModel: create(expenseData)
        DailyExpenseModel->>Database: INSERT INTO daily_expenses (project_id, user_id, expense_date, category, amount)
        Database-->>DailyExpenseModel: Expense Created (ID: 1)
        DailyExpenseModel-->>DailyExpenseController: Expense Object

        %% Update Budget Analysis
        DailyExpenseController->>ProjectService: updateBudgetAnalysis(project)
        ProjectService->>Database: SELECT SUM(amount) FROM daily_expenses WHERE project_id = 1
        Database-->>ProjectService: Total Expenses
        ProjectService-->>DailyExpenseController: Budget Analysis Updated

        DailyExpenseController->>Browser: Redirect to Expense List with Success
        Browser-->>User: Expense List with Success Message
    else Validation Failed
        DailyExpenseRequest-->>DailyExpenseController: Validation Errors
        DailyExpenseController->>Browser: Create Form with Errors
        Browser-->>User: Form with Validation Messages
    end

    %% Daily Report Creation
    Note over User, Database: Daily Report Creation Process
    User->>Browser: Select Project & Click Create Daily Report
    Browser->>DailyReportController: GET /projects/1/daily-reports/create
    DailyReportController->>DailyReportController: checkReportExists(project, today)
    DailyReportController->>Database: SELECT * FROM daily_reports WHERE project_id = 1 AND report_date = CURDATE()
    Database-->>DailyReportController: Report Check Result

    alt Report Not Exists
        DailyReportController->>Browser: Create Daily Report Form
        Browser-->>User: Display Report Form

        User->>Browser: Fill Report Data (activities, challenges, next_plan, progress) & Submit
        Browser->>DailyReportController: POST /projects/1/daily-reports
        DailyReportController->>DailyReportRequest: validate(requestData)
        DailyReportRequest->>DailyReportRequest: validateReportDate()
        DailyReportRequest->>DailyReportRequest: validateProgressPercentage()

        alt Validation Successful
            DailyReportRequest-->>DailyReportController: Validation Passed
            DailyReportController->>DailyReportModel: create(reportData)
            DailyReportModel->>Database: INSERT INTO daily_reports (project_id, report_date, activities_done, progress_percentage)
            Database-->>DailyReportModel: Report Created (ID: 1)
            DailyReportModel-->>DailyReportController: Report Object

            %% Update Project Progress
            DailyReportController->>ProjectService: updateProjectProgressFromReport(project, report)
            ProjectService->>ProjectService: calculateAverageProgress(project)
            ProjectService->>Database: UPDATE projects SET progress = ? WHERE id = 1
            Database-->>ProjectService: Project Updated
            ProjectService-->>DailyReportController: Progress Updated

            DailyReportController->>Browser: Redirect to Report List with Success
            Browser-->>User: Report List with Success Message
        else Validation Failed
            DailyReportRequest-->>DailyReportController: Validation Errors
            DailyReportController->>Browser: Create Form with Errors
            Browser-->>User: Form with Validation Messages
        end
    else Report Already Exists
        DailyReportController->>Browser: Error - Report Already Exists for Today
        Browser-->>User: Error Message with Existing Report Link
    end
```

**Penjelasan Sequence Diagram Daily Operations**:

**Objek yang Terlibat**:
- **DailyExpenseController, DailyReportController**: Controllers untuk daily operations
- **DailyExpenseRequest, DailyReportRequest**: Validation untuk input
- **DailyExpense Model, DailyReport Model**: Data models
- **ProjectService**: Service untuk project calculations

**Business Logic Flow**:
1. **Expense Recording**: Form validation → Expense creation → Budget analysis update
2. **Report Creation**: Existence check → Form validation → Report creation → Progress update

**Key Features**:
- **Unique Constraint**: Satu daily report per hari per project
- **Automatic Updates**: Expense recording update budget analysis, report creation update project progress
- **Data Integrity**: Comprehensive validation untuk data consistency

**Key Messages**:
- `checkReportExists()`: Cek apakah report sudah ada untuk hari ini
- `updateBudgetAnalysis()`: Update budget analysis berdasarkan expenses
- `updateProjectProgressFromReport()`: Update project progress dari daily report

## 8. Sequence Diagram: Worker Assignment

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant WorkerController as WorkerController
    participant ProjectController as ProjectController
    participant WorkerModel as Worker Model
    participant ProjectModel as Project Model
    participant Database as MySQL Database

    Note over User, Database: Worker Assignment to Project Process

    %% View Available Workers
    User->>Browser: Select Project & Click Assign Worker
    Browser->>ProjectController: GET /projects/1/assign-worker
    ProjectController->>WorkerModel: where('end_date', null)->orWhere('end_date', '>', now())
    WorkerModel->>Database: SELECT * FROM workers WHERE end_date IS NULL OR end_date > NOW()
    Database-->>WorkerModel: Available Workers
    WorkerModel-->>ProjectController: Active Workers List

    %% Check Current Assignments
    ProjectController->>ProjectModel: with('workers')->find(1)
    ProjectModel->>Database: SELECT * FROM projects LEFT JOIN project_worker ON projects.id = project_worker.project_id LEFT JOIN workers ON project_worker.worker_id = workers.id WHERE projects.id = 1
    Database-->>ProjectModel: Project with Current Workers
    ProjectModel-->>ProjectController: Project Object with Workers

    ProjectController->>Browser: Worker Assignment Form with Available Workers
    Browser-->>User: Display Available Workers for Assignment

    %% Worker Assignment
    User->>Browser: Select Worker & Confirm Assignment
    Browser->>ProjectController: POST /projects/1/assign-worker
    ProjectController->>ProjectController: validateWorkerAvailability(worker, project)

    alt Worker Available
        ProjectController->>ProjectModel: workers()->attach(workerId)
        ProjectModel->>Database: INSERT INTO project_worker (project_id, worker_id) VALUES (1, workerId)
        Database-->>ProjectModel: Assignment Created
        ProjectModel-->>ProjectController: Worker Assigned Successfully

        %% Update Project Metrics
        ProjectController->>ProjectController: updateProjectMetrics(project)
        ProjectController->>Database: UPDATE projects SET worker_count = (SELECT COUNT(*) FROM project_worker WHERE project_id = 1)
        Database-->>ProjectController: Project Metrics Updated

        ProjectController->>Browser: Redirect with Assignment Success
        Browser-->>User: Project Detail with New Worker Assignment
    else Worker Not Available
        ProjectController->>Browser: Assignment Form with Error
        Browser-->>User: Error Message - Worker Not Available
    end

    %% Worker Removal from Project
    Note over User, Database: Worker Removal from Project Process
    User->>Browser: Click Remove Worker from Project
    Browser->>ProjectController: DELETE /projects/1/workers/workerId
    ProjectController->>ProjectModel: workers()->detach(workerId)
    ProjectModel->>Database: DELETE FROM project_worker WHERE project_id = 1 AND worker_id = workerId
    Database-->>ProjectModel: Assignment Removed
    ProjectModel-->>ProjectController: Worker Removed Successfully

    %% Update Project Metrics
    ProjectController->>ProjectController: updateProjectMetrics(project)
    ProjectController->>Database: UPDATE projects SET worker_count = (SELECT COUNT(*) FROM project_worker WHERE project_id = 1)
    Database-->>ProjectController: Project Metrics Updated

    ProjectController->>Browser: Redirect with Removal Success
    Browser-->>User: Project Detail with Updated Worker List
```

**Penjelasan Sequence Diagram Worker Assignment**:

**Objek yang Terlibat**:
- **WorkerController, ProjectController**: Controllers untuk worker dan project operations
- **Worker Model, Project Model**: Data models dengan many-to-many relationship
- **MySQL Database**: Storage dengan pivot table project_worker

**Business Logic Flow**:
1. **View Available Workers**: Load active workers yang belum assigned
2. **Worker Assignment**: Validate availability → Create assignment → Update metrics
3. **Worker Removal**: Remove assignment → Update project metrics

**Key Features**:
- **Availability Check**: Validate worker availability sebelum assignment
- **Many-to-Many Relationship**: Worker dapat assigned ke multiple projects
- **Automatic Metrics Update**: Project metrics diupdate otomatis

**Key Messages**:
- `validateWorkerAvailability()`: Validate worker dapat di-assign
- `workers()->attach()`: Create many-to-many relationship
- `workers()->detach()`: Remove many-to-many relationship
- `updateProjectMetrics()`: Update project statistics
## 9. Sequence Diagram: Material Management with Cost Calculation

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant MaterialController as MaterialController
    participant MaterialRequest as MaterialRequest
    participant MaterialModel as Material Model
    participant ProjectService as ProjectService
    participant Database as MySQL Database

    Note over User, Database: Material Addition with Automatic Cost Calculation

    %% Material Addition
    User->>Browser: Select Project & Click Add Material
    Browser->>MaterialController: GET /projects/1/materials/create
    MaterialController->>Browser: Add Material Form
    Browser-->>User: Display Material Form

    User->>Browser: Fill Material Data (name, cost, quantity, unit) & Submit
    Browser->>MaterialController: POST /projects/1/materials
    MaterialController->>MaterialRequest: validate(requestData)
    MaterialRequest->>MaterialRequest: validateCostAmount()
    MaterialRequest->>MaterialRequest: validateQuantity()

    alt Validation Successful
        MaterialRequest-->>MaterialController: Validation Passed
        MaterialController->>MaterialModel: create(materialData)
        MaterialModel->>MaterialModel: calculateTotalCost(cost, quantity)
        MaterialModel->>Database: INSERT INTO materials (project_id, name, cost, quantity, unit, total_cost)
        Database-->>MaterialModel: Material Created (ID: 1)
        MaterialModel-->>MaterialController: Material Object

        %% Update Project Material Budget
        MaterialController->>ProjectService: updateProjectMaterialBudget(project)
        ProjectService->>MaterialModel: where('project_id', 1)->sum('total_cost')
        MaterialModel->>Database: SELECT SUM(cost * quantity) FROM materials WHERE project_id = 1
        Database-->>MaterialModel: Total Material Cost
        MaterialModel-->>ProjectService: Material Cost Sum
        ProjectService->>Database: UPDATE projects SET budget_material = ? WHERE id = 1
        Database-->>ProjectService: Project Budget Updated
        ProjectService-->>MaterialController: Budget Update Complete

        MaterialController->>Browser: Redirect to Material List with Success
        Browser-->>User: Material List with Success Message
    else Validation Failed
        MaterialRequest-->>MaterialController: Validation Errors
        MaterialController->>Browser: Create Form with Errors
        Browser-->>User: Form with Validation Messages
    end

    %% Calculate Total Material Cost
    Note over User, Database: Total Material Cost Calculation
    User->>Browser: Click Calculate Total Material Cost
    Browser->>MaterialController: GET /projects/1/materials/calculate-total
    MaterialController->>MaterialModel: where('project_id', 1)->get()
    MaterialModel->>Database: SELECT * FROM materials WHERE project_id = 1
    Database-->>MaterialModel: All Project Materials
    MaterialModel-->>MaterialController: Materials Collection

    MaterialController->>MaterialController: calculateAllMaterialCosts(materials)
    loop For Each Material
        MaterialController->>MaterialController: totalCost += (material.cost * material.quantity)
    end

    MaterialController->>Browser: Material Cost Summary View
    Browser-->>User: Display Total Material Cost Analysis
```

**Penjelasan Sequence Diagram Material Management**:

**Objek yang Terlibat**:
- **MaterialController**: Controller untuk material operations
- **MaterialRequest**: Validation untuk material input
- **Material Model**: Data model dengan cost calculation
- **ProjectService**: Service untuk project budget updates

**Business Logic Flow**:
1. **Material Addition**: Form validation → Cost calculation → Material creation → Budget update
2. **Cost Calculation**: Load all materials → Calculate total cost → Display summary

**Key Features**:
- **Automatic Cost Calculation**: Total cost = cost × quantity
- **Project Budget Update**: Material budget diupdate otomatis
- **Real-time Calculations**: Cost calculations dilakukan real-time

**Key Messages**:
- `calculateTotalCost()`: Hitung total cost material
- `updateProjectMaterialBudget()`: Update project material budget
- `calculateAllMaterialCosts()`: Hitung total cost semua material

## 10. Sequence Diagram: Calendar Event Management

```mermaid
sequenceDiagram
    participant User as Project Manager
    participant Browser as Web Browser
    participant CalendarEventController as CalendarEventController
    participant CalendarEventRequest as CalendarEventRequest
    participant CalendarEventModel as CalendarEvent Model
    participant Database as MySQL Database
    participant FileSystem as File Storage

    Note over User, Database: Calendar Event Creation and Export Process

    %% Event Creation
    User->>Browser: Select Project & Click Create Event
    Browser->>CalendarEventController: GET /projects/1/calendar-events/create
    CalendarEventController->>Browser: Create Event Form
    Browser-->>User: Display Event Form

    User->>Browser: Fill Event Data (title, start_date, end_date, location) & Submit
    Browser->>CalendarEventController: POST /projects/1/calendar-events
    CalendarEventController->>CalendarEventRequest: validate(requestData)
    CalendarEventRequest->>CalendarEventRequest: validateEventDates()
    CalendarEventRequest->>CalendarEventRequest: validateEventDuration()

    alt Validation Successful
        CalendarEventRequest-->>CalendarEventController: Validation Passed
        CalendarEventController->>CalendarEventModel: create(eventData)
        CalendarEventModel->>Database: INSERT INTO calendar_events (project_id, title, start_date, end_date, location)
        Database-->>CalendarEventModel: Event Created (ID: 1)
        CalendarEventModel-->>CalendarEventController: Event Object
        CalendarEventController->>Browser: Redirect to Calendar View with Success
        Browser-->>User: Calendar View with New Event
    else Validation Failed
        CalendarEventRequest-->>CalendarEventController: Validation Errors
        CalendarEventController->>Browser: Create Form with Errors
        Browser-->>User: Form with Validation Messages
    end

    %% Calendar Export Process
    Note over User, Database: Calendar Export Process
    User->>Browser: Click Export Calendar
    Browser->>CalendarEventController: GET /projects/1/calendar/export
    CalendarEventController->>CalendarEventController: getEventsJson(project)
    CalendarEventController->>CalendarEventModel: where('project_id', 1)->get()
    CalendarEventModel->>Database: SELECT * FROM calendar_events WHERE project_id = 1
    Database-->>CalendarEventModel: Project Events
    CalendarEventModel-->>CalendarEventController: Events Collection

    CalendarEventController->>CalendarEventController: generateCalendarData(events)
    loop For Each Event
        CalendarEventController->>CalendarEventController: formatEventForExport(event)
    end

    CalendarEventController->>FileSystem: generateCalendarFile(calendarData, 'ical')
    FileSystem-->>CalendarEventController: Calendar File Generated
    CalendarEventController->>Browser: Send File for Download
    Browser-->>User: Calendar File Downloaded
```

**Penjelasan Sequence Diagram Calendar Event Management**:

**Objek yang Terlibat**:
- **CalendarEventController**: Controller untuk calendar operations
- **CalendarEventRequest**: Validation untuk event input
- **CalendarEvent Model**: Data model untuk calendar events
- **File Storage**: System untuk file generation

**Business Logic Flow**:
1. **Event Creation**: Form validation → Event creation → Calendar update
2. **Calendar Export**: Load events → Format data → Generate file → Download

**Key Features**:
- **Date Validation**: Validate event dates dan duration
- **Export Functionality**: Generate calendar file untuk external use
- **Format Support**: Support multiple calendar formats (iCal, CSV)

**Key Messages**:
- `validateEventDates()`: Validate event start dan end dates
- `generateCalendarData()`: Format events untuk export
- `generateCalendarFile()`: Generate calendar file

## 11. Interaction Patterns Analysis

### 11.1 Common Interaction Patterns

#### Standard CRUD Pattern
Semua sequence diagram mengikuti pattern yang konsisten:
1. **User Request** → Browser → Controller
2. **Form Display** → Controller → Browser → User
3. **Data Submission** → Browser → Controller → Request Validation
4. **Business Logic** → Controller → Service → Model → Database
5. **Response** → Controller → Browser → User

#### Validation Pattern
Setiap input operation menggunakan pattern validation yang sama:
1. **Request Validation** → Validate input data
2. **Business Rule Validation** → Check business constraints
3. **Database Validation** → Check data integrity
4. **Error Handling** → Return validation errors atau proceed

#### Service Layer Pattern
Complex business logic menggunakan service layer:
1. **Controller** → Delegate ke Service
2. **Service** → Implement business logic
3. **Service** → Coordinate multiple models
4. **Service** → Return processed data

### 11.2 Data Flow Patterns

#### Create Operations
1. **Form Display** → Show empty form
2. **Data Input** → User fills form
3. **Validation** → Validate input data
4. **Creation** → Create database record
5. **Side Effects** → Update related data
6. **Response** → Success message dan redirect

#### Update Operations
1. **Load Current Data** → Display existing data
2. **Data Modification** → User modifies data
3. **Validation** → Validate changes
4. **Update** → Update database record
5. **Recalculation** → Update calculated fields
6. **Response** → Success message dan redirect

#### Delete Operations
1. **Confirmation** → Show delete confirmation
2. **User Confirmation** → User confirms deletion
3. **Dependency Check** → Check related data
4. **Deletion** → Delete database record
5. **Cleanup** → Remove related data
6. **Response** → Success message dan redirect

### 11.3 Business Logic Integration

#### Automatic Calculations
- **Project Progress**: Calculated dari task completion
- **Budget Analysis**: Updated saat expense recording
- **Material Costs**: Calculated otomatis (cost × quantity)
- **Profit Percentage**: Calculated dari amount dan estimated profit

#### Cross-Entity Updates
- **Project Creation** → Auto-create budget entries
- **Task Completion** → Update project progress
- **Material Addition** → Update project material budget
- **Worker Assignment** → Update project metrics

#### Data Consistency
- **Unique Constraints**: Daily report per hari per project
- **Referential Integrity**: Foreign key constraints
- **Business Rules**: Validation rules untuk data consistency
- **Transaction Management**: Atomic operations untuk data integrity

## 12. Performance Optimization

### 12.1 Database Optimization

#### Query Optimization
- **Eager Loading**: Load related data dalam single query
- **Selective Loading**: Load only required fields
- **Batch Operations**: Process multiple records efficiently
- **Index Usage**: Optimize queries dengan proper indexing

#### Parallel Processing
- **Dashboard Loading**: Multiple queries dijalankan parallel
- **Budget Comparison**: Budget dan expense calculation parallel
- **Data Aggregation**: Multiple aggregation queries parallel

### 12.2 Caching Strategies

#### Application Level Caching
- **Dashboard Metrics**: Cache calculated statistics
- **Project Statistics**: Cache aggregated data
- **User Sessions**: Efficient session management
- **Query Results**: Cache expensive query results

#### Database Level Optimization
- **Connection Pooling**: Efficient database connections
- **Query Caching**: Cache frequently executed queries
- **Index Optimization**: Strategic indexing untuk performance
- **Database Tuning**: Optimize database configuration

## 13. Security Implementation

### 13.1 Authentication Security

#### Session Management
- **Secure Session Creation**: Proper session handling
- **Session Validation**: Check authentication status
- **Session Timeout**: Automatic session expiration
- **Session Destruction**: Secure logout process

#### Password Security
- **Password Hashing**: Bcrypt hashing untuk passwords
- **Password Validation**: Strong password requirements
- **Credential Verification**: Secure password checking
- **Account Protection**: Protection against brute force

### 13.2 Data Security

#### Input Validation
- **Server-side Validation**: Comprehensive input validation
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Input sanitization
- **File Upload Security**: Secure file handling

#### Access Control
- **Route Protection**: Authentication middleware
- **Resource Authorization**: User access validation
- **Data Isolation**: Project-based data access
- **Audit Trail**: Track user actions

## 14. Error Handling and Recovery

### 14.1 Error Handling Patterns

#### Validation Errors
- **Input Validation**: Return specific validation errors
- **Business Rule Violations**: Clear error messages
- **Data Integrity Errors**: Handle constraint violations
- **User Guidance**: Provide correction guidance

#### System Errors
- **Database Errors**: Handle connection dan query errors
- **File System Errors**: Handle file operation errors
- **Service Errors**: Handle external service failures
- **Graceful Degradation**: Maintain functionality during errors

### 14.2 Recovery Mechanisms

#### Data Recovery
- **Transaction Rollback**: Rollback failed operations
- **Data Backup**: Regular backup procedures
- **Data Restoration**: Recovery dari backup
- **Consistency Checks**: Validate data integrity

#### User Experience Recovery
- **Error Messages**: Clear dan actionable error messages
- **Retry Mechanisms**: Allow users to retry failed operations
- **Alternative Paths**: Provide alternative workflows
- **Help Documentation**: Comprehensive user guidance

## 15. Kesimpulan

Sequence Diagram DisaCloud05-v4 menggambarkan interaksi yang komprehensif dan well-orchestrated antara semua komponen sistem. Dengan design yang konsisten dan robust, aplikasi ini menyediakan:

### 15.1 Architectural Excellence

1. **Layered Architecture**: Clear separation antara presentation, business logic, dan data layers
2. **Service-Oriented Design**: Business logic encapsulation dalam service classes
3. **Consistent Patterns**: Uniform interaction patterns across all functionalities
4. **Scalable Design**: Architecture yang dapat berkembang sesuai kebutuhan

### 15.2 Business Value Delivery

1. **Automated Workflows**: Automatic calculations dan updates untuk efficiency
2. **Data Integrity**: Comprehensive validation dan consistency checks
3. **Real-time Updates**: Immediate reflection of changes across system
4. **User Experience**: Intuitive workflows dengan clear feedback

### 15.3 Technical Robustness

1. **Error Handling**: Comprehensive error handling dan recovery mechanisms
2. **Performance**: Optimized queries dan parallel processing
3. **Security**: Multi-layer security implementation
4. **Maintainability**: Clean code structure dengan clear responsibilities

### 15.4 Integration Capabilities

1. **Cross-Entity Updates**: Seamless integration antar domain objects
2. **Automatic Calculations**: Real-time calculations untuk business metrics
3. **File Management**: Secure file upload, storage, dan download
4. **Export Functionality**: Data export untuk external integration

Sequence Diagram ini menjadi blueprint yang solid untuk implementasi sistem yang reliable, efficient, dan maintainable dalam mengelola proyek konstruksi dengan tingkat kompleksitas tinggi namun tetap user-friendly dan scalable untuk future enhancements.
