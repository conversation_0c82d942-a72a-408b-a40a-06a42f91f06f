# Struktur Database

## Tabel-tabel Utama

### Users
Tabel untuk menyimpan data pengguna sistem
- id (primary key)
- name
- email
- password
- remember_token
- timestamps

### Projects
Tabel untuk menyimpan data proyek
- id (primary key)
- name
- description
- status
- budget_fields
- timestamps

### Tasks
Tabel untuk menyimpan tugas-tugas dalam proyek
- id (primary key)
- project_id (foreign key)
- name
- description
- status
- completed
- timestamps

### Materials
Tabel untuk menyimpan data material
- id (primary key)
- name
- description
- quantity
- unit
- price
- timestamps

### Budgets
Tabel untuk mengelola anggaran
- id (primary key)
- project_id (foreign key)
- amount
- type
- profit
- invoice_fields
- timestamps

### Workers
Tabel untuk data pekerja
- id (primary key)
- name
- position
- contact
- dates
- timestamps

### Daily Expenses
Tabel untuk pengeluaran harian
- id (primary key)
- date
- amount
- description
- category
- timestamps

### Daily Reports
Tabel untuk laporan harian
- id (primary key)
- date
- content
- status
- timestamps

### Activities
Tabel untuk aktivitas
- id (primary key)
- name
- description
- date
- status
- timestamps

### Calendar Events
Tabel untuk event kalender
- id (primary key)
- title
- description
- start_date
- end_date
- timestamps

## Relasi Antar Tabel

1. Projects -> Tasks (One to Many)
   - Satu proyek dapat memiliki banyak tugas

2. Projects -> Budgets (One to Many)
   - Satu proyek dapat memiliki banyak anggaran

3. Projects -> Materials (One to Many)
   - Satu proyek dapat memiliki banyak material

4. Projects -> Workers (Many to Many)
   - Satu proyek dapat memiliki banyak pekerja
   - Satu pekerja dapat bekerja di banyak proyek

## Indeks dan Constraints

- Semua tabel memiliki primary key `id`
- Foreign key constraints untuk menjaga integritas data
- Timestamps untuk tracking waktu pembuatan dan update
- Indeks pada kolom-kolom yang sering digunakan untuk pencarian 