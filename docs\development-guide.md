# Panduan Pengembangan

## Persiapan Lingkungan Development

### 1. Requirements
- PHP 8.2 atau lebih tinggi
- Composer
- Node.js & NPM
- MySQL
- Git

### 2. Setup Project
1. Clone repository:
   ```bash
   git clone [repository-url]
   cd DisaCloud05-v4
   ```

2. Install dependencies PHP:
   ```bash
   composer install
   ```

3. Install dependencies Node.js:
   ```bash
   npm install
   ```

4. Setup environment:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. Konfigurasi database di file .env:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=disacloud05
   DB_USERNAME=root
   DB_PASSWORD=
   ```

6. Jalankan migrasi database:
   ```bash
   php artisan migrate
   ```

7. Jalankan seeder (jika ada):
   ```bash
   php artisan db:seed
   ```

### 3. <PERSON><PERSON>lankan Aplikasi
1. Development server:
   ```bash
   php artisan serve
   ```

2. Vite development server:
   ```bash
   npm run dev
   ```

3. Atau jalankan keduanya sekaligus:
   ```bash
   composer run dev
   ```

## Struktur Project

### 1. Directory Structure
```
DisaCloud05-v4/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   ├── Middleware/
│   │   └── Requests/
│   ├── Models/
│   └── Services/
├── config/
├── database/
│   ├── migrations/
│   └── seeders/
├── public/
├── resources/
│   ├── css/
│   ├── js/
│   └── views/
├── routes/
└── tests/
```

### 2. Key Components
- **Controllers**: Menangani logika aplikasi
- **Models**: Menangani interaksi database
- **Views**: Template Blade untuk tampilan
- **Migrations**: Skema database
- **Routes**: Definisi rute aplikasi

## Best Practices

### 1. Coding Standards
- Ikuti PSR-12 coding standards
- Gunakan Laravel Pint untuk formatting
- Tulis komentar yang jelas
- Gunakan type hinting

### 2. Git Workflow
1. Buat branch baru untuk fitur:
   ```bash
   git checkout -b feature/nama-fitur
   ```

2. Commit changes:
   ```bash
   git add .
   git commit -m "Deskripsi perubahan"
   ```

3. Push ke remote:
   ```bash
   git push origin feature/nama-fitur
   ```

4. Buat Pull Request

### 3. Testing
1. Unit Tests:
   ```bash
   php artisan test
   ```

2. Feature Tests:
   ```bash
   php artisan test --testsuite=Feature
   ```

## Deployment

### 1. Production Setup
1. Update .env:
   ```
   APP_ENV=production
   APP_DEBUG=false
   ```

2. Optimize:
   ```bash
   php artisan optimize
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. Build assets:
   ```bash
   npm run build
   ```

### 2. Maintenance
1. Clear cache:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```

2. Update dependencies:
   ```bash
   composer update
   npm update
   ```

## Troubleshooting

### 1. Common Issues
1. Permission issues:
   ```bash
   chmod -R 775 storage bootstrap/cache
   ```

2. Composer issues:
   ```bash
   composer dump-autoload
   ```

3. Cache issues:
   ```bash
   php artisan cache:clear
   ```

### 2. Debugging
1. Enable debug mode:
   ```
   APP_DEBUG=true
   ```

2. Check logs:
   ```bash
   tail -f storage/logs/laravel.log
   ```

3. Use Laravel Telescope (development):
   ```bash
   composer require laravel/telescope --dev
   php artisan telescope:install
   ``` 