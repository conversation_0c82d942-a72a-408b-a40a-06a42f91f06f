# Referensi Pembuatan Diagram

## 1. Diagram Arsitektur Sistem

### 1.1 Komponen Utama
1. **Client Layer**
   - Web Browser
   - Responsive UI dengan TailwindCSS
   - JavaScript untuk interaksi
   - Axios untuk HTTP requests
   - Chart.js untuk visualisasi data

2. **Application Layer**
   - Laravel Framework 12.0
   - PHP 8.2
   - MVC Architecture
   - Service Layer
   - Repository Pattern

3. **Data Layer**
   - MySQL Database
   - Redis Cache
   - File Storage System

### 1.2 Alur Komunikasi
1. **Client ke Server**
   - HTTP/HTTPS requests
   - RESTful API endpoints
   - WebSocket untuk real-time updates
   - File uploads

2. **Server ke Database**
   - Database queries
   - Caching mechanisms
   - Transaction management
   - Data validation

## 2. Business Process Model and Notation (BPMN)

### 2.1 Proses Autentikasi
1. **Start Point**: User mengakses aplikasi
2. **Decision Points**:
   - User sudah login?
   - Kredensial valid?
3. **Process Steps**:
   - Input kredensial
   - Validasi data
   - Generate session
   - Redirect ke dashboard
4. **End Point**: User berhasil login

### 2.2 Proses Manajemen Proyek
1. **Start Point**: Pembuatan proyek baru
2. **Main Process**:
   - Input data proyek
   - Penentuan anggaran
   - Penugasan pekerja
   - Pembuatan tugas
3. **Sub-processes**:
   - Tracking progress
   - Update status
   - Manajemen material
   - Pencatatan pengeluaran
4. **Decision Points**:
   - Proyek selesai?
   - Anggaran mencukupi?
   - Perlu perubahan?
5. **End Point**: Proyek selesai

### 2.3 Proses Manajemen Anggaran
1. **Start Point**: Pembuatan anggaran
2. **Main Process**:
   - Perhitungan biaya
   - Tracking pengeluaran
   - Update saldo
3. **Decision Points**:
   - Over budget?
   - Perlu approval?
   - Perlu revisi?
4. **End Point**: Laporan keuangan

## 3. Entity Relationship Diagram (ERD)

### 3.1 Entities Utama
1. **Projects**
   - Primary Key: id
   - Attributes: name, description, status, start_date, end_date
   - Relationships: one-to-many dengan Tasks, Materials, Budgets
   - Relationships: many-to-many dengan Workers

2. **Tasks**
   - Primary Key: id
   - Foreign Key: project_id
   - Attributes: name, description, status, completed, due_date
   - Relationships: many-to-one dengan Projects

3. **Workers**
   - Primary Key: id
   - Attributes: name, position, contact, dates
   - Relationships: many-to-many dengan Projects

4. **Budgets**
   - Primary Key: id
   - Foreign Key: project_id
   - Attributes: amount, type, profit, invoice_fields
   - Relationships: many-to-one dengan Projects

5. **Materials**
   - Primary Key: id
   - Foreign Key: project_id
   - Attributes: name, description, quantity, unit, price
   - Relationships: many-to-one dengan Projects

### 3.2 Relasi dan Constraints
1. **One-to-Many**:
   - Project -> Tasks
   - Project -> Materials
   - Project -> Budgets
   - Project -> Daily Reports
   - Project -> Daily Expenses

2. **Many-to-Many**:
   - Projects <-> Workers
   - Projects <-> Materials

3. **Constraints**:
   - Foreign Key Constraints
   - Unique Constraints
   - Not Null Constraints
   - Default Values

## 4. Sequence Diagram

### 4.1 Interaksi Utama
1. **Pembuatan Proyek**
   - Actor: Project Manager
   - System Components: Controller, Model, Database
   - Flow:
     * Request creation
     * Validation
     * Database operation
     * Response handling
     * UI update

2. **Update Status Tugas**
   - Actor: Worker/Manager
   - System Components: Controller, Model, Database
   - Flow:
     * Status update request
     * Validation
     * Database update
     * Notification
     * UI refresh

## 5. State Diagram

### 5.1 State Proyek
1. **States**:
   - Planning
   - In Progress
   - On Hold
   - Completed
   - Cancelled

2. **Transitions**:
   - Planning -> In Progress
   - In Progress -> On Hold
   - On Hold -> In Progress
   - In Progress -> Completed
   - Any State -> Cancelled

### 5.2 State Tugas
1. **States**:
   - Pending
   - In Progress
   - On Hold
   - Completed
   - Cancelled

2. **Transitions**:
   - Pending -> In Progress
   - In Progress -> On Hold
   - On Hold -> In Progress
   - In Progress -> Completed
   - Any State -> Cancelled

## 6. Use Case Diagram

### 6.1 Actors
1. **Administrator**
   - Manage users
   - System configuration
   - Access control

2. **Project Manager**
   - Manage projects
   - Track progress
   - Generate reports
   - Manage budget

3. **Worker**
   - Update task status
   - View assignments
   - Submit reports

### 6.2 Use Cases
1. **Project Management**
   - Create project
   - Edit project
   - Delete project
   - View project details

2. **Task Management**
   - Create task
   - Assign task
   - Update status
   - Track progress

3. **Budget Management**
   - Create budget
   - Track expenses
   - Generate reports
   - Compare budgets

4. **Report Generation**
   - Daily reports
   - Project reports
   - Financial reports
   - Progress reports

## 7. Deployment Diagram

### 7.1 Infrastructure Components
1. **Web Servers**
   - Load Balancer
   - Multiple Application Servers
   - SSL Termination

2. **Database Servers**
   - Primary Database
   - Replica Databases
   - Backup System

3. **Cache Layer**
   - Redis Cluster
   - Session Storage
   - Data Caching

4. **Storage System**
   - File Storage
   - Backup Storage
   - CDN Integration

### 7.2 Network Architecture
1. **Security Layers**
   - Firewall
   - SSL/TLS
   - VPN Access

2. **Load Balancing**
   - Round Robin
   - Health Checks
   - Failover

3. **Monitoring**
   - Server Monitoring
   - Application Monitoring
   - Database Monitoring 