# Dokumentasi Diagram

## 1. Diagram Arsitektur Sistem

### 1.1 High-Level Architecture
```mermaid
graph TB
    Client[Client Browser] --> |HTTP/HTTPS| WebServer[Web Server]
    WebServer --> |Request| Laravel[Laravel Application]
    Laravel --> |Query| Database[(MySQL Database)]
    Laravel --> |Cache| Redis[(Redis Cache)]
    Laravel --> |Storage| FileSystem[(File Storage)]
```

### 1.2 Component Architecture
```mermaid
graph LR
    subgraph Frontend
        Blade[Blade Templates]
        JS[JavaScript]
        CSS[TailwindCSS]
    end
    
    subgraph Backend
        Controllers[Controllers]
        Models[Models]
        Services[Services]
    end
    
    subgraph Database
        MySQL[(MySQL)]
    end
    
    Frontend --> Backend
    Backend --> Database
```

## 2. Diagram Alur Proses (BPMN)

### 2.1 Proses <PERSON>tenti<PERSON>i
```mermaid
graph TD
    A[Start] --> B{User Login?}
    B -->|Yes| C[Input Credentials]
    B -->|No| D[Register]
    C --> E{Valid?}
    E -->|Yes| F[Access Granted]
    E -->|No| G[Show Error]
    D --> H[Create Account]
    H --> I[Login]
    I --> F
```

### 2.2 Proses Manajemen Proyek
```mermaid
graph TD
    A[Start] --> B[Create Project]
    B --> C[Assign Workers]
    C --> D[Set Budget]
    D --> E[Create Tasks]
    E --> F[Track Progress]
    F --> G{Project Complete?}
    G -->|No| F
    G -->|Yes| H[Generate Report]
    H --> I[End]
```

### 2.3 Proses Manajemen Anggaran
```mermaid
graph TD
    A[Start] --> B[Create Budget]
    B --> C[Track Expenses]
    C --> D{Over Budget?}
    D -->|Yes| E[Alert Manager]
    D -->|No| F[Update Records]
    E --> F
    F --> G[Generate Report]
    G --> H[End]
```

## 3. Diagram Relasi Database (ERD)

### 3.1 Core Entities
```mermaid
erDiagram
    PROJECTS ||--o{ TASKS : contains
    PROJECTS ||--o{ BUDGETS : has
    PROJECTS ||--o{ MATERIALS : uses
    PROJECTS }o--o{ WORKERS : employs
    PROJECTS ||--o{ DAILY_REPORTS : generates
    PROJECTS ||--o{ DAILY_EXPENSES : has
```

## 4. Diagram Sequence

### 4.1 Proses Pembuatan Proyek
```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant M as Model
    participant D as Database
    
    U->>C: Create Project Request
    C->>M: Validate Data
    M->>D: Save Project
    D-->>M: Confirm Save
    M-->>C: Return Result
    C-->>U: Show Success Message
```

### 4.2 Proses Update Status Tugas
```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant M as Model
    participant D as Database
    
    U->>C: Update Task Status
    C->>M: Validate Status
    M->>D: Update Record
    D-->>M: Confirm Update
    M-->>C: Return Result
    C-->>U: Show Updated Status
```

## 5. Diagram State

### 5.1 State Proyek
```mermaid
stateDiagram-v2
    [*] --> Planning
    Planning --> InProgress
    InProgress --> OnHold
    OnHold --> InProgress
    InProgress --> Completed
    Completed --> [*]
```

### 5.2 State Tugas
```mermaid
stateDiagram-v2
    [*] --> Pending
    Pending --> InProgress
    InProgress --> OnHold
    OnHold --> InProgress
    InProgress --> Completed
    Completed --> [*]
```

## 6. Diagram Deployment

### 6.1 Production Environment
```mermaid
graph TB
    subgraph Production
        LB[Load Balancer]
        WS1[Web Server 1]
        WS2[Web Server 2]
        DB[(Database)]
        Cache[(Redis Cache)]
        Storage[(File Storage)]
    end
    
    Client --> LB
    LB --> WS1
    LB --> WS2
    WS1 --> DB
    WS2 --> DB
    WS1 --> Cache
    WS2 --> Cache
    WS1 --> Storage
    WS2 --> Storage
```

## 7. Diagram Use Case

### 7.1 Use Case Utama
```mermaid
graph TD
    subgraph Actors
        Admin[Administrator]
        Manager[Project Manager]
        Worker[Worker]
    end
    
    subgraph Use Cases
        UC1[Manage Projects]
        UC2[Manage Workers]
        UC3[Track Budget]
        UC4[Generate Reports]
        UC5[Update Tasks]
    end
    
    Admin --> UC1
    Admin --> UC2
    Manager --> UC1
    Manager --> UC3
    Manager --> UC4
    Worker --> UC5
```

## Catatan Penggunaan Diagram

1. **Tools yang Direkomendasikan**:
   - Draw.io
   - Lucidchart
   - PlantUML
   - Mermaid.js

2. **Format File**:
   - PNG untuk presentasi
   - SVG untuk web
   - PDF untuk dokumen

3. **Best Practices**:
   - Gunakan warna konsisten
   - Tambahkan legenda
   - Berikan deskripsi
   - Update diagram secara berkala

4. **Pemeliharaan**:
   - Review diagram setiap update sistem
   - Simpan versi diagram
   - Dokumentasikan perubahan
   - Sertakan dalam version control 