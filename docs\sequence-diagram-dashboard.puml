@startuml Dashboard Access Sequence Diagram
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor "Project Manager\n/ Site Leader" as PM
participant "View" as V
participant "DashboardController" as DC
database "Database" as DB

== Dashboard Access ==
PM -> V : Access dashboard menu
activate V
V -> DC : GET /dashboard
activate DC

note over DC : Check auth middleware\nVerify user authentication

== Database Queries for Dashboard Statistics ==

DC -> DB : Query total active projects
activate DB
note over DB : SELECT COUNT(*) FROM projects\nWHERE status = 'in_progress'
DB --> DC : Return active projects count
deactivate DB

DC -> DB : Query upcoming deadlines
activate DB
note over DB : SELECT * FROM projects\nWHERE status = 'in_progress'\nAND end_date BETWEEN NOW()\nAND DATE_ADD(NOW(), INTERVAL 7 DAY)\nORDER BY end_date
DB --> DC : Return upcoming deadline projects
deactivate DB

DC -> DB : Query project status distribution
activate DB
note over DB : SELECT status, COUNT(*) as count\nFROM projects\nGROUP BY status\n(planned, in_progress, completed, on_hold)
DB --> DC : Return status counts
deactivate DB

DC -> DB : Query active projects with progress
activate DB
note over DB : SELECT * FROM projects\nWHERE status = 'in_progress'\nORDER BY end_date
DB --> DC : Return active projects data
deactivate DB

DC -> DB : Query recent status updates
activate DB
note over DB : SELECT * FROM projects\nORDER BY updated_at DESC\nLIMIT 5
DB --> DC : Return recent project updates
deactivate DB

DC -> DB : Query notifications data
activate DB
note over DB : Multiple queries:\n• Urgent deadlines (≤3 days)\n• Stalled projects (no update 7 days)\n• Overdue projects
DB --> DC : Return notification data
deactivate DB

== Response ==
DC -> V : Return dashboard view with data
note over DC : Compact data:\n• totalActiveProjects\n• upcomingDeadlines\n• projectStatusDistribution\n• activeProjects\n• recentStatusUpdates\n• notifications
deactivate DC

V -> PM : Display dashboard with statistics
note over V : Show:\n• Charts and graphs\n• Project lists\n• Notifications\n• Progress bars
deactivate V

@enduml
