# Sequence Diagram - Logout Process DisaCloud05-v4

## Format Standard UML Sequence Diagram

```
Project Manager    View              AuthController         Database
      |             |                      |                   |
      |             |                      |                   |
      |------------>|                      |                   |
      | Click Logout|                      |                   |
      |             |                      |                   |
      |             |--------------------->|                   |
      |             | POST /logout         |                   |
      |             |                      |                   |
      |             |                      |------------------>|
      |             |                      | Verify CSRF Token |
      |             |                      |<------------------|
      |             |                      | Token Valid       |
      |             |                      |                   |
      |             |                      |------------------>|
      |             |                      | Check Auth Status |
      |             |                      |<------------------|
      |             |                      | User Authenticated|
      |             |                      |                   |
      |             |                      |------------------>|
      |             |                      | Auth::logout()    |
      |             |                      |<------------------|
      |             |                      | Authentication    |
      |             |                      | Cleared           |
      |             |                      |                   |
      |             |                      |------------------>|
      |             |                      | session()->       |
      |             |                      | invalidate()      |
      |             |                      |<------------------|
      |             |                      | Session Destroyed |
      |             |                      |                   |
      |             |                      |------------------>|
      |             |                      | session()->       |
      |             |                      | regenerateToken() |
      |             |                      |<------------------|
      |             |                      | New CSRF Token    |
      |             |                      |                   |
      |             |<---------------------|                   |
      |             | redirect()->route    |                   |
      |             | ('landing')          |                   |
      |             |                      |                   |
      |<------------|                      |                   |
      | Display     |                      |                   |
      | Landing Page|                      |                   |
      |             |                      |                   |
```

## Penjelasan Alur Logout

### 1. User Action
- Project Manager mengklik tombol "Logout" di interface aplikasi

### 2. Request Processing
- View mengirim POST request ke `/logout` endpoint
- Request diterima oleh `AuthController::logout()` method

### 3. Security Verification
- Sistem memverifikasi CSRF token untuk keamanan
- Mengecek status autentikasi user saat ini

### 4. Session Management
- `Auth::logout()`: Menghapus status autentikasi user
- `session()->invalidate()`: Menghancurkan seluruh data session
- `session()->regenerateToken()`: Membuat CSRF token baru

### 5. Response & Redirect
- Controller mengirim redirect response ke landing page
- User diarahkan ke halaman utama dalam status tidak terautentikasi

## Kode Implementation

Berdasarkan `app/Http/Controllers/AuthController.php`:

```php
public function logout(Request $request)
{
    Auth::logout();                    // Clear authentication
    $request->session()->invalidate(); // Destroy session
    $request->session()->regenerateToken(); // Generate new CSRF token
    return redirect()->route('landing');
}
```

## Route Configuration

Dari `routes/web.php`:
```php
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
```

## Security Features

1. **CSRF Protection**: Automatic CSRF token verification
2. **Complete Session Cleanup**: All session data removed
3. **Token Regeneration**: New CSRF token for security
4. **Secure Redirect**: Redirect to safe landing page
5. **Middleware Protection**: Protected routes automatically secured
