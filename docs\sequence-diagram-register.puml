@startuml Create Account Sequence Diagram
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor "Project Manager\n/ Site Leader" as PM
participant "View" as V
participant "RegisterController" as RC
database "Database" as DB

== Account Registration Access ==
PM -> V : Access register page
activate V
V -> RC : GET /register
activate RC

note over RC : Check guest middleware\nEnsure user not authenticated

RC -> V : Return registration form
note over RC : showRegistrationForm() method
deactivate RC

V -> PM : Display registration form
note over V : Show form fields:\n• Name (required|string|max:255)\n• Email (required|email|unique:users)\n• Password (required|min:6|confirmed)\n• Password Confirmation
deactivate V

== Form Submission ==
PM -> V : Submit registration data
activate V
V -> RC : POST /register (name, email, password, password_confirmation)
activate RC

note over RC : register() method\nValidate input data

== Validation Process ==
RC -> RC : Validate input data
note over RC : Validation rules:\n• name: required|string|max:255\n• email: required|email|unique:users\n• password: required|min:6|confirmed

alt Validation fails
    RC -> V : Return validation errors
    note over RC : redirect()->back()\n->withErrors($validator)\n->withInput()
    V -> PM : Display error messages
    note over V : Show validation errors\nwith previous input data
else Validation success
    == Database Operations ==
    RC -> DB : Check email uniqueness
    activate DB
    note over DB : SELECT COUNT(*) FROM users\nWHERE email = ?
    DB --> RC : Email available
    deactivate DB
    
    RC -> RC : Hash password
    note over RC : Hash::make($request->password)
    
    RC -> DB : Create new user
    activate DB
    note over DB : INSERT INTO users\n(name, email, password, created_at, updated_at)\nVALUES (?, ?, ?, NOW(), NOW())
    DB --> RC : User created successfully
    deactivate DB
    
    == Success Response ==
    RC -> V : redirect()->route('login')
    note over RC : with('success', 'Akun berhasil dibuat,\nsilakan login.')
    deactivate RC
    
    V -> PM : Display login page with success message
    note over V : Redirect to login form\nwith success notification
    deactivate V
end

@enduml
