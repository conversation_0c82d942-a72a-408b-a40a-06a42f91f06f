# Black Box Testing - Authentication Module

## Overview
Dokumentasi testing untuk modul autentikasi aplikasi DisaCloud05-v4, mencakup fitur login, register, logout, dan manajemen session.

## Test Environment
- **Application**: DisaCloud05-v4
- **Module**: Authentication
- **Test Type**: Black Box Testing
- **Browser**: Chrome, Firefox, Safari, Edge
- **Test Data**: Valid/Invalid user credentials

## Test Cases

### 1. Landing Page Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_001 | Akses halaman landing page | URL: `/` | Halaman landing ditampilkan dengan link login/register | | |
| TC_AUTH_002 | Navigasi ke login dari landing | Click "Login" button | Redirect ke halaman login `/login` | | |
| TC_AUTH_003 | Navigasi ke register dari landing | Click "Register" button | Redirect ke halaman register `/register` | | |

### 2. Login Functionality Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_004 | Login dengan kredensial valid | Email: `<EMAIL>`<br>Password: `password123` | Login berhasil, redirect ke dashboard | | |
| TC_AUTH_005 | Login dengan email kosong | Email: ` `<br>Password: `password123` | Error: "Email field is required" | | |
| TC_AUTH_006 | Login dengan password kosong | Email: `<EMAIL>`<br>Password: ` ` | Error: "Password field is required" | | |
| TC_AUTH_007 | Login dengan email invalid format | Email: `invalid-email`<br>Password: `password123` | Error: "Please enter a valid email" | | |
| TC_AUTH_008 | Login dengan email tidak terdaftar | Email: `<EMAIL>`<br>Password: `password123` | Error: "The provided credentials do not match our records" | | |
| TC_AUTH_009 | Login dengan password salah | Email: `<EMAIL>`<br>Password: `wrongpassword` | Error: "The provided credentials do not match our records" | | |
| TC_AUTH_010 | Login dengan remember me checked | Email: `<EMAIL>`<br>Password: `password123`<br>Remember: `checked` | Login berhasil, session persistent | | |
| TC_AUTH_011 | Login ketika sudah login | Access `/login` saat sudah login | Redirect ke dashboard | | |

### 3. Registration Functionality Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_012 | Register dengan data valid | Name: `John Doe`<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password123` | Registrasi berhasil, redirect ke login dengan success message | | |
| TC_AUTH_013 | Register dengan nama kosong | Name: ` `<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password123` | Error: "Name field is required" | | |
| TC_AUTH_014 | Register dengan email kosong | Name: `John Doe`<br>Email: ` `<br>Password: `password123`<br>Confirm: `password123` | Error: "Email field is required" | | |
| TC_AUTH_015 | Register dengan email invalid | Name: `John Doe`<br>Email: `invalid-email`<br>Password: `password123`<br>Confirm: `password123` | Error: "Please enter a valid email" | | |
| TC_AUTH_016 | Register dengan email sudah ada | Name: `John Doe`<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password123` | Error: "Email already exists" | | |
| TC_AUTH_017 | Register dengan password kurang dari 6 karakter | Name: `John Doe`<br>Email: `<EMAIL>`<br>Password: `123`<br>Confirm: `123` | Error: "Password must be at least 6 characters" | | |
| TC_AUTH_018 | Register dengan password tidak match | Name: `John Doe`<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password456` | Error: "Password confirmation does not match" | | |
| TC_AUTH_019 | Register dengan nama maksimal 255 karakter | Name: `[255 characters]`<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password123` | Registrasi berhasil | | |
| TC_AUTH_020 | Register dengan nama lebih dari 255 karakter | Name: `[256 characters]`<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password123` | Error: "Name may not be greater than 255 characters" | | |

### 4. Logout Functionality Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_021 | Logout dari dashboard | Click "Logout" button | Session terminated, redirect ke landing page | | |
| TC_AUTH_022 | Akses halaman protected setelah logout | Access `/dashboard` after logout | Redirect ke login page | | |
| TC_AUTH_023 | Logout dengan CSRF token valid | Valid CSRF token | Logout berhasil | | |

### 5. Session Management Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_024 | Akses dashboard tanpa login | Direct access to `/dashboard` | Redirect ke login page | | |
| TC_AUTH_025 | Akses projects tanpa login | Direct access to `/projects` | Redirect ke login page | | |
| TC_AUTH_026 | Session timeout handling | Wait for session timeout | Redirect ke login dengan timeout message | | |
| TC_AUTH_027 | Multiple browser session | Login di 2 browser berbeda | Both sessions active | | |
| TC_AUTH_028 | Session regeneration setelah login | Check session ID before/after login | Session ID berubah setelah login | | |

### 6. Security Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_029 | SQL Injection pada email | Email: `<EMAIL>'; DROP TABLE users; --`<br>Password: `password123` | Input disanitasi, tidak ada SQL injection | | |
| TC_AUTH_030 | XSS pada nama register | Name: `<script>alert('XSS')</script>`<br>Email: `<EMAIL>`<br>Password: `password123`<br>Confirm: `password123` | Input disanitasi, script tidak dieksekusi | | |
| TC_AUTH_031 | CSRF protection pada login | Submit login tanpa CSRF token | Error: "CSRF token mismatch" | | |
| TC_AUTH_032 | Password hashing | Check database setelah register | Password tersimpan dalam bentuk hash | | |

### 7. UI/UX Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_033 | Responsive design login page | Test pada mobile (375x667) | Layout responsive, semua elemen terlihat | | |
| TC_AUTH_034 | Responsive design register page | Test pada tablet (768x1024) | Layout responsive, form dapat digunakan | | |
| TC_AUTH_035 | Error message visibility | Submit form dengan error | Error message jelas dan mudah dibaca | | |
| TC_AUTH_036 | Success message visibility | Successful registration | Success message ditampilkan dengan jelas | | |
| TC_AUTH_037 | Form field validation real-time | Type invalid email | Real-time validation feedback | | |

### 8. Performance Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_AUTH_038 | Login response time | Valid credentials | Response time < 2 seconds | | |
| TC_AUTH_039 | Register response time | Valid registration data | Response time < 3 seconds | | |
| TC_AUTH_040 | Page load time | Load login/register page | Page load time < 3 seconds | | |

## Test Summary

### Test Execution Summary
- **Total Test Cases**: 40
- **Passed**: [To be filled]
- **Failed**: [To be filled]
- **Blocked**: [To be filled]
- **Not Executed**: [To be filled]

### Pass/Fail Criteria
- **Critical Functions**: 100% pass rate required
- **Overall**: 95% pass rate required
- **No Critical/High severity defects**

### Defects Found
[To be documented during test execution]

### Recommendations
[To be provided after test completion]

---

**Test Executed By**: [Tester Name]  
**Test Execution Date**: [Date]  
**Review Date**: [Date]  
**Approved By**: [Approver Name]
