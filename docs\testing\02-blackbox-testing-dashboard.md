# Black Box Testing - Dashboard Module

## Overview
Dokumentasi testing untuk modul dashboard aplikasi DisaCloud05-v4, mencakup overview statistik, filter deadline, progress tracking, dan notifikasi.

## Test Environment
- **Application**: DisaCloud05-v4
- **Module**: Dashboard
- **Test Type**: Black Box Testing
- **URL**: `/dashboard`
- **Prerequisites**: User harus sudah login

## Test Cases

### 1. Dashboard Access & Layout Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_001 | Akses dashboard setelah login | Valid user session | Dashboard ditampilkan dengan semua komponen | | |
| TC_DASH_002 | Dashboard layout responsiveness | Screen: 1920x1080 | Layout optimal, semua widget terlihat | | |
| TC_DASH_003 | Dashboard layout mobile | Screen: 375x667 | Layout responsive, navigasi mobile friendly | | |
| TC_DASH_004 | Dashboard sidebar navigation | Click sidebar menu items | Navigation berfungsi dengan highlight aktif | | |

### 2. Project Statistics Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_005 | Total proyek aktif display | Projects dengan status 'in_progress' | Menampilkan jumlah proyek aktif yang benar | | |
| TC_DASH_006 | Project status distribution | Mix projects: planning, in_progress, completed, on_hold | Chart distribusi status ditampilkan dengan benar | | |
| TC_DASH_007 | Empty state - no active projects | No projects dengan status 'in_progress' | Menampilkan "0" atau pesan "No active projects" | | |
| TC_DASH_008 | Large number of projects | 100+ active projects | Statistik ditampilkan dengan format yang tepat | | |

### 3. Deadline Filter Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_009 | Default deadline filter | No filter selected | Default filter "7 days" aktif | | |
| TC_DASH_010 | Filter deadline 1 hari | Select "1 day" filter | Menampilkan proyek dengan deadline 1 hari ke depan | | |
| TC_DASH_011 | Filter deadline 1 minggu | Select "1 week" filter | Menampilkan proyek dengan deadline 1 minggu ke depan | | |
| TC_DASH_012 | Filter deadline 1 bulan | Select "1 month" filter | Menampilkan proyek dengan deadline 1 bulan ke depan | | |
| TC_DASH_013 | Filter dengan no upcoming deadlines | No projects dalam range filter | Menampilkan empty state atau "No upcoming deadlines" | | |

### 4. Active Projects Progress Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_014 | Progress calculation accuracy | Project: start_date, end_date, current_date | Progress percentage dihitung dengan benar | | |
| TC_DASH_015 | Progress bar visualization | Projects dengan berbagai progress | Progress bar menampilkan persentase yang tepat | | |
| TC_DASH_016 | Days left calculation | Project dengan end_date di masa depan | Days left dihitung dengan benar | | |
| TC_DASH_017 | Overdue project indication | Project dengan end_date di masa lalu | Ditampilkan sebagai overdue dengan indikator | | |
| TC_DASH_018 | Future start date project | Project dengan start_date di masa depan | Progress 0%, days left dihitung dari start_date | | |

### 5. Notifications Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_019 | Urgent deadlines notification | Projects dengan deadline ≤ 3 hari | Notifikasi urgent deadlines ditampilkan | | |
| TC_DASH_020 | Stalled projects notification | Projects tidak update ≥ 7 hari | Notifikasi stalled projects ditampilkan | | |
| TC_DASH_021 | Overdue projects notification | Projects dengan end_date < today | Notifikasi overdue projects ditampilkan | | |
| TC_DASH_022 | No notifications state | No urgent/stalled/overdue projects | Menampilkan "No notifications" atau empty state | | |
| TC_DASH_023 | Multiple notifications | Mix urgent, stalled, overdue projects | Semua jenis notifikasi ditampilkan dengan benar | | |

### 6. Recent Status Updates Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_024 | Recent status updates display | Projects dengan recent updates | Menampilkan daftar update terbaru | | |
| TC_DASH_025 | Status update timestamp | Projects dengan berbagai update time | Timestamp ditampilkan dengan format yang benar | | |
| TC_DASH_026 | Status update sorting | Multiple recent updates | Updates diurutkan berdasarkan waktu (terbaru dulu) | | |
| TC_DASH_027 | No recent updates | No projects updated recently | Menampilkan empty state atau "No recent updates" | | |

### 7. Navigation & Quick Actions Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_028 | Navigate to projects from dashboard | Click "View All Projects" | Redirect ke halaman projects | | |
| TC_DASH_029 | Navigate to specific project | Click project name/link | Redirect ke detail project | | |
| TC_DASH_030 | Quick create project | Click "Create Project" button | Redirect ke form create project | | |
| TC_DASH_031 | Navigate to workers | Click workers link/button | Redirect ke halaman workers | | |
| TC_DASH_032 | Navigate to reports | Click reports link/button | Redirect ke halaman reports | | |

### 8. Data Refresh & Real-time Updates Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_033 | Page refresh data consistency | Refresh dashboard page | Data tetap konsisten dan akurat | | |
| TC_DASH_034 | Auto-refresh functionality | Wait for auto-refresh (if implemented) | Data ter-update otomatis | | |
| TC_DASH_035 | Manual refresh button | Click refresh button (if available) | Data ter-update dengan loading indicator | | |

### 9. Performance Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_036 | Dashboard load time | Access dashboard | Page load time < 3 seconds | | |
| TC_DASH_037 | Dashboard with large dataset | 1000+ projects, workers, tasks | Dashboard tetap responsive | | |
| TC_DASH_038 | Filter response time | Apply deadline filter | Filter response time < 1 second | | |
| TC_DASH_039 | Chart rendering performance | Complex project data | Charts render dalam < 2 seconds | | |

### 10. Error Handling Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_040 | Database connection error | Simulate DB error | Graceful error handling, user-friendly message | | |
| TC_DASH_041 | Network timeout | Simulate network timeout | Timeout handling dengan retry option | | |
| TC_DASH_042 | Invalid filter parameter | Manipulate filter URL parameter | Default filter applied, no error | | |
| TC_DASH_043 | Missing project data | Project deleted while viewing dashboard | Handle missing data gracefully | | |

### 11. Security Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_044 | Unauthorized access | Access dashboard without login | Redirect ke login page | | |
| TC_DASH_045 | Session timeout on dashboard | Let session expire | Redirect ke login dengan timeout message | | |
| TC_DASH_046 | CSRF protection | Manipulate CSRF token | Request rejected dengan error | | |

### 12. Browser Compatibility Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_DASH_047 | Chrome compatibility | Test pada Chrome latest | Dashboard berfungsi normal | | |
| TC_DASH_048 | Firefox compatibility | Test pada Firefox latest | Dashboard berfungsi normal | | |
| TC_DASH_049 | Safari compatibility | Test pada Safari latest | Dashboard berfungsi normal | | |
| TC_DASH_050 | Edge compatibility | Test pada Edge latest | Dashboard berfungsi normal | | |

## Test Summary

### Test Execution Summary
- **Total Test Cases**: 50
- **Passed**: [To be filled]
- **Failed**: [To be filled]
- **Blocked**: [To be filled]
- **Not Executed**: [To be filled]

### Pass/Fail Criteria
- **Critical Functions**: 100% pass rate required
- **Overall**: 95% pass rate required
- **Performance**: Load time < 3 seconds
- **No Critical/High severity defects**

### Key Metrics to Validate
- **Statistics Accuracy**: All counts and calculations must be correct
- **Filter Functionality**: All filters must work as expected
- **Notification System**: All notifications must be relevant and timely
- **Navigation**: All links and buttons must work correctly
- **Responsive Design**: Must work on all screen sizes

### Defects Found
[To be documented during test execution]

### Recommendations
[To be provided after test completion]

---

**Test Executed By**: [Tester Name]  
**Test Execution Date**: [Date]  
**Review Date**: [Date]  
**Approved By**: [Approver Name]
