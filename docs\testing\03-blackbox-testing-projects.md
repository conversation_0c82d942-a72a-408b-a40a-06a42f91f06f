# Black Box Testing - Projects Management Module

## Overview
Dokumentasi testing untuk modul manajemen proyek aplikasi DisaCloud05-v4, mencakup CRUD operations, task management, worker assignment, dan calendar integration.

## Test Environment
- **Application**: DisaCloud05-v4
- **Module**: Projects Management
- **Test Type**: Black Box Testing
- **Base URL**: `/projects`
- **Prerequisites**: User harus sudah login

## Test Cases

### 1. Projects Index Page Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_001 | Akses halaman projects index | URL: `/projects` | Menampilkan daftar semua proyek | | |
| TC_PROJ_002 | Filter proyek berdasarkan status | Status: `planning` | Menampilkan hanya proyek dengan status planning | | |
| TC_PROJ_003 | Filter proyek berdasarkan status | Status: `in_progress` | Menampilkan hanya proyek dengan status in_progress | | |
| TC_PROJ_004 | Filter proyek berdasarkan status | Status: `completed` | Menampilkan hanya proyek dengan status completed | | |
| TC_PROJ_005 | Filter proyek berdasarkan status | Status: `on_hold` | Menampilkan hanya proyek dengan status on_hold | | |
| TC_PROJ_006 | Clear filter | Remove status filter | Menampilkan semua proyek | | |
| TC_PROJ_007 | Empty state - no projects | No projects in database | Menampilkan empty state message | | |
| TC_PROJ_008 | Project sorting | Default sorting | Proyek diurutkan berdasarkan start_date ascending | | |

### 2. Create Project Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_009 | Akses form create project | URL: `/projects/create` | Form create project ditampilkan | | |
| TC_PROJ_010 | Create project dengan data valid | Name: `Test Project`<br>Description: `Test Description`<br>Status: `planning`<br>Start Date: `2025-07-01`<br>End Date: `2025-12-31`<br>Budget Material: `1000000`<br>Budget Jasa: `2000000` | Project berhasil dibuat, redirect ke projects index | | |
| TC_PROJ_011 | Create project dengan nama kosong | Name: ` `<br>Other fields: valid | Error: "Name field is required" | | |
| TC_PROJ_012 | Create project dengan nama maksimal 255 karakter | Name: `[255 characters]`<br>Other fields: valid | Project berhasil dibuat | | |
| TC_PROJ_013 | Create project dengan nama lebih dari 255 karakter | Name: `[256 characters]`<br>Other fields: valid | Error: "Name may not be greater than 255 characters" | | |
| TC_PROJ_014 | Create project dengan end date sebelum start date | Start Date: `2025-12-31`<br>End Date: `2025-07-01` | Error: "End date must be after or equal to start date" | | |
| TC_PROJ_015 | Create project dengan budget negatif | Budget Material: `-1000` | Error: "Budget must be greater than or equal to 0" | | |
| TC_PROJ_016 | Create project dengan tasks | Tasks: `["Task 1", "Task 2", "Task 3"]` | Project dan tasks berhasil dibuat | | |
| TC_PROJ_017 | Create project tanpa tasks | No tasks added | Project berhasil dibuat tanpa tasks | | |

### 3. Edit Project Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_018 | Akses form edit project | URL: `/projects/{id}/edit` | Form edit dengan data existing ditampilkan | | |
| TC_PROJ_019 | Update project dengan data valid | Update name, description, dates | Project berhasil diupdate | | |
| TC_PROJ_020 | Update project status | Change status from `planning` to `in_progress` | Status berhasil diupdate | | |
| TC_PROJ_021 | Add new task to existing project | Add task: `"New Task"` | Task baru berhasil ditambahkan | | |
| TC_PROJ_022 | Edit existing task | Update task name | Task berhasil diupdate | | |
| TC_PROJ_023 | Delete task from project | Select task for deletion | Task berhasil dihapus | | |
| TC_PROJ_024 | Update project dengan validation error | Invalid data (e.g., empty name) | Error message ditampilkan, data tidak tersimpan | | |

### 4. Delete Project Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_025 | Delete project confirmation | Click delete button | Confirmation dialog ditampilkan | | |
| TC_PROJ_026 | Confirm delete project | Confirm deletion | Project berhasil dihapus, redirect ke index | | |
| TC_PROJ_027 | Cancel delete project | Cancel deletion | Project tidak dihapus, tetap di halaman | | |
| TC_PROJ_028 | Delete project dengan related data | Project dengan workers, tasks, materials | Project dan related data terhapus (cascade) | | |

### 5. Mark Project as Completed Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_029 | Mark project as completed | Project dengan status `in_progress` | Status berubah menjadi `completed` | | |
| TC_PROJ_030 | Mark completed project | Project dengan status `completed` | No action atau error message | | |
| TC_PROJ_031 | Mark project completed dengan tasks incomplete | Project dengan incomplete tasks | Confirmation atau warning message | | |

### 6. Worker Assignment Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_032 | View project workers | Access project edit page | Menampilkan assigned workers | | |
| TC_PROJ_033 | Assign worker to project | Select available worker | Worker berhasil di-assign ke project | | |
| TC_PROJ_034 | Remove worker from project | Click remove worker | Worker berhasil diremove dari project | | |
| TC_PROJ_035 | Assign multiple workers | Select multiple workers | Semua workers berhasil di-assign | | |
| TC_PROJ_036 | View available workers | Open worker selection | Menampilkan workers yang belum di-assign | | |

### 7. Project Calendar Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_037 | Akses project calendar | URL: `/projects/calendar` | Calendar view ditampilkan | | |
| TC_PROJ_038 | Filter calendar by project | Select specific project | Calendar menampilkan hanya project yang dipilih | | |
| TC_PROJ_039 | View all projects in calendar | No filter selected | Calendar menampilkan semua projects | | |
| TC_PROJ_040 | Navigate calendar months | Click prev/next month | Calendar navigasi berfungsi | | |
| TC_PROJ_041 | Export calendar | Click export button | Calendar data ter-export | | |

### 8. Project Date Update Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_042 | Update project date via AJAX | New start/end date | Date berhasil diupdate tanpa page reload | | |
| TC_PROJ_043 | Update date dengan invalid range | End date before start date | Error message, date tidak terupdate | | |
| TC_PROJ_044 | Update date untuk completed project | Change date of completed project | Date berhasil diupdate atau warning message | | |

### 9. Project Validation Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_045 | Required field validation | Submit form dengan field kosong | Error messages untuk required fields | | |
| TC_PROJ_046 | Date format validation | Invalid date format | Error: "Please enter a valid date" | | |
| TC_PROJ_047 | Numeric field validation | Non-numeric budget values | Error: "Budget must be a number" | | |
| TC_PROJ_048 | Status enum validation | Invalid status value | Error atau default status applied | | |

### 10. Project Search & Pagination Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_049 | Search project by name | Search term: `"Test"` | Menampilkan projects dengan nama mengandung "Test" | | |
| TC_PROJ_050 | Search with no results | Search term: `"NonExistent"` | Menampilkan "No projects found" | | |
| TC_PROJ_051 | Pagination functionality | Navigate through pages | Pagination berfungsi dengan benar | | |
| TC_PROJ_052 | Items per page | Change items per page | Jumlah items per page berubah sesuai setting | | |

### 11. Project Performance Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_053 | Load projects index | Large dataset (1000+ projects) | Page load time < 3 seconds | | |
| TC_PROJ_054 | Create project response time | Submit create form | Response time < 2 seconds | | |
| TC_PROJ_055 | Update project response time | Submit update form | Response time < 2 seconds | | |
| TC_PROJ_056 | Delete project response time | Confirm delete | Response time < 1 second | | |

### 12. Project Security Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_057 | Unauthorized access to projects | Access without login | Redirect ke login page | | |
| TC_PROJ_058 | CSRF protection on create | Submit without CSRF token | Request rejected | | |
| TC_PROJ_059 | CSRF protection on update | Submit without CSRF token | Request rejected | | |
| TC_PROJ_060 | CSRF protection on delete | Submit without CSRF token | Request rejected | | |
| TC_PROJ_061 | SQL injection on search | Search: `'; DROP TABLE projects; --` | Input disanitasi, no SQL injection | | |
| TC_PROJ_062 | XSS on project name | Name: `<script>alert('XSS')</script>` | Input disanitasi, script tidak dieksekusi | | |

### 13. Project UI/UX Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_063 | Responsive design - mobile | Screen: 375x667 | Layout responsive, form dapat digunakan | | |
| TC_PROJ_064 | Responsive design - tablet | Screen: 768x1024 | Layout responsive, table dapat di-scroll | | |
| TC_PROJ_065 | Form field labels | All form fields | Labels jelas dan descriptive | | |
| TC_PROJ_066 | Error message clarity | Various validation errors | Error messages jelas dan actionable | | |
| TC_PROJ_067 | Success message display | Successful operations | Success messages ditampilkan dengan jelas | | |
| TC_PROJ_068 | Loading indicators | Form submissions | Loading indicators ditampilkan | | |

### 14. Project Integration Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_PROJ_069 | Project-Budget integration | Create project dengan budget | Budget entry otomatis terbuat | | |
| TC_PROJ_070 | Project-Material integration | Add materials to project | Materials terkait dengan project | | |
| TC_PROJ_071 | Project-Report integration | Create daily report for project | Report terkait dengan project | | |
| TC_PROJ_072 | Project-Calendar integration | Project dates | Dates muncul di calendar view | | |

## Test Summary

### Test Execution Summary
- **Total Test Cases**: 72
- **Passed**: [To be filled]
- **Failed**: [To be filled]
- **Blocked**: [To be filled]
- **Not Executed**: [To be filled]

### Pass/Fail Criteria
- **Critical Functions**: 100% pass rate required (CRUD operations)
- **Overall**: 95% pass rate required
- **Performance**: Response time < 3 seconds
- **No Critical/High severity defects**

### Key Features to Validate
- **CRUD Operations**: Create, Read, Update, Delete projects
- **Task Management**: Add, edit, delete tasks within projects
- **Worker Assignment**: Assign/remove workers to/from projects
- **Status Management**: Update project status correctly
- **Calendar Integration**: Project dates display correctly
- **Data Validation**: All form validations work properly
- **Security**: CSRF protection and input sanitization

### Defects Found
[To be documented during test execution]

### Recommendations
[To be provided after test completion]

---

**Test Executed By**: [Tester Name]
**Test Execution Date**: [Date]
**Review Date**: [Date]
**Approved By**: [Approver Name]
