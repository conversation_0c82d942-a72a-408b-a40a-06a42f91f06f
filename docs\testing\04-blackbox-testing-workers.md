# Black Box Testing - Workers Management Module

## Overview
Dokumentasi testing untuk modul manajemen pekerja aplikasi DisaCloud05-v4, mencakup CRUD operations, project assignment, dan skill management.

## Test Environment
- **Application**: DisaCloud05-v4
- **Module**: Workers Management
- **Test Type**: Black Box Testing
- **Base URL**: `/workers`
- **Prerequisites**: User harus sudah login

## Test Cases

### 1. Workers Index Page Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_001 | Akses halaman workers index | URL: `/workers` | Menampilkan daftar semua pekerja | | |
| TC_WORK_002 | Display worker information | Workers dengan data lengkap | Menampilkan nama, spesialisasi, email, phone | | |
| TC_WORK_003 | Display assigned projects | Workers dengan project assignments | Menampilkan daftar proyek yang di-assign | | |
| TC_WORK_004 | Empty state - no workers | No workers in database | Menampilkan empty state message | | |
| TC_WORK_005 | Worker sorting | Default sorting | Workers diurutkan berdasarkan created_at descending | | |

### 2. Create Worker Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_006 | Akses form create worker | URL: `/workers/create` | Form create worker ditampilkan | | |
| TC_WORK_007 | Create worker dengan data valid | Name: `John Doe`<br>Specialization: `Carpenter`<br>Email: `<EMAIL>`<br>Phone: `081234567890`<br>Skills: `Wood working, Furniture` | Worker berhasil dibuat, redirect ke workers index | | |
| TC_WORK_008 | Create worker dengan nama kosong | Name: ` `<br>Other fields: valid | Error: "Name field is required" | | |
| TC_WORK_009 | Create worker dengan spesialisasi kosong | Specialization: ` `<br>Other fields: valid | Error: "Specialization field is required" | | |
| TC_WORK_010 | Create worker dengan email kosong | Email: ` `<br>Other fields: valid | Error: "Email field is required" | | |
| TC_WORK_011 | Create worker dengan email invalid | Email: `invalid-email`<br>Other fields: valid | Error: "Please enter a valid email" | | |
| TC_WORK_012 | Create worker dengan email duplicate | Email: existing worker email<br>Other fields: valid | Error: "Email already exists" | | |
| TC_WORK_013 | Create worker dengan phone invalid | Phone: `invalid-phone`<br>Other fields: valid | Error atau phone disimpan as-is | | |
| TC_WORK_014 | Create worker dengan project assignment | Select projects during creation | Worker dibuat dan di-assign ke projects | | |
| TC_WORK_015 | Create worker tanpa project assignment | No projects selected | Worker dibuat tanpa project assignment | | |

### 3. Edit Worker Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_016 | Akses form edit worker | URL: `/workers/{id}/edit` | Form edit dengan data existing ditampilkan | | |
| TC_WORK_017 | Update worker dengan data valid | Update name, specialization, skills | Worker berhasil diupdate | | |
| TC_WORK_018 | Update worker email | Change email to new valid email | Email berhasil diupdate | | |
| TC_WORK_019 | Update worker email ke existing email | Change email to existing worker email | Error: "Email already exists" | | |
| TC_WORK_020 | Update worker skills | Add/modify skills text | Skills berhasil diupdate | | |
| TC_WORK_021 | Update worker project assignments | Add/remove project assignments | Project assignments berhasil diupdate | | |
| TC_WORK_022 | Update worker dengan validation error | Invalid data (e.g., empty name) | Error message ditampilkan, data tidak tersimpan | | |

### 4. Delete Worker Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_023 | Delete worker confirmation | Click delete button | Confirmation dialog ditampilkan | | |
| TC_WORK_024 | Confirm delete worker | Confirm deletion | Worker berhasil dihapus, redirect ke index | | |
| TC_WORK_025 | Cancel delete worker | Cancel deletion | Worker tidak dihapus, tetap di halaman | | |
| TC_WORK_026 | Delete worker dengan project assignments | Worker assigned to projects | Worker dihapus, assignments diremove | | |

### 5. Project Assignment Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_027 | View worker project assignments | Worker dengan assignments | Menampilkan daftar assigned projects | | |
| TC_WORK_028 | Assign worker to single project | Select one project | Worker berhasil di-assign ke project | | |
| TC_WORK_029 | Assign worker to multiple projects | Select multiple projects | Worker berhasil di-assign ke semua projects | | |
| TC_WORK_030 | Remove worker from project | Unselect assigned project | Worker berhasil diremove dari project | | |
| TC_WORK_031 | View available projects | Open project selection | Menampilkan semua available projects | | |

### 6. Worker Skills Management Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_032 | Add worker skills | Skills: `Welding, Metal work` | Skills berhasil disimpan | | |
| TC_WORK_033 | Update worker skills | Modify existing skills | Skills berhasil diupdate | | |
| TC_WORK_034 | Clear worker skills | Remove all skills | Skills field kosong | | |
| TC_WORK_035 | Skills with special characters | Skills: `C++, .NET, HTML/CSS` | Skills dengan special characters tersimpan | | |
| TC_WORK_036 | Long skills text | Skills: `[Very long text > 1000 chars]` | Skills tersimpan atau error jika melebihi limit | | |

### 7. Worker Search & Filter Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_037 | Search worker by name | Search: `"John"` | Menampilkan workers dengan nama mengandung "John" | | |
| TC_WORK_038 | Search worker by specialization | Search: `"Carpenter"` | Menampilkan workers dengan spesialisasi Carpenter | | |
| TC_WORK_039 | Search with no results | Search: `"NonExistent"` | Menampilkan "No workers found" | | |
| TC_WORK_040 | Filter by project assignment | Filter: assigned to specific project | Menampilkan workers assigned to project | | |
| TC_WORK_041 | Filter by skills | Filter: workers with specific skills | Menampilkan workers dengan skills yang sesuai | | |

### 8. Worker Validation Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_042 | Required field validation | Submit form dengan field kosong | Error messages untuk required fields | | |
| TC_WORK_043 | Email format validation | Invalid email formats | Error: "Please enter a valid email" | | |
| TC_WORK_044 | Name length validation | Name dengan 255+ characters | Error atau truncation | | |
| TC_WORK_045 | Phone format validation | Various phone formats | Validation atau format normalization | | |

### 9. Worker Performance Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_046 | Load workers index | Large dataset (1000+ workers) | Page load time < 3 seconds | | |
| TC_WORK_047 | Create worker response time | Submit create form | Response time < 2 seconds | | |
| TC_WORK_048 | Update worker response time | Submit update form | Response time < 2 seconds | | |
| TC_WORK_049 | Delete worker response time | Confirm delete | Response time < 1 second | | |
| TC_WORK_050 | Search response time | Perform search | Search results < 1 second | | |

### 10. Worker Security Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_051 | Unauthorized access to workers | Access without login | Redirect ke login page | | |
| TC_WORK_052 | CSRF protection on create | Submit without CSRF token | Request rejected | | |
| TC_WORK_053 | CSRF protection on update | Submit without CSRF token | Request rejected | | |
| TC_WORK_054 | CSRF protection on delete | Submit without CSRF token | Request rejected | | |
| TC_WORK_055 | SQL injection on search | Search: `'; DROP TABLE workers; --` | Input disanitasi, no SQL injection | | |
| TC_WORK_056 | XSS on worker name | Name: `<script>alert('XSS')</script>` | Input disanitasi, script tidak dieksekusi | | |

### 11. Worker UI/UX Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_057 | Responsive design - mobile | Screen: 375x667 | Layout responsive, form dapat digunakan | | |
| TC_WORK_058 | Responsive design - tablet | Screen: 768x1024 | Layout responsive, table dapat di-scroll | | |
| TC_WORK_059 | Form field labels | All form fields | Labels jelas dan descriptive | | |
| TC_WORK_060 | Error message clarity | Various validation errors | Error messages jelas dan actionable | | |
| TC_WORK_061 | Success message display | Successful operations | Success messages ditampilkan dengan jelas | | |
| TC_WORK_062 | Loading indicators | Form submissions | Loading indicators ditampilkan | | |

### 12. Worker Integration Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_WORK_063 | Worker-Project integration | Assign worker to project | Worker muncul di project team | | |
| TC_WORK_064 | Worker-Calendar integration | Worker assignments | Worker activities muncul di calendar | | |
| TC_WORK_065 | Worker deletion impact | Delete worker dengan assignments | Project assignments ter-remove | | |
| TC_WORK_066 | Worker skills display | Worker dengan skills | Skills ditampilkan di project assignment | | |

## Test Summary

### Test Execution Summary
- **Total Test Cases**: 66
- **Passed**: [To be filled]
- **Failed**: [To be filled]
- **Blocked**: [To be filled]
- **Not Executed**: [To be filled]

### Pass/Fail Criteria
- **Critical Functions**: 100% pass rate required (CRUD operations)
- **Overall**: 95% pass rate required
- **Performance**: Response time < 3 seconds
- **No Critical/High severity defects**

### Key Features to Validate
- **CRUD Operations**: Create, Read, Update, Delete workers
- **Project Assignment**: Assign/remove workers to/from projects
- **Skills Management**: Add, edit, display worker skills
- **Email Uniqueness**: Prevent duplicate email addresses
- **Data Validation**: All form validations work properly
- **Integration**: Worker data integrates with projects and calendar

### Defects Found
[To be documented during test execution]

### Recommendations
[To be provided after test completion]

---

**Test Executed By**: [Tester Name]  
**Test Execution Date**: [Date]  
**Review Date**: [Date]  
**Approved By**: [Approver Name]
