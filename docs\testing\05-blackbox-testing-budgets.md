# Black Box Testing - Budget Management Module

## Overview
Dokumentasi testing untuk modul manajemen budget aplikasi DisaCloud05-v4, mencakup CRUD operations, profit calculation, invoice management, dan budget comparison.

## Test Environment
- **Application**: DisaCloud05-v4
- **Module**: Budget Management
- **Test Type**: Black Box Testing
- **Base URL**: `/budgets`
- **Prerequisites**: User harus sudah login, projects tersedia

## Test Cases

### 1. Budget Index Page Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_001 | Akses halaman budgets index | URL: `/budgets` | Menampilkan daftar semua budget | | |
| TC_BUDG_002 | Display budget information | Budgets dengan data lengkap | Menampilkan project, amount, profit, status | | |
| TC_BUDG_003 | Budget status indicators | Budgets dengan berbagai status | Status ditampilkan dengan color coding | | |
| TC_BUDG_004 | Empty state - no budgets | No budgets in database | Menampilkan empty state message | | |
| TC_BUDG_005 | Budget sorting | Default sorting | Budgets diurutkan berdasarkan created_at descending | | |

### 2. Create Budget Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_006 | Akses form create budget | URL: `/budgets/create` | Form create budget ditampilkan | | |
| TC_BUDG_007 | Create budget dengan data valid | Project: `Project A`<br>Amount: `10000000`<br>Estimated Profit: `2000000`<br>Description: `Budget for Project A`<br>Budget Date: `2025-07-01`<br>Status: `pending` | Budget berhasil dibuat, profit percentage dihitung otomatis | | |
| TC_BUDG_008 | Create budget dengan project kosong | Project: ` `<br>Other fields: valid | Error: "Project field is required" | | |
| TC_BUDG_009 | Create budget dengan amount kosong | Amount: ` `<br>Other fields: valid | Error: "Amount field is required" | | |
| TC_BUDG_010 | Create budget dengan amount negatif | Amount: `-1000000`<br>Other fields: valid | Error: "Amount must be greater than or equal to 0" | | |
| TC_BUDG_011 | Create budget dengan estimated profit negatif | Estimated Profit: `-500000`<br>Other fields: valid | Error: "Estimated profit must be greater than or equal to 0" | | |
| TC_BUDG_012 | Create budget dengan description kosong | Description: ` `<br>Other fields: valid | Error: "Description field is required" | | |
| TC_BUDG_013 | Create budget dengan description > 500 chars | Description: `[501 characters]`<br>Other fields: valid | Error: "Description may not be greater than 500 characters" | | |
| TC_BUDG_014 | Create budget dengan invalid date | Budget Date: `invalid-date`<br>Other fields: valid | Error: "Please enter a valid date" | | |
| TC_BUDG_015 | Create budget dengan invoice file | Upload PDF file < 10MB | Budget dibuat dengan invoice file tersimpan | | |
| TC_BUDG_016 | Create budget dengan invalid file type | Upload non-PDF file | Error: "Invoice must be a PDF file" | | |
| TC_BUDG_017 | Create budget dengan file > 10MB | Upload PDF file > 10MB | Error: "Invoice file may not be greater than 10MB" | | |

### 3. Budget Profit Calculation Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_018 | Profit percentage calculation | Amount: `10000000`<br>Estimated Profit: `2000000` | Profit percentage = 20% | | |
| TC_BUDG_019 | Profit percentage dengan amount 0 | Amount: `0`<br>Estimated Profit: `1000000` | Profit percentage = 0% | | |
| TC_BUDG_020 | Profit percentage dengan profit 0 | Amount: `10000000`<br>Estimated Profit: `0` | Profit percentage = 0% | | |
| TC_BUDG_021 | Profit percentage dengan decimal | Amount: `7500000`<br>Estimated Profit: `1125000` | Profit percentage = 15% | | |

### 4. Edit Budget Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_022 | Akses form edit budget | URL: `/budgets/{id}/edit` | Form edit dengan data existing ditampilkan | | |
| TC_BUDG_023 | Update budget dengan data valid | Update amount, profit, description | Budget berhasil diupdate, profit percentage recalculated | | |
| TC_BUDG_024 | Update budget status | Change status from `pending` to `approved` | Status berhasil diupdate | | |
| TC_BUDG_025 | Update budget dengan validation error | Invalid data (e.g., negative amount) | Error message ditampilkan, data tidak tersimpan | | |
| TC_BUDG_026 | Replace invoice file | Upload new PDF file | Invoice file berhasil diganti | | |
| TC_BUDG_027 | Remove invoice file | Remove existing invoice | Invoice file berhasil dihapus | | |

### 5. Budget Detail View Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_028 | View budget detail | URL: `/budgets/{id}` | Detail budget ditampilkan lengkap | | |
| TC_BUDG_029 | Budget usage calculation | Budget dengan materials | Usage percentage dihitung berdasarkan materials cost | | |
| TC_BUDG_030 | Remaining amount calculation | Budget dengan spending | Remaining amount = budget - total spending | | |
| TC_BUDG_031 | Materials list in budget detail | Budget dengan materials | Menampilkan daftar materials terkait | | |
| TC_BUDG_032 | Download invoice from detail | Budget dengan invoice file | Invoice file dapat didownload | | |

### 6. Budget Comparison Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_033 | Akses budget comparison | URL: `/budgets/compare` | Halaman comparison ditampilkan | | |
| TC_BUDG_034 | Compare budget vs actual | Select projects untuk comparison | Menampilkan perbandingan budget vs actual spending | | |
| TC_BUDG_035 | Variance calculation | Budget vs actual dengan selisih | Variance dihitung dan ditampilkan dengan benar | | |
| TC_BUDG_036 | Comparison chart display | Data comparison | Chart perbandingan ditampilkan | | |
| TC_BUDG_037 | Export comparison report | Click export button | Report comparison ter-export | | |
| TC_BUDG_038 | Filter comparison by date range | Select date range | Comparison filtered berdasarkan date range | | |

### 7. Invoice Management Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_039 | Upload invoice file | Valid PDF file < 10MB | Invoice berhasil diupload dan tersimpan | | |
| TC_BUDG_040 | Download invoice file | Budget dengan invoice | Invoice file berhasil didownload | | |
| TC_BUDG_041 | View invoice in browser | Budget dengan invoice | Invoice dapat dibuka di browser | | |
| TC_BUDG_042 | Replace existing invoice | Upload new invoice file | Invoice lama diganti dengan yang baru | | |
| TC_BUDG_043 | Delete invoice file | Remove invoice from budget | Invoice file berhasil dihapus | | |

### 8. Delete Budget Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_044 | Delete budget confirmation | Click delete button | Confirmation dialog ditampilkan | | |
| TC_BUDG_045 | Confirm delete budget | Confirm deletion | Budget berhasil dihapus, redirect ke index | | |
| TC_BUDG_046 | Cancel delete budget | Cancel deletion | Budget tidak dihapus, tetap di halaman | | |
| TC_BUDG_047 | Delete budget dengan invoice | Budget dengan invoice file | Budget dan invoice file terhapus | | |

### 9. Budget Validation Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_048 | Required field validation | Submit form dengan field kosong | Error messages untuk required fields | | |
| TC_BUDG_049 | Numeric field validation | Non-numeric amount/profit values | Error: "Field must be a number" | | |
| TC_BUDG_050 | Date format validation | Invalid date format | Error: "Please enter a valid date" | | |
| TC_BUDG_051 | File type validation | Upload non-PDF file | Error: "Invoice must be a PDF file" | | |
| TC_BUDG_052 | File size validation | Upload file > 10MB | Error: "File may not be greater than 10MB" | | |

### 10. Budget Search & Filter Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_053 | Search budget by project | Search: project name | Menampilkan budgets untuk project tersebut | | |
| TC_BUDG_054 | Filter budget by status | Filter: `approved` | Menampilkan hanya budgets dengan status approved | | |
| TC_BUDG_055 | Filter budget by date range | Date range: last month | Menampilkan budgets dalam range tersebut | | |
| TC_BUDG_056 | Search with no results | Search: non-existent project | Menampilkan "No budgets found" | | |

### 11. Budget Performance Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_057 | Load budgets index | Large dataset (1000+ budgets) | Page load time < 3 seconds | | |
| TC_BUDG_058 | Create budget response time | Submit create form | Response time < 2 seconds | | |
| TC_BUDG_059 | Update budget response time | Submit update form | Response time < 2 seconds | | |
| TC_BUDG_060 | Invoice upload response time | Upload 5MB PDF file | Upload time < 10 seconds | | |
| TC_BUDG_061 | Budget comparison load time | Generate comparison report | Report generation < 5 seconds | | |

### 12. Budget Security Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_062 | Unauthorized access to budgets | Access without login | Redirect ke login page | | |
| TC_BUDG_063 | CSRF protection on create | Submit without CSRF token | Request rejected | | |
| TC_BUDG_064 | CSRF protection on update | Submit without CSRF token | Request rejected | | |
| TC_BUDG_065 | CSRF protection on delete | Submit without CSRF token | Request rejected | | |
| TC_BUDG_066 | File upload security | Upload malicious file | File rejected atau disanitasi | | |
| TC_BUDG_067 | SQL injection on search | Search: `'; DROP TABLE budgets; --` | Input disanitasi, no SQL injection | | |

### 13. Budget UI/UX Testing

| Test Case ID | Skenario Testing | Test Data | Expected Result | Actual Result | Status |
|--------------|------------------|-----------|-----------------|---------------|--------|
| TC_BUDG_068 | Responsive design - mobile | Screen: 375x667 | Layout responsive, form dapat digunakan | | |
| TC_BUDG_069 | Responsive design - tablet | Screen: 768x1024 | Layout responsive, table dapat di-scroll | | |
| TC_BUDG_070 | Form field labels | All form fields | Labels jelas dan descriptive | | |
| TC_BUDG_071 | Error message clarity | Various validation errors | Error messages jelas dan actionable | | |
| TC_BUDG_072 | Success message display | Successful operations | Success messages ditampilkan dengan jelas | | |

## Test Summary

### Test Execution Summary
- **Total Test Cases**: 72
- **Passed**: [To be filled]
- **Failed**: [To be filled]
- **Blocked**: [To be filled]
- **Not Executed**: [To be filled]

### Pass/Fail Criteria
- **Critical Functions**: 100% pass rate required (CRUD, calculations)
- **Overall**: 95% pass rate required
- **Performance**: Response time < 3 seconds
- **File Upload**: < 10 seconds for 5MB files
- **No Critical/High severity defects**

### Key Features to Validate
- **CRUD Operations**: Create, Read, Update, Delete budgets
- **Profit Calculation**: Automatic profit percentage calculation
- **Invoice Management**: Upload, download, replace invoice files
- **Budget Comparison**: Compare budget vs actual spending
- **Usage Tracking**: Calculate budget usage based on materials
- **Data Validation**: All form validations work properly
- **File Security**: Secure file upload and storage

### Defects Found
[To be documented during test execution]

### Recommendations
[To be provided after test completion]

---

**Test Executed By**: [Tester Name]  
**Test Execution Date**: [Date]  
**Review Date**: [Date]  
**Approved By**: [Approver Name]
