@startuml Use Case Diagram - Logout Activity
!theme plain
skinparam backgroundColor #FFFFFF
skinparam actorBorderColor #2563EB
skinparam actorBackgroundColor #EFF6FF
skinparam usecaseBorderColor #1D4ED8
skinparam usecaseBackgroundColor #DBEAFE
skinparam packageBorderColor #1E40AF
skinparam packageBackgroundColor #F8FAFC

left to right direction

actor "Project Manager\n/ Site Leader" as PM

package "DisaCloud05-v4 System" {
    
    package "Authentication Module" {
        usecase "Akses Menu Logout" as UC1
        usecase "Verifikasi CSRF Token" as UC2
        usecase "Hapus Session Data" as UC3
        usecase "Invalidate Authentication" as UC4
        usecase "Generate New CSRF Token" as UC5
        usecase "Redirect ke Landing Page" as UC6
    }
    
    package "Security Module" {
        usecase "Validate User Session" as UC7
        usecase "Clear Authentication State" as UC8
        usecase "Destroy Session Storage" as UC9
    }
    
    package "Navigation Module" {
        usecase "Show Landing Page" as UC10
        usecase "Block Protected Routes" as UC11
    }
}

' Primary Flow
PM --> UC1 : Click Logout Button
UC1 --> UC2 : <<include>>
UC2 --> UC7 : <<include>>
UC7 --> UC4 : <<include>>
UC4 --> UC8 : <<include>>
UC8 --> UC3 : <<include>>
UC3 --> UC9 : <<include>>
UC9 --> UC5 : <<include>>
UC5 --> UC6 : <<include>>
UC6 --> UC10 : <<include>>

' Security Extensions
UC4 --> UC11 : <<extend>>

note right of UC1
**Primary Scenario:**
1. User clicks logout button/link
2. System verifies CSRF token
3. System validates current session
4. System clears authentication state
5. System destroys session data
6. System generates new CSRF token
7. System redirects to landing page
8. User sees landing page (logged out)
end note

note right of UC2
**CSRF Protection:**
- Validates POST request token
- Prevents CSRF attacks
- Required for logout action
end note

note right of UC3
**Session Management:**
- session()->invalidate()
- Removes all session data
- Clears user authentication
end note

note right of UC8
**Authentication State:**
- Auth::logout()
- Clears Laravel auth state
- Removes user from session
end note

note right of UC11
**Route Protection:**
- Middleware blocks access
- Redirects to login page
- Maintains security after logout
end note

@enduml
