# Projects User Flow - DisaCloud05-v4

## Overview

Halaman Projects adalah inti dari aplikasi yang memungkinkan user mengelola semua proyek konstruksi. User dapat melakukan CRUD operations, filtering, tracking progress, dan navigasi ke detail proyek. Halaman ini menggunakan card-based layout yang responsive dan user-friendly.

## User Flows

### 🔴 HIGH PRIORITY - Melihat Daftar Semua Proyek

#### Deskripsi
User melihat overview semua proyek dalam bentuk card grid dengan informasi essential dan status visual.

#### Langkah-langkah
1. User mengaks<PERSON> halaman "Semua Proyek" dari sidebar
2. System menampilkan grid layout (1 col mobile, 2 col tablet, 3 col desktop)
3. Setiap project card menampilkan:
   - Nama proyek (header)
   - Status badge dengan color coding:
     - Completed: hijau (bg-green-100 text-green-800)
     - In Progress: biru (bg-blue-100 text-blue-800)
     - On Hold: kuning (bg-yellow-100 text-yellow-800)
     - Planned: abu-abu (bg-gray-100 text-gray-800)
   - Deskripsi proyek
   - Tanggal mulai dan selesai
   - Action buttons (Edit, <PERSON><PERSON><PERSON>, Hapus)
4. User dapat scroll untuk melihat semua proyek

#### Hasil yang Diharapkan
- Overview visual yang jelas dari semua proyek
- Status yang mudah diidentifikasi melalui color coding
- Layout responsive di semua device
- Loading yang smooth

#### Kondisi Error/Edge Cases
- Jika tidak ada proyek: tampilkan empty state dengan CTA "Tambah Proyek Baru"
- Jika loading lambat: tampilkan skeleton cards
- Jika error database: tampilkan error message dengan retry button

#### Dependencies
- User sudah login
- Database connection available
- Projects table accessible

---

### 🔴 HIGH PRIORITY - Filter Proyek Berdasarkan Status

#### Deskripsi
User dapat memfilter proyek berdasarkan status untuk fokus pada kategori tertentu.

#### Langkah-langkah
1. User melihat filter section di atas grid proyek
2. User mengklik dropdown "Filter Status"
3. Pilihan yang tersedia:
   - Semua Status (default)
   - Sedang Berjalan
   - Direncanakan
   - Selesai
   - Ditunda
4. User memilih status, form auto-submit
5. Page reload dengan proyek yang difilter
6. Filter state tetap tersimpan di URL

#### Hasil yang Diharapkan
- Filter berfungsi real-time
- URL parameter ter-update
- State filter persistent saat refresh
- Visual feedback saat filtering

#### Kondisi Error/Edge Cases
- Jika tidak ada proyek dengan status tertentu: tampilkan pesan "Tidak ada proyek dengan status ini"
- Jika filter error: reset ke "Semua Status"

#### Dependencies
- JavaScript enabled untuk auto-submit
- Server-side filtering logic
- URL parameter handling

---

### 🔴 HIGH PRIORITY - Membuat Proyek Baru

#### Deskripsi
User membuat proyek baru dengan form komprehensif yang mencakup detail proyek, budget, dan tasks.

#### Langkah-langkah
1. User mengklik "Tambah Proyek Baru" button
2. System redirect ke halaman create project
3. User mengisi form dengan sections:
   - **Informasi Dasar**:
     - Nama proyek (required)
     - Deskripsi (optional)
     - Status (dropdown: planned, in_progress, completed, on_hold)
   - **Timeline**:
     - Tanggal mulai (date picker)
     - Tanggal selesai (date picker, harus >= start_date)
   - **Budget**:
     - Budget Material (numeric, min: 0)
     - Budget Jasa (numeric, min: 0)
   - **Tasks** (dynamic section):
     - Nama task (required jika ada)
     - Deskripsi task (optional)
     - Button "Tambah Task Lain" untuk multiple tasks
4. User mengklik "Simpan Proyek"
5. System validasi data
6. Jika valid: create project, auto-sync budget entries, create tasks
7. Redirect ke projects index dengan success message

#### Hasil yang Diharapkan
- Form validation yang comprehensive
- Auto-sync budget entries ke budget table
- Tasks ter-create dengan status default "pending"
- Success feedback yang jelas

#### Kondisi Error/Edge Cases
- Validation errors: tampilkan error di field yang bermasalah
- Date validation: end_date harus >= start_date
- Jika save gagal: tampilkan error message, preserve form data

#### Dependencies
- Form validation rules
- Budget sync logic
- Task creation logic
- Date validation

---

### 🔴 HIGH PRIORITY - Edit Proyek Existing

#### Deskripsi
User mengedit proyek yang sudah ada dengan kemampuan update semua field dan manage tasks.

#### Langkah-langkah
1. User mengklik "Edit" pada project card
2. System redirect ke edit form dengan data pre-filled
3. Form sections sama dengan create, plus:
   - **Existing Tasks Management**:
     - List tasks yang sudah ada
     - Edit nama/deskripsi existing tasks
     - Delete tasks (dengan confirmation)
     - Add new tasks
   - **Worker Assignment** (jika ada):
     - List workers yang sudah assigned
     - Available workers untuk assignment
4. User melakukan perubahan
5. User mengklik "Update Proyek"
6. System validasi dan update data
7. Auto-sync budget jika budget fields berubah
8. Update/create/delete tasks sesuai perubahan
9. Redirect dengan success message

#### Hasil yang Diharapkan
- Pre-filled form dengan data existing
- Task management yang flexible
- Budget re-sync jika diperlukan
- Preserve relationships (workers, etc.)

#### Kondisi Error/Edge Cases
- Jika proyek tidak ditemukan: 404 error
- Validation errors: tampilkan di field yang bermasalah
- Jika update gagal: preserve changes, tampilkan error

#### Dependencies
- Project exists dan accessible
- Task CRUD operations
- Budget sync logic
- Worker relationship management

---

### 🔴 HIGH PRIORITY - Mark Proyek Sebagai Selesai

#### Deskripsi
User dapat menandai proyek sebagai completed dengan confirmation untuk mencegah accident.

#### Langkah-langkah
1. User mengklik "Selesai" button pada project card
2. System tampilkan confirmation dialog:
   "Apakah Anda yakin ingin menandai proyek ini sebagai selesai?"
3. User konfirmasi atau cancel
4. Jika konfirmasi: system update status ke "completed"
5. Page refresh dengan updated status
6. Success message ditampilkan

#### Hasil yang Diharapkan
- Confirmation dialog mencegah accident
- Status update yang immediate
- Visual feedback yang jelas
- Button "Selesai" hilang setelah completed

#### Kondisi Error/Edge Cases
- Jika update gagal: tampilkan error message
- Jika proyek sudah completed: button tidak tampil

#### Dependencies
- JavaScript untuk confirmation dialog
- Status update logic
- UI state management

---

### 🟡 MEDIUM PRIORITY - Delete Proyek

#### Deskripsi
User dapat menghapus proyek dengan confirmation dan cascade delete untuk related data.

#### Langkah-langkah
1. User mengklik "Hapus" button pada project card
2. System tampilkan confirmation dialog:
   "Apakah Anda yakin ingin menghapus proyek ini? Semua data terkait akan ikut terhapus."
3. User konfirmasi atau cancel
4. Jika konfirmasi: system delete project dan related data:
   - Tasks
   - Budget entries
   - Materials
   - Worker assignments
   - Daily reports
5. Page refresh tanpa deleted project
6. Success message ditampilkan

#### Hasil yang Diharapkan
- Strong confirmation untuk destructive action
- Cascade delete yang proper
- Clean removal dari UI
- Clear success feedback

#### Kondisi Error/Edge Cases
- Jika delete gagal: tampilkan error message
- Jika ada foreign key constraints: handle gracefully

#### Dependencies
- Cascade delete logic
- Foreign key handling
- Confirmation dialog

---

### 🟡 MEDIUM PRIORITY - Navigasi ke Detail Proyek

#### Deskripsi
User dapat mengakses detail proyek untuk melihat informasi lengkap dan sub-modules.

#### Langkah-langkah
1. User mengklik nama proyek atau area card (bukan action buttons)
2. System redirect ke project detail page
3. Detail page menampilkan:
   - Project overview
   - Tasks list
   - Materials list
   - Budget information
   - Daily reports
   - Worker assignments
   - Progress tracking
4. User dapat navigate kembali ke projects index

#### Hasil yang Diharapkan
- Seamless navigation ke detail
- Comprehensive project information
- Easy return navigation
- Related data loading

#### Kondisi Error/Edge Cases
- Jika proyek tidak ditemukan: 404 page
- Jika related data loading gagal: partial view dengan error sections

#### Dependencies
- Project detail route dan controller
- Related data relationships
- Navigation breadcrumbs

---

### 🟡 MEDIUM PRIORITY - Responsive Layout Interaction

#### Deskripsi
User menggunakan projects page di berbagai device dengan layout yang optimal.

#### Langkah-langkah
1. **Desktop (lg+)**: 3-column grid layout
2. **Tablet (md)**: 2-column grid layout
3. **Mobile (sm)**: Single column layout
4. Action buttons tetap accessible di semua breakpoints
5. Filter section responsive dengan proper spacing

#### Hasil yang Diharapkan
- Optimal viewing di semua device sizes
- Touch-friendly buttons pada mobile
- Readable text di screen kecil
- Proper spacing dan alignment

#### Kondisi Error/Edge Cases
- Jika CSS tidak load: fallback ke basic layout
- Jika screen sangat kecil: stack elements vertically

#### Dependencies
- TailwindCSS responsive classes
- Proper viewport meta tag
- Touch-friendly design

---

### 🟢 LOW PRIORITY - Bulk Operations

#### Deskripsi
User dapat melakukan operasi pada multiple proyek sekaligus (future enhancement).

#### Langkah-langkah
1. User mengaktifkan "Selection Mode"
2. Checkbox muncul pada setiap project card
3. User select multiple projects
4. Bulk action buttons muncul:
   - Mark as Completed
   - Change Status
   - Export Data
5. User pilih action dan konfirmasi
6. System proses bulk operation

#### Hasil yang Diharapkan
- Efficient bulk operations
- Clear selection state
- Batch processing feedback
- Undo capability untuk safety

#### Kondisi Error/Edge Cases
- Jika sebagian operasi gagal: tampilkan partial success report
- Jika semua gagal: rollback dan error message

#### Dependencies
- Selection state management
- Bulk operation logic
- Progress indicators

---

### 🟢 LOW PRIORITY - Search dan Advanced Filtering

#### Deskripsi
User dapat mencari proyek berdasarkan nama/deskripsi dan filter advanced.

#### Langkah-langkah
1. User menggunakan search box untuk text search
2. User dapat combine dengan status filter
3. Advanced filters:
   - Date range (start/end date)
   - Budget range
   - Progress percentage
4. Real-time filtering saat user mengetik
5. Clear filters option

#### Hasil yang Diharapkan
- Fast search response
- Multiple filter combinations
- Clear filter state
- Search highlighting

#### Kondisi Error/Edge Cases
- Jika search tidak menemukan hasil: empty state dengan suggestions
- Jika filter terlalu restrictive: pesan "Coba perluas kriteria filter"

#### Dependencies
- Search indexing
- Advanced filtering logic
- Debounced search input

## Navigation Patterns

### Primary Navigation
- Sidebar menu untuk akses utama
- "Tambah Proyek Baru" button sebagai primary CTA
- Project cards sebagai entry points ke detail

### Secondary Navigation
- Filter dropdown untuk kategorisasi
- Action buttons pada setiap card
- Breadcrumb navigation pada detail pages

### Return Patterns
- "Kembali ke Daftar" button pada create/edit forms
- Browser back button support
- Logo click untuk return to dashboard

## Data Flow Patterns

### Create Flow
Projects Index → Create Form → Validation → Database Insert → Budget Sync → Task Creation → Success Redirect

### Update Flow
Projects Index → Edit Form → Pre-fill Data → Validation → Database Update → Related Data Sync → Success Redirect

### Delete Flow
Projects Index → Confirmation → Cascade Delete → UI Update → Success Message

## Performance Considerations

### Loading Optimization
- Lazy loading untuk large project lists
- Skeleton loading untuk better UX
- Pagination untuk scalability

### Caching Strategy
- Cache project lists untuk 5 menit
- Invalidate cache saat ada perubahan
- Client-side caching untuk filter states

### Error Handling
- Graceful degradation untuk network issues
- Retry mechanisms untuk failed operations
- Clear error messages dengan recovery options
