# Workers User Flow - DisaCloud05-v4

## Overview

Halaman Workers memungkinkan user mengelola database pekerja konstruksi, termasuk informasi personal, spesialisasi, skills, dan assignment ke proyek. Halaman ini menggunakan list-based layout dengan filtering dan search capabilities yang powerful.

## User Flows

### 🔴 HIGH PRIORITY - Melihat Daftar Semua Pekerja

#### Deskripsi
User melihat daftar lengkap semua pekerja dalam format list dengan informasi essential dan status assignment.

#### Langkah-langkah
1. User mengakses halaman "Workers" dari sidebar
2. System menampilkan list layout dengan header informasi:
   - "Daftar Pekerja" sebagai title
   - "Kelola semua pekerja yang terlibat dalam proyek" sebagai subtitle
   - "Tambah Pekerja" button di kanan atas
3. Setiap worker item menampilkan:
   - Nama pekerja (link biru)
   - Spesialisasi
   - Status badge (Aktif/Tidak Aktif) berdasarkan end_date
   - Email dan phone (jika ada)
   - Skills yang dimiliki
   - Proyek yang sedang ditugaskan
   - Action buttons (Edit, Delete)
4. List menggunakan divide-y styling untuk separation

#### Hasil yang Diharapkan
- List yang clean dan readable
- Status visual yang jelas (aktif/tidak aktif)
- Informasi lengkap dalam format compact
- Quick access ke actions

#### Kondisi Error/Edge Cases
- Jika tidak ada pekerja: tampilkan empty state dengan CTA "Tambah Pekerja Pertama"
- Jika loading lambat: tampilkan skeleton list items
- Jika error database: tampilkan error message dengan retry

#### Dependencies
- User sudah login
- Workers table accessible
- Relationship dengan projects loaded

---

### 🔴 HIGH PRIORITY - Filter dan Search Pekerja

#### Deskripsi
User dapat memfilter pekerja berdasarkan status, proyek assignment, dan melakukan text search.

#### Langkah-langkah
1. User melihat filter section dengan 3 filter options:
   - **Status Filter**: Dropdown dengan pilihan:
     - Semua Status
     - Aktif (end_date null atau future)
     - Tidak Aktif (end_date sudah lewat)
   - **Project Filter**: Dropdown dengan pilihan:
     - Semua Proyek
     - [List semua proyek yang ada]
   - **Search Box**: Text input untuk search nama/spesialisasi
2. User memilih filter atau mengetik search term
3. JavaScript real-time filtering:
   - Filter by status berdasarkan end_date
   - Filter by project berdasarkan assignment
   - Search berdasarkan nama dan spesialisasi
4. List ter-update secara real-time tanpa page reload

#### Hasil yang Diharapkan
- Real-time filtering yang responsive
- Multiple filter combinations work together
- Search highlighting (optional)
- Clear filter state

#### Kondisi Error/Edge Cases
- Jika filter tidak menemukan hasil: tampilkan "Tidak ada pekerja yang sesuai kriteria"
- Jika JavaScript disabled: fallback ke server-side filtering

#### Dependencies
- JavaScript enabled untuk real-time filtering
- Project data untuk filter dropdown
- Proper data attributes pada worker items

---

### 🔴 HIGH PRIORITY - Membuat Pekerja Baru

#### Deskripsi
User membuat pekerja baru dengan form komprehensif dan langsung assign ke proyek.

#### Langkah-langkah
1. User mengklik "Tambah Pekerja" button
2. System redirect ke create form dengan sections:
   - **Informasi Personal**:
     - Nama (required)
     - Email (required, unique)
     - Phone (optional)
   - **Informasi Profesional**:
     - Spesialisasi (required)
     - Skills (textarea, optional)
   - **Assignment Proyek**:
     - Multi-select dropdown dengan Select2
     - Dapat pilih multiple proyek sekaligus
     - Optional (bisa tidak assign ke proyek apapun)
3. User mengisi form dan submit
4. System validasi:
   - Email unique validation
   - Required fields validation
   - Project existence validation
5. Jika valid: create worker dan attach ke selected projects
6. Redirect ke workers index dengan success message

#### Hasil yang Diharapkan
- Form validation yang comprehensive
- Select2 integration untuk better UX
- Multi-project assignment capability
- Clear success feedback

#### Kondisi Error/Edge Cases
- Email duplicate: tampilkan error "Email sudah digunakan"
- Validation errors: highlight field yang bermasalah
- Jika save gagal: preserve form data, tampilkan error

#### Dependencies
- Select2 library untuk multi-select
- Email uniqueness validation
- Many-to-many relationship dengan projects

---

### 🔴 HIGH PRIORITY - Edit Pekerja Existing

#### Deskripsi
User mengedit informasi pekerja dan mengubah project assignments.

#### Langkah-langkah
1. User mengklik "Edit" icon pada worker item
2. System redirect ke edit form dengan data pre-filled
3. Form sections sama dengan create, dengan data existing:
   - Personal info ter-isi
   - Current project assignments ter-select di dropdown
4. User melakukan perubahan:
   - Update personal/professional info
   - Add/remove project assignments
5. User submit form
6. System validasi (email unique kecuali untuk worker ini)
7. Update worker data dan sync project assignments
8. Redirect dengan success message

#### Hasil yang Diharapkan
- Pre-filled form dengan data existing
- Project assignments ter-load dengan benar
- Sync functionality untuk project changes
- Preserve relationships yang tidak berubah

#### Kondisi Error/Edge Cases
- Jika worker tidak ditemukan: 404 error
- Email conflict dengan worker lain: validation error
- Jika update gagal: preserve changes, tampilkan error

#### Dependencies
- Worker exists dan accessible
- Project sync logic
- Email validation excluding current worker

---

### 🔴 HIGH PRIORITY - Delete Pekerja

#### Deskripsi
User dapat menghapus pekerja dengan confirmation dan proper cleanup.

#### Langkah-langkah
1. User mengklik delete icon (trash) pada worker item
2. System tampilkan confirmation dialog:
   "Apakah Anda yakin ingin menghapus pekerja ini?"
3. User konfirmasi atau cancel
4. Jika konfirmasi: system delete worker
5. Cascade handling:
   - Remove project assignments (pivot table)
   - Keep project data intact
   - Remove worker dari semua relationships
6. Page refresh tanpa deleted worker
7. Success message ditampilkan

#### Hasil yang Diharapkan
- Strong confirmation untuk destructive action
- Proper cascade cleanup
- Projects tetap intact (hanya assignment yang dihapus)
- Clean removal dari UI

#### Kondisi Error/Edge Cases
- Jika delete gagal: tampilkan error message
- Jika worker sedang assigned ke active project: warning message

#### Dependencies
- Confirmation dialog (JavaScript)
- Cascade delete logic
- Pivot table cleanup

---

### 🟡 MEDIUM PRIORITY - Project Assignment Management

#### Deskripsi
User dapat melihat dan mengelola assignment pekerja ke proyek dari halaman workers.

#### Langkah-langkah
1. User melihat project assignments pada setiap worker item
2. Assignments ditampilkan sebagai:
   - List nama proyek yang di-assign
   - Status proyek (aktif/completed)
   - Link ke project detail (optional)
3. User dapat quick-assign melalui:
   - Edit worker untuk bulk assignment changes
   - Inline assignment controls (future enhancement)

#### Hasil yang Diharapkan
- Clear visibility of current assignments
- Easy access untuk assignment changes
- Project status context
- Quick navigation ke project details

#### Kondisi Error/Edge Cases
- Jika project sudah tidak ada: tampilkan "Project tidak ditemukan"
- Jika assignment data corrupt: handle gracefully

#### Dependencies
- Project-worker relationship data
- Project status information
- Navigation routes ke project details

---

### 🟡 MEDIUM PRIORITY - Worker Status Management

#### Deskripsi
User dapat melihat dan mengelola status aktif/tidak aktif pekerja berdasarkan end_date.

#### Langkah-langkah
1. System otomatis determine status berdasarkan end_date:
   - Aktif: end_date null atau future date
   - Tidak Aktif: end_date sudah lewat
2. Status badge dengan color coding:
   - Hijau untuk aktif
   - Abu-abu untuk tidak aktif
3. User dapat set end_date melalui edit form
4. Filter dapat memisahkan aktif/tidak aktif

#### Hasil yang Diharapkan
- Automatic status calculation
- Visual status indicators
- Easy filtering by status
- End date management capability

#### Kondisi Error/Edge Cases
- Jika end_date invalid: treat as aktif
- Jika timezone issues: use server timezone

#### Dependencies
- Carbon date handling
- End date field dalam database
- Status calculation logic

---

### 🟡 MEDIUM PRIORITY - Bulk Operations

#### Deskripsi
User dapat melakukan operasi pada multiple pekerja sekaligus (future enhancement).

#### Langkah-langkah
1. User mengaktifkan "Selection Mode"
2. Checkbox muncul pada setiap worker item
3. User select multiple workers
4. Bulk action options:
   - Assign to Project
   - Set End Date
   - Export Data
   - Delete Multiple
5. User pilih action dan konfirmasi
6. System proses bulk operation

#### Hasil yang Diharapkan
- Efficient bulk operations
- Clear selection state
- Batch processing feedback
- Undo capability untuk safety

#### Kondisi Error/Edge Cases
- Jika sebagian operasi gagal: partial success report
- Jika semua gagal: rollback dan error message

#### Dependencies
- Selection state management
- Bulk operation logic
- Progress indicators

---

### 🟢 LOW PRIORITY - Advanced Search dan Filtering

#### Deskripsi
User dapat melakukan pencarian advanced dengan multiple criteria.

#### Langkah-langkah
1. Advanced search options:
   - Search by skills/specialization
   - Filter by assignment count
   - Filter by join date range
   - Sort by various criteria
2. Saved search preferences
3. Export filtered results
4. Search result highlighting

#### Hasil yang Diharapkan
- Powerful search capabilities
- Saved preferences
- Export functionality
- Result highlighting

#### Kondisi Error/Edge Cases
- Jika search terlalu complex: performance warning
- Jika no results: suggestions untuk broaden criteria

#### Dependencies
- Advanced search logic
- User preferences storage
- Export functionality

---

### 🟢 LOW PRIORITY - Worker Performance Tracking

#### Deskripsi
User dapat melihat performance metrics untuk setiap pekerja (future enhancement).

#### Langkah-langkah
1. Performance indicators pada worker items:
   - Project completion rate
   - Average project duration
   - Skill utilization
   - Performance rating
2. Performance detail view
3. Performance-based filtering
4. Performance reports

#### Hasil yang Diharapkan
- Meaningful performance metrics
- Visual performance indicators
- Performance-based decisions
- Comprehensive reporting

#### Kondisi Error/Edge Cases
- Jika insufficient data: tampilkan "Belum cukup data"
- Jika calculation error: fallback to basic info

#### Dependencies
- Performance calculation logic
- Historical project data
- Reporting system

## Navigation Patterns

### Primary Navigation
- Sidebar menu untuk akses utama
- "Tambah Pekerja" button sebagai primary CTA
- Worker names sebagai links ke detail/edit

### Secondary Navigation
- Filter controls untuk kategorisasi
- Search box untuk quick finding
- Action icons untuk quick operations

### Return Patterns
- "Kembali" button pada create/edit forms
- Browser back button support
- Logo click untuk return to dashboard

## Data Flow Patterns

### Create Flow
Workers Index → Create Form → Validation → Database Insert → Project Assignment → Success Redirect

### Update Flow
Workers Index → Edit Form → Pre-fill Data → Validation → Database Update → Project Sync → Success Redirect

### Delete Flow
Workers Index → Confirmation → Cascade Delete → UI Update → Success Message

### Filter Flow
User Input → JavaScript Filter → DOM Manipulation → Real-time Results

## Performance Considerations

### Loading Optimization
- Eager loading untuk project relationships
- Lazy loading untuk large worker lists
- Skeleton loading untuk better UX

### Caching Strategy
- Cache worker lists untuk 10 menit
- Invalidate cache saat ada perubahan
- Client-side caching untuk filter states

### Error Handling
- Graceful degradation untuk JavaScript failures
- Retry mechanisms untuk failed operations
- Clear error messages dengan recovery options

## Integration Points

### Project Integration
- Worker assignment ke projects
- Project status visibility
- Cross-navigation ke project details

### Reporting Integration
- Worker performance dalam reports
- Assignment history tracking
- Utilization analytics
