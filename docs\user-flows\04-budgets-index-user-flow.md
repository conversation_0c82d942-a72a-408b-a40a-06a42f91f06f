# Budgets Index User Flow - DisaCloud05-v4

## Overview

Halaman Budgets Index adalah pusat manajemen budget yang menampilkan semua budget entries yang dikelompokkan berdasarkan proyek. User dapat melihat overview statistik, melakukan filtering, searching, dan mengakses berbagai operasi budget management.

## User Flows

### 🔴 HIGH PRIORITY - Melihat Overview Statistik Budget

#### Deskripsi
User melihat ringkasan statistik budget dalam bentuk cards yang memberikan insight cepat tentang kondisi finansial.

#### Langkah-langkah
1. User mengakses halaman "Budgets" dari sidebar dropdown
2. System menampilkan 4 cards statistik di bagian atas:
   - **Total Budget**: Total semua budget (warna indigo)
   - **Approved Budget**: Budget yang sudah disetujui (warna hijau)
   - **Pending Budget**: Budget yang menunggu approval (warna kuning)
   - **Rejected Budget**: Budget yang ditolak (warna merah)
3. Setiap card menampilkan nilai dalam format Rupiah
4. Cards menggunakan grid layout (4 kolom desktop, responsive)

#### Hasil yang Diharapkan
- Overview finansial yang jelas dan immediate
- Visual yang mudah dipahami dengan color coding
- Format currency yang proper (Rp xxx.xxx.xxx)
- Layout responsive di semua device

#### Kondisi Error/Edge Cases
- Jika tidak ada budget: tampilkan "Rp 0" dengan pesan informatif
- Jika calculation error: tampilkan "Data tidak tersedia"
- Jika loading lambat: tampilkan skeleton cards

#### Dependencies
- Budget data dengan status yang valid
- Calculation logic untuk aggregation
- Currency formatting helper

---

### 🔴 HIGH PRIORITY - Melihat Daftar Budget Berdasarkan Proyek

#### Deskripsi
User melihat semua budget entries yang dikelompokkan berdasarkan proyek dalam format table yang terstruktur.

#### Langkah-langkah
1. User melihat table dengan grouping berdasarkan proyek
2. Setiap group proyek memiliki:
   - Header row dengan nama proyek (background abu-abu)
   - Budget entries untuk proyek tersebut
3. Setiap budget entry menampilkan:
   - Type/kategori budget
   - Deskripsi (limited 50 karakter)
   - Amount (format Rupiah)
   - Estimated Profit (format Rupiah)
   - Profit Percentage (dengan color coding)
   - Budget Date
   - Status badge (pending/approved/rejected)
   - Invoice status (ada/tidak ada)
   - Action buttons (Detail, Edit, Hapus)
4. Table menggunakan alternating row colors untuk readability

#### Hasil yang Diharapkan
- Grouping yang jelas berdasarkan proyek
- Informasi budget yang comprehensive
- Status visual yang mudah diidentifikasi
- Action buttons yang accessible

#### Kondisi Error/Edge Cases
- Jika tidak ada budget: tampilkan empty state dengan CTA "Tambah Budget Pertama"
- Jika proyek tidak ditemukan: tampilkan "Proyek Tidak Ditemukan"
- Jika data corrupt: handle gracefully dengan fallback values

#### Dependencies
- Budget-Project relationship
- Proper grouping logic
- Status calculation dan formatting

---

### 🔴 HIGH PRIORITY - Search dan Filter Budget

#### Deskripsi
User dapat mencari budget berdasarkan deskripsi dan memfilter berdasarkan proyek dan status.

#### Langkah-langkah
1. User melihat filter section dengan 3 komponen:
   - **Search Box**: Text input untuk search deskripsi
   - **Project Filter**: Dropdown dengan semua proyek
   - **Status Filter**: Dropdown dengan status options
2. User dapat menggunakan filter secara individual atau kombinasi:
   - Search: mencari dalam deskripsi budget
   - Project: filter berdasarkan proyek tertentu
   - Status: filter berdasarkan pending/approved/rejected
3. Filter menggunakan GET parameters dan form submission
4. Results ter-update dengan URL parameters yang persistent

#### Hasil yang Diharapkan
- Search yang responsive dan akurat
- Multiple filter combinations work together
- URL parameters untuk bookmarkable results
- Clear filter state indication

#### Kondisi Error/Edge Cases
- Jika search tidak menemukan hasil: tampilkan "Tidak ada budget yang sesuai"
- Jika filter terlalu restrictive: saran untuk memperluas kriteria
- Jika form submission error: preserve filter state

#### Dependencies
- Server-side filtering logic
- URL parameter handling
- Form state management

---

### 🔴 HIGH PRIORITY - Quick Actions dari Index

#### Deskripsi
User dapat mengakses quick actions untuk operasi budget yang sering digunakan.

#### Langkah-langkah
1. User melihat action buttons di header:
   - **"Bandingkan Budget"**: Navigate ke comparison page
   - **Search Box**: Quick search functionality
2. User dapat menggunakan action buttons pada setiap budget entry:
   - **"Detail"**: View budget detail dengan usage analysis
   - **"Edit"**: Edit budget information
   - **"Hapus"**: Delete budget dengan confirmation
3. Quick navigation ke related pages:
   - Budget comparison
   - Budget creation
   - Project detail

#### Hasil yang Diharapkan
- Easy access ke frequently used functions
- Clear visual hierarchy untuk actions
- Smooth navigation between related pages
- Consistent action patterns

#### Kondisi Error/Edge Cases
- Jika route tidak tersedia: handle 404 gracefully
- Jika permission denied: redirect dengan pesan error

#### Dependencies
- Proper routing configuration
- Permission checking
- Navigation state management

---

### 🟡 MEDIUM PRIORITY - Invoice Management dari Index

#### Deskripsi
User dapat melihat status invoice dan melakukan download langsung dari index page.

#### Langkah-langkah
1. User melihat kolom "Invoice" pada table yang menampilkan:
   - **Ada Invoice**: Link "Download" yang clickable
   - **Tidak Ada**: Text "Tidak ada" dalam warna abu-abu
2. User mengklik "Download" untuk budget yang memiliki invoice
3. System trigger download file PDF
4. User dapat identify budget mana yang sudah/belum memiliki invoice

#### Hasil yang Diharapkan
- Clear visual indication untuk invoice status
- Direct download capability dari index
- File download yang smooth
- Proper file handling

#### Kondisi Error/Edge Cases
- Jika file tidak ditemukan: tampilkan error "File tidak tersedia"
- Jika download gagal: retry mechanism
- Jika file corrupt: error message yang informatif

#### Dependencies
- File storage system
- Download route dan logic
- File existence checking

---

### 🟡 MEDIUM PRIORITY - Status Management dan Visual Indicators

#### Deskripsi
User dapat melihat dan memahami status budget melalui visual indicators yang konsisten.

#### Langkah-langkah
1. Status badges dengan color coding:
   - **Pending**: Badge kuning dengan text "Pending"
   - **Approved**: Badge hijau dengan text "Approved"
   - **Rejected**: Badge merah dengan text "Rejected"
2. Profit percentage dengan color coding:
   - Hijau untuk profit tinggi (>20%)
   - Kuning untuk profit sedang (10-20%)
   - Merah untuk profit rendah (<10%)
3. Visual hierarchy yang membantu quick scanning

#### Hasil yang Diharapkan
- Consistent color coding across application
- Quick visual identification of status
- Meaningful profit indicators
- Accessible color choices

#### Kondisi Error/Edge Cases
- Jika status invalid: default ke "pending"
- Jika profit calculation error: tampilkan "-"

#### Dependencies
- Status enumeration consistency
- Color scheme standardization
- Calculation accuracy

---

### 🟡 MEDIUM PRIORITY - Bulk Operations

#### Deskripsi
User dapat melakukan operasi pada multiple budget entries sekaligus (future enhancement).

#### Langkah-langkah
1. User mengaktifkan "Selection Mode"
2. Checkbox muncul pada setiap budget row
3. User select multiple budgets
4. Bulk action options muncul:
   - Change Status (Approve/Reject multiple)
   - Export Selected
   - Delete Multiple
5. User pilih action dan konfirmasi
6. System proses bulk operation dengan progress indicator

#### Hasil yang Diharapkan
- Efficient bulk operations
- Clear selection state
- Progress feedback untuk long operations
- Undo capability untuk safety

#### Kondisi Error/Edge Cases
- Jika sebagian operasi gagal: partial success report
- Jika permission denied untuk beberapa items: skip dengan notification

#### Dependencies
- Selection state management
- Bulk operation logic
- Progress tracking system

---

### 🟢 LOW PRIORITY - Advanced Sorting dan Pagination

#### Deskripsi
User dapat mengurutkan budget berdasarkan berbagai criteria dan navigate large datasets.

#### Langkah-langkah
1. Sortable table headers:
   - Amount (ascending/descending)
   - Budget Date (chronological)
   - Profit Percentage (highest/lowest)
   - Status (alphabetical)
2. Pagination controls untuk large datasets:
   - Items per page selection
   - Page navigation
   - Jump to page functionality
3. Sort state persistent dalam URL parameters

#### Hasil yang Diharapkan
- Flexible sorting options
- Efficient pagination
- Performance optimization untuk large datasets
- State persistence

#### Kondisi Error/Edge Cases
- Jika sort parameter invalid: default ke date descending
- Jika page number out of range: redirect ke page 1

#### Dependencies
- Database indexing untuk performance
- Pagination logic
- URL parameter management

---

### 🟢 LOW PRIORITY - Export dan Reporting

#### Deskripsi
User dapat export budget data dalam berbagai format untuk reporting purposes.

#### Langkah-langkah
1. Export options:
   - Export All Budgets (CSV/Excel)
   - Export Filtered Results
   - Export by Project
   - Export by Date Range
2. Report generation:
   - Budget summary report
   - Profit analysis report
   - Status distribution report
3. Download generated files

#### Hasil yang Diharapkan
- Multiple export formats
- Filtered export capability
- Professional report formatting
- Fast generation dan download

#### Kondisi Error/Edge Cases
- Jika export gagal: retry mechanism
- Jika file terlalu besar: chunked processing
- Jika no data to export: informative message

#### Dependencies
- Export library (Laravel Excel)
- Report generation logic
- File handling system

## Navigation Patterns

### Primary Navigation
- Sidebar dropdown menu untuk akses ke budgets
- "Bandingkan Budget" button untuk comparison feature
- Search box untuk quick finding

### Secondary Navigation
- Filter controls untuk data refinement
- Action buttons pada setiap row
- Pagination controls untuk large datasets

### Return Patterns
- Breadcrumb navigation untuk orientation
- "Kembali" buttons pada detail/edit pages
- Browser back button support

## Data Flow Patterns

### Index Loading Flow
Page Load → Statistics Calculation → Budget Grouping → Table Rendering → Filter State Application

### Filter Flow
User Input → Form Submission → Server Processing → URL Update → Results Rendering

### Action Flow
Button Click → Permission Check → Operation Execution → State Update → User Feedback

## Performance Considerations

### Loading Optimization
- Eager loading untuk project relationships
- Pagination untuk large datasets
- Lazy loading untuk non-critical data

### Caching Strategy
- Cache statistics untuk 5 menit
- Cache project list untuk dropdown
- Invalidate cache saat ada perubahan

### Error Handling
- Graceful degradation untuk calculation errors
- Retry mechanisms untuk failed operations
- Clear error messages dengan recovery options

## Integration Points

### Project Integration
- Budget grouping berdasarkan project
- Navigation ke project detail
- Project-based filtering

### Invoice Integration
- Invoice status display
- Direct download capability
- Upload/edit navigation

### Comparison Integration
- Quick access ke comparison feature
- Multi-project analysis
- Variance reporting
