# Budgets Create User Flow - DisaCloud05-v4

## Overview

Halaman Budgets Create memungkinkan user membuat budget entry baru dengan form komprehensif yang mencakup project selection, financial details, profit calculation, dan invoice upload. Form ini memiliki real-time calculation dan validation yang robust.

## User Flows

### 🔴 HIGH PRIORITY - Membuat Budget Entry Baru

#### Deskripsi
User membuat budget entry baru dengan form yang comprehensive dan validation yang real-time.

#### Langkah-langkah
1. User mengakses halaman create budget dari:
   - Budgets index → "Tambah Budget" button
   - Sidebar dropdown → "Tambah Budget"
2. System menampilkan form dengan sections:
   - **Project Selection**: Dropdown dengan semua proyek
   - **Financial Details**: Amount dan estimated profit
   - **Description**: Textarea untuk deskripsi budget
   - **Budget Date**: Date picker
   - **Status**: Dropdown (pending/approved/rejected)
   - **Invoice Upload**: File upload untuk PDF (optional)
3. User mengisi form step by step
4. Real-time profit percentage calculation saat user input amount/profit
5. User submit form
6. System validasi dan create budget entry
7. Redirect ke budgets index dengan success message

#### Hasil yang Diharapkan
- Form yang user-friendly dengan clear sections
- Real-time calculations untuk profit percentage
- Comprehensive validation dengan clear error messages
- Successful budget creation dengan proper data

#### Kondisi Error/Edge Cases
- Validation errors: highlight field yang bermasalah dengan pesan spesifik
- File upload error: tampilkan error untuk file yang tidak valid
- Jika save gagal: preserve form data, tampilkan error message

#### Dependencies
- Project data untuk dropdown
- File upload handling
- Real-time calculation JavaScript
- Form validation rules

---

### 🔴 HIGH PRIORITY - Project Selection dan Validation

#### Deskripsi
User memilih proyek untuk budget entry dengan validation yang memastikan project exists dan accessible.

#### Langkah-langkah
1. User melihat dropdown "Proyek" dengan:
   - Placeholder "Pilih Proyek"
   - List semua proyek yang tersedia (ordered by name)
   - Required field indicator
2. User memilih proyek dari dropdown
3. System validasi:
   - Project ID harus exists di database
   - Project harus accessible oleh user
4. Form state update dengan selected project

#### Hasil yang Diharapkan
- Dropdown yang responsive dengan semua proyek
- Clear indication untuk required field
- Proper validation untuk project selection
- Good UX dengan search/filter dalam dropdown

#### Kondisi Error/Edge Cases
- Jika tidak ada proyek: tampilkan pesan "Belum ada proyek tersedia"
- Jika project tidak valid: validation error "Proyek tidak valid"
- Jika project deleted setelah form load: handle gracefully

#### Dependencies
- Project model dengan proper ordering
- Validation rules untuk project existence
- Dropdown component functionality

---

### 🔴 HIGH PRIORITY - Financial Input dengan Real-time Calculation

#### Deskripsi
User memasukkan data finansial dengan automatic profit percentage calculation yang real-time.

#### Langkah-langkah
1. User mengisi field finansial:
   - **Amount**: Input numeric untuk total budget amount
   - **Estimated Profit**: Input numeric untuk estimated profit
2. JavaScript real-time calculation:
   - Saat user mengetik di amount atau profit field
   - Calculate profit percentage = (estimated_profit / amount) × 100
   - Display hasil calculation di field readonly
3. Visual feedback:
   - Profit percentage dengan color coding
   - Format currency untuk amount fields
   - Validation untuk minimum values (≥ 0)

#### Hasil yang Diharapkan
- Real-time calculation yang smooth
- Visual feedback yang immediate
- Proper currency formatting
- Validation untuk numeric inputs

#### Kondisi Error/Edge Cases
- Jika amount = 0: profit percentage = 0, tampilkan warning
- Jika input non-numeric: validation error
- Jika calculation overflow: handle dengan graceful fallback

#### Dependencies
- JavaScript untuk real-time calculation
- Currency formatting helper
- Numeric validation rules

---

### 🔴 HIGH PRIORITY - Invoice File Upload

#### Deskripsi
User dapat upload file invoice PDF sebagai attachment untuk budget entry.

#### Langkah-langkah
1. User melihat file upload section:
   - Label "Upload Invoice (Opsional)"
   - File input dengan accept=".pdf"
   - Help text "File PDF maksimal 10MB"
2. User memilih file PDF dari device
3. Client-side validation:
   - File type harus PDF
   - File size maksimal 10MB
   - Display selected filename
4. Server-side validation saat form submit:
   - MIME type validation
   - File size validation
   - Virus scanning (optional)
5. File disimpan dengan unique filename di storage/invoices

#### Hasil yang Diharapkan
- Easy file selection dengan proper constraints
- Clear feedback untuk selected file
- Secure file upload dengan validation
- Proper file storage dengan unique naming

#### Kondisi Error/Edge Cases
- File bukan PDF: "Hanya file PDF yang diperbolehkan"
- File terlalu besar: "File maksimal 10MB"
- Upload gagal: "Gagal upload file, coba lagi"
- Storage penuh: "Storage tidak tersedia"

#### Dependencies
- File upload handling
- Storage configuration
- MIME type validation
- File size checking

---

### 🟡 MEDIUM PRIORITY - Form Validation dan Error Handling

#### Deskripsi
User mendapat feedback validation yang comprehensive dengan error messages yang clear dan actionable.

#### Langkah-langkah
1. Client-side validation (immediate feedback):
   - Required fields validation
   - Numeric format validation
   - Date format validation
   - File type/size validation
2. Server-side validation (comprehensive):
   - Business rules validation
   - Database constraints validation
   - Security validation
3. Error display:
   - Field-level errors di bawah input
   - Error styling (red border, red text)
   - Summary errors di top form (optional)
4. Success handling:
   - Clear success message
   - Redirect ke appropriate page

#### Hasil yang Diharapkan
- Immediate feedback untuk user errors
- Clear, actionable error messages
- Visual indication untuk error fields
- Smooth success flow

#### Kondisi Error/Edge Cases
- Multiple validation errors: tampilkan semua dengan prioritas
- Network error saat submit: retry mechanism
- Session timeout: redirect ke login dengan message

#### Dependencies
- Validation rules (client dan server)
- Error message localization
- Form state management

---

### 🟡 MEDIUM PRIORITY - Form State Management dan UX

#### Deskripsi
User experience yang smooth dengan proper form state management dan user guidance.

#### Langkah-langkah
1. Form state indicators:
   - Required field markers (*)
   - Loading states saat submit
   - Disabled state untuk submit button saat invalid
2. User guidance:
   - Help text untuk complex fields
   - Placeholder text yang descriptive
   - Progress indication untuk multi-step (jika ada)
3. Data preservation:
   - Preserve form data saat validation error
   - Auto-save draft (optional)
   - Confirm navigation away dengan unsaved changes

#### Hasil yang Diharapkan
- Clear guidance untuk user input
- Preserved data saat errors
- Loading feedback untuk operations
- Prevention of data loss

#### Kondisi Error/Edge Cases
- Browser refresh dengan unsaved data: confirmation dialog
- Network interruption: preserve form state
- Long operation: progress indicator

#### Dependencies
- Form state management JavaScript
- Local storage untuk draft (optional)
- Loading state indicators

---

### 🟡 MEDIUM PRIORITY - Navigation dan Cancel Handling

#### Deskripsi
User dapat navigate away dari form dengan proper handling untuk unsaved changes.

#### Langkah-langkah
1. Navigation options:
   - "Kembali" button ke budgets index
   - "Batal" button dengan confirmation
   - Breadcrumb navigation
2. Unsaved changes handling:
   - Detect form changes
   - Confirmation dialog saat navigate away
   - Option untuk save draft atau discard
3. Cancel flow:
   - Confirmation dialog "Yakin ingin membatalkan?"
   - Option untuk continue editing atau discard
   - Redirect ke budgets index jika confirmed

#### Hasil yang Diharapkan
- Safe navigation dengan data protection
- Clear cancel/back options
- Confirmation untuk destructive actions
- Smooth return to previous page

#### Kondisi Error/Edge Cases
- Browser back button: handle dengan beforeunload event
- Direct URL navigation: same protection
- Session timeout during edit: handle gracefully

#### Dependencies
- JavaScript untuk change detection
- beforeunload event handling
- Navigation state management

---

### 🟢 LOW PRIORITY - Advanced Features dan Enhancements

#### Deskripsi
Enhanced user experience dengan advanced features untuk power users.

#### Langkah-langkah
1. Auto-complete features:
   - Description suggestions berdasarkan history
   - Amount suggestions berdasarkan similar projects
   - Smart defaults untuk new entries
2. Bulk creation:
   - Template-based creation
   - Copy from existing budget
   - Batch creation untuk multiple projects
3. Integration features:
   - Import dari external systems
   - API integration untuk automatic data
   - Sync dengan accounting systems

#### Hasil yang Diharapkan
- Enhanced productivity untuk frequent users
- Smart suggestions dan automation
- Integration capabilities
- Template dan bulk operations

#### Kondisi Error/Edge Cases
- Auto-complete service down: fallback ke manual input
- Import errors: detailed error reporting
- Integration failures: graceful degradation

#### Dependencies
- Auto-complete service
- Import/export logic
- External API integrations

## Form Structure dan Layout

### Form Sections
1. **Header Section**: Title, breadcrumb, navigation
2. **Project Section**: Project selection dropdown
3. **Financial Section**: Amount, profit, calculations
4. **Details Section**: Description, date, status
5. **Upload Section**: Invoice file upload
6. **Actions Section**: Submit, cancel buttons

### Responsive Design
- Desktop: Two-column layout untuk efficiency
- Tablet: Single column dengan proper spacing
- Mobile: Stack all elements vertically

### Accessibility
- Proper label associations
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## Validation Rules

### Required Fields
- project_id: must exist in projects table
- amount: numeric, minimum 0
- estimated_profit: numeric, minimum 0
- description: string, maximum 500 characters
- budget_date: valid date format
- status: must be in [pending, approved, rejected]

### Optional Fields
- invoice_file: PDF format, maximum 10MB

### Business Rules
- Amount harus > 0 untuk meaningful budget
- Estimated profit reasonable (tidak > amount)
- Budget date tidak boleh terlalu jauh di masa lalu
- Project harus active untuk new budget

## Performance Considerations

### Client-side Optimization
- Debounced calculation untuk real-time updates
- Lazy loading untuk large project lists
- Progressive enhancement untuk JavaScript features

### Server-side Optimization
- Efficient project loading dengan minimal data
- File upload dengan streaming untuk large files
- Validation caching untuk repeated checks

### Error Recovery
- Retry mechanisms untuk network failures
- Graceful degradation untuk JavaScript errors
- Fallback options untuk file upload issues
