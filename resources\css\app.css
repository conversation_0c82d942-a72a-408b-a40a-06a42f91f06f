@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Calendar Styles */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
}

.calendar-header {
    background-color: #f3f4f6;
    padding: 0.75rem;
    font-weight: 600;
    text-align: center;
    color: #4b5563;
    border-bottom: 1px solid #e5e7eb;
}

.calendar-day {
    background-color: white;
    min-height: 120px;
    padding: 0.5rem;
    position: relative;
}

.calendar-day.today {
    background-color: #eef2ff;
}

.calendar-day.other-month {
    background-color: #f9fafb;
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
}

.today .calendar-day-number {
    color: #4f46e5;
}

.other-month .calendar-day-number {
    color: #9ca3af;
}

.event-container {
    max-height: calc(100% - 2rem);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.project-event {
    margin-bottom: 0.25rem;
    position: relative;
    transition: all 0.2s ease-in-out;
}

.project-event:hover {
    transform: translateY(-1px);
}

.project-tooltip {
    max-width: 300px;
    z-index: 50;
    pointer-events: none;
}

/* Progress Bar Styles */
.progress-bar {
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    transition: width 0.3s ease-in-out;
}

/* Toast Notification Styles */
.toast-container {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    z-index: 50;
}

.toast {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Indicator Styles */
.loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
