@extends('layouts.app')

@section('title', 'Tambah Budget')
@section('header', 'Tambah Budget Baru')

@section('content')
    <div class="max-w-3xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form action="{{ route('budgets.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="mb-4">
                    <label for="project_id" class="block text-sm font-medium text-gray-700 mb-1">Proyek</label>
                    <select name="project_id" id="project_id" class="form-select w-full rounded-md @error('project_id') border-red-500 @enderror" required>
                        <option value="">Pilih Proyek</option>
                        @foreach($projects as $project)
                            <option value="{{ $project->id }}" {{ old('project_id') == $project->id ? 'selected' : '' }}>
                                {{ $project->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('project_id')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Jumlah Budget (Rp)</label>
                        <input type="number" name="amount" id="amount" value="{{ old('amount') }}" 
                            class="form-input w-full rounded-md @error('amount') border-red-500 @enderror" 
                            required step="0.01" min="0">
                        @error('amount')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="estimated_profit" class="block text-sm font-medium text-gray-700 mb-1">Estimasi Keuntungan (Rp)</label>
                        <input type="number" name="estimated_profit" id="estimated_profit" value="{{ old('estimated_profit') }}" 
                            class="form-input w-full rounded-md @error('estimated_profit') border-red-500 @enderror" 
                            required step="0.01" min="0">
                        @error('estimated_profit')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
                    <textarea name="description" id="description" rows="4" 
                        class="form-textarea w-full rounded-md @error('description') border-red-500 @enderror" 
                        required>{{ old('description') }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="budget_date" class="block text-sm font-medium text-gray-700 mb-1">Tanggal</label>
                        <input type="date" name="budget_date" id="budget_date" value="{{ old('budget_date', date('Y-m-d')) }}" 
                            class="form-input w-full rounded-md @error('budget_date') border-red-500 @enderror" 
                            required>
                        @error('budget_date')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status" class="form-select w-full rounded-md @error('status') border-red-500 @enderror" required>
                            <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ old('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="rejected" {{ old('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                        @error('status')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mb-6">
                    <label for="invoice_file" class="block text-sm font-medium text-gray-700 mb-1">File Invoice (PDF)</label>
                    <input type="file" name="invoice_file" id="invoice_file" 
                        class="form-input w-full rounded-md @error('invoice_file') border-red-500 @enderror"
                        accept=".pdf">
                    <p class="text-xs text-gray-500 mt-1">Upload file PDF (max: 10MB)</p>
                    @error('invoice_file')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="flex justify-between">
                    <a href="{{ route('budgets.index') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                        Kembali
                    </a>
                    <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        Simpan Budget
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    // Calculate profit percentage automatically
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.getElementById('amount');
        const profitInput = document.getElementById('estimated_profit');
        
        function updateProfitPercentage() {
            const amount = parseFloat(amountInput.value) || 0;
            const profit = parseFloat(profitInput.value) || 0;
            
            if (amount > 0) {
                const percentage = (profit / amount) * 100;
                document.getElementById('profit_percentage_display').textContent = percentage.toFixed(2) + '%';
            } else {
                document.getElementById('profit_percentage_display').textContent = '0%';
            }
        }
        
        amountInput.addEventListener('input', updateProfitPercentage);
        profitInput.addEventListener('input', updateProfitPercentage);
    });
</script>
@endsection
