@extends('layouts.app')

@section('title', 'Edit Budget')
@section('header', 'Edit Budget')

@section('content')
    <div class="max-w-3xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form action="{{ route('budgets.update', $budget) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="mb-4">
                    <label for="project_id" class="block text-sm font-medium text-gray-700 mb-1">Proyek</label>
                    <select name="project_id" id="project_id" class="form-select w-full rounded-md @error('project_id') border-red-500 @enderror" required>
                        <option value="">Pilih Proyek</option>
                        @foreach($projects as $project)
                            <option value="{{ $project->id }}" {{ old('project_id', $budget->project_id) == $project->id ? 'selected' : '' }}>
                                {{ $project->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('project_id')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Jumlah Budget (Rp)</label>
                        <input type="number" name="amount" id="amount" value="{{ old('amount', $budget->amount) }}" 
                            class="form-input w-full rounded-md @error('amount') border-red-500 @enderror" 
                            required step="0.01" min="0">
                        @error('amount')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="estimated_profit" class="block text-sm font-medium text-gray-700 mb-1">Estimasi Keuntungan (Rp)</label>
                        <input type="number" name="estimated_profit" id="estimated_profit" value="{{ old('estimated_profit', $budget->estimated_profit) }}" 
                            class="form-input w-full rounded-md @error('estimated_profit') border-red-500 @enderror" 
                            required step="0.01" min="0">
                        @error('estimated_profit')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">
                            Persentase Keuntungan: <span id="profit_percentage_display">{{ number_format($budget->profit_percentage, 2) }}%</span>
                        </p>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
                    <textarea name="description" id="description" rows="4" 
                        class="form-textarea w-full rounded-md @error('description') border-red-500 @enderror" 
                        required>{{ old('description', $budget->description) }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="budget_date" class="block text-sm font-medium text-gray-700 mb-1">Tanggal</label>
                        <input type="date" name="budget_date" id="budget_date" 
                            value="{{ old('budget_date', $budget->budget_date ? date('Y-m-d', strtotime($budget->budget_date)) : date('Y-m-d')) }}" 
                            class="form-input w-full rounded-md @error('budget_date') border-red-500 @enderror" 
                            required>
                        @error('budget_date')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status" class="form-select w-full rounded-md @error('status') border-red-500 @enderror" required>
                            <option value="pending" {{ old('status', $budget->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ old('status', $budget->status) == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="rejected" {{ old('status', $budget->status) == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                        @error('status')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mb-6">
                    <label for="invoice_file" class="block text-sm font-medium text-gray-700 mb-1">File Invoice (PDF)</label>
                    @if($budget->invoice_file)
                        <div class="flex items-center mb-2">
                            <span class="text-sm text-gray-600 mr-2">File saat ini:</span>
                            <a href="{{ route('budgets.download-invoice', $budget) }}" class="text-blue-600 hover:text-blue-900 text-sm flex items-center">
                                Download Invoice
                            </a>
                        </div>
                    @endif
                    <input type="file" name="invoice_file" id="invoice_file" 
                        class="form-input w-full rounded-md @error('invoice_file') border-red-500 @enderror"
                        accept=".pdf">
                    <p class="text-xs text-gray-500 mt-1">Upload file PDF baru (max: 10MB) atau biarkan kosong untuk mempertahankan file saat ini</p>
                    @error('invoice_file')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="flex justify-between">
                    <a href="{{ route('budgets.index') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                        Kembali
                    </a>
                    <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        Perbarui Budget
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    // Calculate profit percentage automatically
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.getElementById('amount');
        const profitInput = document.getElementById('estimated_profit');
        const percentageDisplay = document.getElementById('profit_percentage_display');
        
        function updateProfitPercentage() {
            const amount = parseFloat(amountInput.value) || 0;
            const profit = parseFloat(profitInput.value) || 0;
            
            if (amount > 0) {
                const percentage = (profit / amount) * 100;
                percentageDisplay.textContent = percentage.toFixed(2) + '%';
            } else {
                percentageDisplay.textContent = '0%';
            }
        }
        
        amountInput.addEventListener('input', updateProfitPercentage);
        profitInput.addEventListener('input', updateProfitPercentage);
    });
</script>
@endsection
