@extends('layouts.app')

@section('title', 'Budget Management')
@section('header', 'Budget Management')

@section('content')
    <div class="mb-6 flex justify-between items-center">
        <div class="flex space-x-2">

            <a href="{{ route('budgets.compare') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center">
                Bandingkan Budget
            </a>
            <form action="{{ route('budgets.index') }}" method="GET" class="flex">
                <input type="text" name="search" placeholder="Cari budget..." value="{{ request('search') }}" class="border border-gray-300 rounded-lg px-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <button type="submit" class="ml-2 px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Search
                </button>
            </form>
        </div>
        
        <div class="flex space-x-2">
            <form action="{{ route('budgets.index') }}" method="GET" class="flex space-x-2">
                <select name="project_id" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" onchange="this.form.submit()">
                    <option value="">Semua Proyek</option>
                    @foreach($allProjects as $project)
                        <option value="{{ $project->id }}" {{ request('project_id') == $project->id ? 'selected' : '' }}>{{ $project->name }}</option>
                    @endforeach
                </select>
                
                <select name="status" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" onchange="this.form.submit()">
                    <option value="">Semua Status</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                </select>
                
                @if(request('search') || request('project_id') || request('status'))
                    <a href="{{ route('budgets.index') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Reset Filter
                    </a>
                @endif
            </form>
            
            <a href="{{ route('budgets.compare') }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Bandingkan Budget
            </a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Total Budget</h3>
            <p class="text-3xl font-bold text-indigo-600">Rp {{ number_format($totalBudget, 0, ',', '.') }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Approved Budget</h3>
            <p class="text-3xl font-bold text-green-600">Rp {{ number_format($approvedBudget, 0, ',', '.') }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Pending Budget</h3>
            <p class="text-3xl font-bold text-yellow-600">Rp {{ number_format($pendingBudget, 0, ',', '.') }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Estimasi Keuntungan</h3>
            <p class="text-3xl font-bold text-blue-600">Rp {{ number_format($totalEstimatedProfit, 0, ',', '.') }}</p>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Proyek</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deskripsi</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estimasi Profit</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($budgetsGroupedByProject as $projectId => $projectBudgets)
                    @if($projectBudgets->isNotEmpty())
                        @php
                            // Attempt to get project name. Handles cases where project might be null.
                            $projectName = $projectBudgets->first()->project ? $projectBudgets->first()->project->name : ($projectId === 'unassigned' ? 'Belum Ditugaskan' : 'Proyek Tidak Ditemukan');
                        @endphp
                        <tr class="bg-gray-50">
                            <td colspan="8" class="px-4 py-3 text-left text-sm font-semibold text-gray-700">
                                Proyek: {{ $projectName }}
                            </td>
                        </tr>
                        @foreach ($projectBudgets as $budget)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {{-- This cell can be repurposed or left blank as project name is a header --}}
                                    {{-- For example, display budget type if available --}}
                                    <div class="text-sm text-gray-700">{{ $budget->type ? Str::ucfirst($budget->type) : '' }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ Str::limit($budget->description, 50) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Rp {{ number_format($budget->amount, 0, ',', '.') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        Rp {{ number_format($budget->estimated_profit, 0, ',', '.') }}
                                        @if($budget->amount > 0)
                                        <span class="text-xs text-gray-500">({{ number_format(($budget->estimated_profit / $budget->amount) * 100, 1) }}%)</span>
                                        @else
                                        <span class="text-xs text-gray-500">(0%)</span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ \Carbon\Carbon::parse($budget->budget_date)->format('d M Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {{ $budget->status == 'approved' ? 'bg-green-100 text-green-800' : '' }}
                                        {{ $budget->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                        {{ $budget->status == 'rejected' ? 'bg-red-100 text-red-800' : '' }}">
                                        {{ ucfirst($budget->status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @if($budget->invoice_file)
                                        <a href="{{ route('budgets.download-invoice', $budget) }}" class="text-blue-600 hover:text-blue-900">
                                            Download
                                        </a>
                                    @else
                                        <span class="text-gray-400">Tidak ada</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                    <a href="{{ route('budgets.show', $budget) }}" class="text-indigo-600 hover:text-indigo-900">Detail</a>
                                    <a href="{{ route('budgets.edit', $budget) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                    <form action="{{ route('budgets.destroy', $budget) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Yakin ingin menghapus budget ini?')">Hapus</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                @empty
                    <tr>
                        <td colspan="8" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                            Tidak ada data budget yang tersedia.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
@endsection
