@extends('layouts.app')

@section('title', 'Detail Budget')
@section('header', 'Detail Budget')

@section('content')
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="flex justify-between items-start mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-800">Budget untuk Proyek: {{ $budget->project->name }}</h2>
                    <p class="text-gray-600">{{ \Carbon\Carbon::parse($budget->budget_date)->format('d M Y') }}</p>
                </div>
                <div>
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full
                        {{ $budget->status == 'approved' ? 'bg-green-100 text-green-800' : '' }}
                        {{ $budget->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                        {{ $budget->status == 'rejected' ? 'bg-red-100 text-red-800' : '' }}">
                        {{ ucfirst($budget->status) }}
                    </span>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2">Informasi Budget</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Jumlah Budget:</span>
                            <span class="font-medium">Rp {{ number_format($budget->amount, 0, ',', '.') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Estimasi Keuntungan:</span>
                            <span class="font-medium">Rp {{ number_format($budget->estimated_profit, 0, ',', '.') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Persentase Keuntungan:</span>
                            <span class="font-medium">{{ number_format($budget->profit_percentage, 2) }}%</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2">Informasi Proyek</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Nama Proyek:</span>
                            <span class="font-medium">{{ $budget->project->name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status Proyek:</span>
                            <span class="font-medium">{{ ucfirst($budget->project->status) }}</span>
                        </div>
                        @if($budget->project->start_date && $budget->project->end_date)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Periode Proyek:</span>
                            <span class="font-medium">
                                {{ \Carbon\Carbon::parse($budget->project->start_date)->format('d M Y') }} - 
                                {{ \Carbon\Carbon::parse($budget->project->end_date)->format('d M Y') }}
                            </span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Deskripsi</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-800 whitespace-pre-line">{{ $budget->description }}</p>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Invoice</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    @if($budget->invoice_file)
                        <div class="flex items-center">
                            <a href="{{ route('budgets.download-invoice', $budget) }}" class="text-indigo-600 hover:text-indigo-900 font-medium">
                                Download Invoice PDF
                            </a>
                        </div>
                    @else
                        <p class="text-gray-500">Tidak ada file invoice untuk budget ini.</p>
                    @endif
                </div>
            </div>
            
            <div class="flex justify-between">
                <a href="{{ route('budgets.index') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                    Kembali ke Daftar
                </a>
                <div class="flex space-x-2">
                    <a href="{{ route('budgets.edit', $budget) }}" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        Edit Budget
                    </a>
                    <form action="{{ route('budgets.destroy', $budget) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700" 
                            onclick="return confirm('Yakin ingin menghapus budget ini?')">
                            Hapus Budget
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
