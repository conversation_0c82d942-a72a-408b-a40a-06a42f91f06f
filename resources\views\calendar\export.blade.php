@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Export Calendar</h1>
            <p class="text-gray-600">Choose your preferred format and options to export the project calendar.</p>
        </div>

        <!-- Export Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form action="{{ route('calendar.export') }}" method="GET" id="exportForm">
                <!-- Format Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Export Format</label>
                    {{-- <div class="grid grid-cols-1 md:grid-cols-3 gap-4"> --}}
                        <!-- PDF Option -->
                        <div class="relative">
                            <input type="radio" name="format" value="pdf" id="format_pdf" class="sr-only" checked>
                            <label for="format_pdf" class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors format-option">
                                <svg class="w-8 h-8 text-red-500 mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-900">PDF</span>
                                <span class="text-xs text-gray-500 text-center mt-1">Professional document format</span>
                            </label>
                        </div>

                        {{-- <!-- PNG Option -->
                        <div class="relative">
                            <input type="radio" name="format" value="png" id="format_png" class="sr-only">
                            <label for="format_png" class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors format-option">
                                <svg class="w-8 h-8 text-green-500 mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-900">PNG</span>
                                <span class="text-xs text-gray-500 text-center mt-1">High quality image</span>
                            </label>
                        </div>

                        <!-- JPG Option -->
                        <div class="relative">
                            <input type="radio" name="format" value="jpg" id="format_jpg" class="sr-only">
                            <label for="format_jpg" class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-indigo-300 transition-colors format-option">
                                <svg class="w-8 h-8 text-blue-500 mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-900">JPG</span>
                                <span class="text-xs text-gray-500 text-center mt-1">Compressed image</span>
                            </label>
                        </div> --}}
                    {{-- </div> --}}
                </div>

                <!-- Date Selection -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Month Selection -->
                    <div>
                        <label for="month" class="block text-sm font-medium text-gray-700 mb-2">Month</label>
                        <select name="month" id="month" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            @for($i = 1; $i <= 12; $i++)
                                <option value="{{ $i }}" {{ $i == now()->month ? 'selected' : '' }}>
                                    {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>

                    <!-- Year Selection -->
                    <div>
                        <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Year</label>
                        <select name="year" id="year" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            @for($year = now()->year - 1; $year <= now()->year + 2; $year++)
                                <option value="{{ $year }}" {{ $year == now()->year ? 'selected' : '' }}>
                                    {{ $year }}
                                </option>
                            @endfor
                        </select>
                    </div>
                </div>

                <!-- Project Filter -->
                <div class="mb-6">
                    <label for="project_id" class="block text-sm font-medium text-gray-700 mb-2">Filter by Project (Optional)</label>
                    <select name="project_id" id="project_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">All Projects</option>
                        @foreach($projects as $project)
                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Format Information -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Format Information</h3>
                    <div id="format-info">
                        <div class="format-description" data-format="pdf">
                            <p class="text-sm text-gray-600">PDF format provides a professional document with detailed project information, perfect for printing and sharing with stakeholders.</p>
                        </div>
                        <div class="format-description hidden" data-format="png">
                            <p class="text-sm text-gray-600">PNG format creates a high-quality image with transparent background support, ideal for presentations and web use.</p>
                        </div>
                        <div class="format-description hidden" data-format="jpg">
                            <p class="text-sm text-gray-600">JPG format creates a compressed image file, perfect for email attachments and quick sharing.</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <button type="submit" class="flex-1 bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors flex items-center justify-center">
                        Export Calendar
                    </button>
                    
                    <a href="{{ route('calendar.index') }}" class="flex-1 bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors flex items-center justify-center">
                        Back to Calendar
                    </a>
                </div>
            </form>
        </div>


    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle format selection
    const formatOptions = document.querySelectorAll('input[name="format"]');
    const formatDescriptions = document.querySelectorAll('.format-description');
    
    formatOptions.forEach(option => {
        option.addEventListener('change', function() {
            // Update visual selection
            document.querySelectorAll('.format-option').forEach(label => {
                label.classList.remove('border-indigo-500', 'bg-indigo-50');
                label.classList.add('border-gray-200');
            });
            
            if (this.checked) {
                const label = document.querySelector(`label[for="${this.id}"]`);
                label.classList.remove('border-gray-200');
                label.classList.add('border-indigo-500', 'bg-indigo-50');
            }
            
            // Update format description
            formatDescriptions.forEach(desc => {
                desc.classList.add('hidden');
            });
            
            const selectedDesc = document.querySelector(`[data-format="${this.value}"]`);
            if (selectedDesc) {
                selectedDesc.classList.remove('hidden');
            }
        });
    });
    
    // Initialize first option
    const firstOption = document.querySelector('input[name="format"]:checked');
    if (firstOption) {
        firstOption.dispatchEvent(new Event('change'));
    }
    
    // Handle form submission
    document.getElementById('exportForm').addEventListener('submit', function(e) {
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = `Generating...`;
        
        // Re-enable button after 5 seconds
        setTimeout(() => {
            submitButton.disabled = false;
            submitButton.innerHTML = `Export Calendar`;
        }, 5000);
    });
});
</script>
@endsection
