@extends('layouts.app')

@section('styles')
    <link href='https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/5.11.3/main.min.css' rel='stylesheet' />
    <style>
        .fc-event {
            cursor: pointer;
        }
        .fc-event:hover {
            opacity: 0.9;
        }
        .fc .fc-button-primary {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .fc .fc-button-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }
        .fc .fc-button-primary:disabled {
            background-color: #93c5fd;
            border-color: #93c5fd;
        }
        .fc-daygrid-day-number,
        .fc-col-header-cell-cushion {
            color: #374151;
        }
    </style>
@endsection

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Kalender Proyek</h1>
                    <p class="mt-2 text-sm text-gray-500">Lihat jadwal dan aktivitas semua proyek</p>
                </div>
                <div class="flex space-x-3">
                    <select id="projectFilter" class="border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Semua Proyek</option>
                        @foreach($projects ?? [] as $project)
                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                        @endforeach
                    </select>
                    <select id="workerFilter" class="border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Semua Pekerja</option>
                        @foreach($workers ?? [] as $worker)
                            <option value="{{ $worker->id }}">{{ $worker->name }}</option>
                        @endforeach
                    </select>
                    <a href="{{ route('calendar.export.form') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                        Export Calendar
                    </a>
                </div>
            </div>
        </div>

        <!-- Calendar -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="p-6">
                <div id="calendar"></div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Aktivitas -->
<div id="eventModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg overflow-hidden shadow-xl transform w-full max-w-lg">
            <div class="px-6 py-4">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle"></h3>
                    <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Tutup</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-gray-500" id="modalDescription"></p>
                    <div class="mt-3">
                        <p class="text-sm text-gray-600">Proyek: <span id="modalProject" class="font-medium"></span></p>
                        <p class="text-sm text-gray-600 mt-1">Pekerja: <span id="modalWorker" class="font-medium"></span></p>
                        <p class="text-sm text-gray-600 mt-1">Tanggal: <span id="modalDate" class="font-medium"></span></p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button type="button" onclick="closeModal()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
    <script src='https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/5.11.3/main.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/5.11.3/locales/id.js'></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');
            var calendar = new FullCalendar.Calendar(calendarEl, {
                locale: 'id',
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                events: '/calendar/events',
                eventClick: function(info) {
                    showEventDetails(info.event);
                },
                eventDidMount: function(info) {
                    info.el.title = info.event.title;
                }
            });
            calendar.render();

            // Filter events
            document.getElementById('projectFilter').addEventListener('change', function() {
                reloadEvents();
            });

            document.getElementById('workerFilter').addEventListener('change', function() {
                reloadEvents();
            });

            function reloadEvents() {
                const projectId = document.getElementById('projectFilter').value;
                const workerId = document.getElementById('workerFilter').value;
                
                calendar.removeAllEvents();
                calendar.addEventSource({
                    url: '/calendar/events',
                    extraParams: {
                        project_id: projectId,
                        worker_id: workerId
                    }
                });
            }
        });

        function showEventDetails(event) {
            document.getElementById('modalTitle').textContent = event.title;
            document.getElementById('modalDescription').textContent = event.extendedProps.description || 'Tidak ada deskripsi';
            document.getElementById('modalProject').textContent = event.extendedProps.project_name;
            document.getElementById('modalWorker').textContent = event.extendedProps.worker_name;
            document.getElementById('modalDate').textContent = event.start.toLocaleDateString('id-ID', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('eventModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('eventModal').classList.add('hidden');
        }
    </script>
@endsection 