@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Simple Calendar Export</h1>
        
        <div class="bg-white rounded-lg shadow p-6">
            <form action="{{ route('simple.calendar.export') }}" method="GET">
                <!-- Format Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Format</label>
                    <select name="format" class="w-full border rounded px-3 py-2">
                        <option value="pdf">PDF</option>
                        <option value="html">HTML (for PNG/JPG)</option>
                    </select>
                </div>

                <!-- Month Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Month</label>
                    <select name="month" class="w-full border rounded px-3 py-2">
                        @for($i = 1; $i <= 12; $i++)
                            <option value="{{ $i }}" {{ $i == now()->month ? 'selected' : '' }}>
                                {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                            </option>
                        @endfor
                    </select>
                </div>

                <!-- Year Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Year</label>
                    <select name="year" class="w-full border rounded px-3 py-2">
                        @for($year = now()->year - 1; $year <= now()->year + 2; $year++)
                            <option value="{{ $year }}" {{ $year == now()->year ? 'selected' : '' }}>
                                {{ $year }}
                            </option>
                        @endfor
                    </select>
                </div>

                <!-- Project Filter -->
                <div class="mb-6">
                    <label class="block text-sm font-medium mb-2">Project (Optional)</label>
                    <select name="project_id" class="w-full border rounded px-3 py-2">
                        <option value="">All Projects</option>
                        @foreach($projects as $project)
                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                        @endforeach
                    </select>
                </div>

                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                    Export Calendar
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
