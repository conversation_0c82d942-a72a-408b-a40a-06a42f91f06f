@extends('layouts.app')

@section('title', 'Dashboard')
@section('header', 'Dashboard')

@section('content')
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Total Proyek Aktif -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-gray-600 text-sm">Total Proyek Aktif</h2>
                    <p class="text-2xl font-semibold text-gray-800">{{ $totalActiveProjects }}</p>
                </div>
            </div>
        </div>

        <!-- Proyek Mendekati Deadline -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-gray-600 text-sm">Mendekati Deadline
                        <span class="text-xs">
                            @if(request('deadline_filter') == '1_day')
                                (1 Hari)
                            @elseif(request('deadline_filter') == '1_month')
                                (1 Bulan)
                            @else
                                (1 Minggu)
                            @endif
                        </span>
                    </h2>
                    <p class="text-2xl font-semibold text-gray-800">{{ $upcomingDeadlines->count() }}</p>
                </div>
            </div>
        </div>

        <!-- Proyek Selesai -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-gray-600 text-sm">Proyek Selesai</h2>
                    <p class="text-2xl font-semibold text-gray-800">{{ $projectStatusDistribution['completed'] }}</p>
                </div>
            </div>
        </div>

        <!-- Proyek Ditunda -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-gray-600 text-sm">Proyek Ditunda</h2>
                    <p class="text-2xl font-semibold text-gray-800">{{ $projectStatusDistribution['on_hold'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Distribusi Status Proyek -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold mb-4">Distribusi Status Proyek</h3>
            <div class="relative" style="height: 300px;">
                <canvas id="projectStatusChart"></canvas>
            </div>
        </div>

        <!-- Proyek Mendekati Deadline -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Proyek Mendekati Deadline</h3>
                <form action="{{ route('dashboard') }}" method="GET" class="flex">
                    <select name="deadline_filter" id="deadline_filter" class="text-sm border rounded-md px-2 py-1" onchange="this.form.submit()">
                        <option value="1_day" {{ request('deadline_filter') == '1_day' ? 'selected' : '' }}>1 Hari</option>
                        <option value="1_week" {{ request('deadline_filter') == '1_week' || !request('deadline_filter') ? 'selected' : '' }}>1 Minggu</option>
                        <option value="1_month" {{ request('deadline_filter') == '1_month' ? 'selected' : '' }}>1 Bulan</option>
                    </select>
                </form>
            </div>
            <div class="space-y-4">
                @forelse($upcomingDeadlines as $project)
                    <div class="border-b pb-3">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-medium">{{ $project->name }}</h4>
                                <p class="text-sm text-gray-500">{{ $project->description }}</p>
                            </div>
                            <span class="text-xs px-2 py-1 rounded-full {{ $project->end_date->diffInDays(now()) <= 3 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ (int)$project->end_date->diffInDays(now()) }} hari tersisa
                            </span>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">Tidak ada proyek yang mendekati deadline</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Progress Proyek Aktif -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Progress Proyek Aktif</h3>
        <div class="space-y-4">
            @forelse($activeProjects as $project)
                <div class="border-b pb-4">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-medium">{{ $project['name'] }}</h4>
                        <span class="text-sm text-gray-500">{{ $project['progress'] }}%</span>
                    </div>
                    <div class="relative pt-1">
                        <div class="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                            <div style="width: {{ max(0, $project['progress']) }}%"
                                 class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center
                                        {{ $project['progress'] < 40 ? 'bg-red-500' : ($project['progress'] < 70 ? 'bg-yellow-500' : 'bg-green-500') }}">
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between mt-2">
                        <span class="text-xs text-gray-500">{{ $project['start_date'] }}</span>
                        <span class="text-xs text-gray-500">{{ $project['end_date'] }}</span>
                    </div>
                </div>
            @empty
                <p class="text-gray-500 text-center py-4">Tidak ada proyek aktif saat ini</p>
            @endforelse
        </div>
    </div>

    <!-- Update Status Terbaru -->
    <div class="mb-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold mb-4">Update Status Terbaru</h3>
            <div class="space-y-4">
                @forelse($recentStatusUpdates as $update)
                    <div class="border-b pb-4">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-medium text-gray-900">{{ $update['name'] }}</h4>
                                <p class="text-sm text-gray-600 mt-1">{{ $update['description'] }}</p>
                            </div>
                            <span class="text-xs px-2 py-1 rounded-full
                                {{ $update['status'] === 'completed' ? 'bg-green-100 text-green-800' :
                                   ($update['status'] === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                    ($update['status'] === 'on_hold' ? 'bg-yellow-100 text-yellow-800' :
                                     'bg-gray-100 text-gray-800')) }}">
                                {{ ucfirst(str_replace('_', ' ', $update['status'])) }}
                            </span>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">Diperbarui {{ $update['updated_at'] }}</p>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">Tidak ada update status terbaru</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Notifikasi Penting -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold mb-4">Notifikasi Penting</h3>
        <div class="space-y-4">
            @if($notifications['urgent_deadlines']->isNotEmpty())
                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-red-800">Proyek Mendekati Deadline (3 hari atau kurang)</h4>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    @foreach($notifications['urgent_deadlines'] as $project)
                                        <li>{{ $project->name }} - {{ $project->end_date->diffForHumans() }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            @if($notifications['stalled_projects']->isNotEmpty())
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-yellow-800">Proyek Tanpa Progress (7 hari atau lebih)</h4>
                            <div class="mt-2 text-sm text-yellow-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    @foreach($notifications['stalled_projects'] as $project)
                                        <li>{{ $project->name }} - Terakhir diupdate {{ $project->updated_at->diffForHumans() }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            @if($notifications['overdue_projects']->isNotEmpty())
                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-red-800">Proyek Melewati Deadline</h4>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    @foreach($notifications['overdue_projects'] as $project)
                                        <li>{{ $project->name }} - Terlambat {{ $project->end_date->diffForHumans() }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            @if($notifications['urgent_deadlines']->isEmpty() && 
                $notifications['stalled_projects']->isEmpty() && 
                $notifications['overdue_projects']->isEmpty())
                <p class="text-gray-500 text-center py-4">Tidak ada notifikasi penting saat ini</p>
            @endif
        </div>
    </div>

    @section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('projectStatusChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Direncanakan', 'Sedang Berjalan', 'Selesai', 'Ditunda'],
                datasets: [{
                    data: [
                        {{ $projectStatusDistribution['planned'] }},
                        {{ $projectStatusDistribution['in_progress'] }},
                        {{ $projectStatusDistribution['completed'] }},
                        {{ $projectStatusDistribution['on_hold'] }}
                    ],
                    backgroundColor: [
                        '#60A5FA', // Biru untuk Direncanakan
                        '#34D399', // Hijau untuk Sedang Berjalan
                        '#F472B6', // Pink untuk Selesai
                        '#FBBF24'  // Kuning untuk Ditunda
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
    </script>
    @endsection
@endsection 