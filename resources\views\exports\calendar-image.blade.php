<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Export - {{ $monthName }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            background: #ffffff;
            width: 1200px;
            height: 800px;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 3px solid #4F46E5;
        }
        
        .header h1 {
            font-size: 32px;
            color: #4F46E5;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .header h2 {
            font-size: 24px;
            color: #6B7280;
            font-weight: normal;
        }
        
        .calendar-container {
            width: 100%;
            margin: 0 auto;
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            border: 2px solid #374151;
        }
        
        .calendar-table th {
            background-color: #374151;
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 12px 8px;
            border: 1px solid #374151;
            font-size: 16px;
        }
        
        .calendar-table td {
            border: 1px solid #D1D5DB;
            vertical-align: top;
            height: 80px;
            width: 14.28%;
            padding: 5px;
            position: relative;
            background-color: #ffffff;
        }
        
        .day-number {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
            display: block;
        }
        
        .current-month {
            color: #111827;
        }
        
        .other-month {
            color: #9CA3AF;
            background-color: #F9FAFB;
        }
        
        .today {
            background-color: #FEF3C7 !important;
        }
        
        .today .day-number {
            color: #ffffff;
            background-color: #F59E0B;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }
        
        .event {
            font-size: 10px;
            padding: 2px 4px;
            margin: 1px 0;
            border-radius: 3px;
            color: white;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
            font-weight: bold;
        }
        
        .event-project-start {
            background-color: #10B981;
            border: 1px solid #059669;
        }
        
        .event-project-end {
            background-color: #EF4444;
            border: 1px solid #DC2626;
        }
        
        .event-calendar {
            background-color: #3B82F6;
            border: 1px solid #2563EB;
        }
        
        .legend {
            margin-top: 15px;
            padding: 15px;
            background-color: #F9FAFB;
            border-radius: 8px;
            border: 1px solid #E5E7EB;
        }
        
        .legend h3 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #374151;
            font-weight: bold;
        }
        
        .legend-items {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid #374151;
        }
        
        .legend-text {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }
        
        .footer {
            position: absolute;
            bottom: 10px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 12px;
            color: #6B7280;
            border-top: 1px solid #E5E7EB;
            padding-top: 8px;
        }
        
        /* Ensure high contrast for image export */
        .calendar-table th,
        .calendar-table td {
            border-width: 2px;
        }
        
        .event {
            border-width: 1px;
            border-style: solid;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DisaCloud Calendar</h1>
        <h2>{{ $monthName }}</h2>
    </div>

    <div class="calendar-container">
        <table class="calendar-table">
            <thead>
                <tr>
                    <th>Sunday</th>
                    <th>Monday</th>
                    <th>Tuesday</th>
                    <th>Wednesday</th>
                    <th>Thursday</th>
                    <th>Friday</th>
                    <th>Saturday</th>
                </tr>
            </thead>
            <tbody>
                @foreach($calendar as $week)
                <tr>
                    @foreach($week as $day)
                    <td class="{{ $day['isToday'] ? 'today' : '' }} {{ $day['isCurrentMonth'] ? '' : 'other-month' }}">
                        <div class="day-number {{ $day['isCurrentMonth'] ? 'current-month' : 'other-month' }}">
                            {{ $day['day'] }}
                        </div>
                        
                        @foreach(array_slice($day['events'], 0, 4) as $event)
                        <div class="event event-{{ $event['type'] == 'project_start' ? 'project-start' : ($event['type'] == 'project_end' ? 'project-end' : 'calendar') }}" 
                             title="{{ $event['title'] }}">
                            {{ Str::limit($event['title'], 15) }}
                        </div>
                        @endforeach
                        
                        @if(count($day['events']) > 4)
                        <div class="event" style="background-color: #6B7280; font-size: 9px;">
                            +{{ count($day['events']) - 4 }} more
                        </div>
                        @endif
                    </td>
                    @endforeach
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="legend">
        <h3>Legend</h3>
        <div class="legend-items">
            <div class="legend-item">
                <span class="legend-color" style="background-color: #10B981;"></span>
                <span class="legend-text">Project Start</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #EF4444;"></span>
                <span class="legend-text">Project End</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #3B82F6;"></span>
                <span class="legend-text">Calendar Event</span>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Generated on {{ now()->format('F d, Y \a\t H:i') }} | DisaCloud Project Management System</p>
    </div>
</body>
</html>
