<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Calendar - {{ $monthName }}</title>
    <style>
        body {
            font-family: Deja<PERSON>u Sans, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        
        .header h2 {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 18px;
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .calendar-table th {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .calendar-table td {
            border: 1px solid #ddd;
            vertical-align: top;
            height: 80px;
            width: 14.28%;
            padding: 4px;
        }
        
        .day-number {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .current-month {
            color: #000;
        }
        
        .other-month {
            color: #ccc;
        }
        
        .today {
            background-color: #fff3cd;
        }
        
        .event {
            font-size: 9px;
            padding: 1px 3px;
            margin: 1px 0;
            border-radius: 2px;
            color: white;
        }
        
        .event-project-start {
            background-color: #28a745;
        }
        
        .event-project-end {
            background-color: #dc3545;
        }
        
        .legend {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
        }
        
        .legend h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        
        .legend-item {
            display: inline-block;
            margin-right: 15px;
            font-size: 11px;
        }
        
        .legend-color {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 5px;
            vertical-align: middle;
        }
        
        .project-list {
            margin-top: 20px;
        }
        
        .project-list h3 {
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .project-item {
            margin-bottom: 8px;
            padding: 6px;
            background-color: #f8f9fa;
            border-left: 3px solid #007bff;
        }
        
        .project-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .project-dates {
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DisaCloud Calendar</h1>
        <h2>{{ $monthName }}</h2>
    </div>

    <table class="calendar-table">
        <thead>
            <tr>
                <th>Sunday</th>
                <th>Monday</th>
                <th>Tuesday</th>
                <th>Wednesday</th>
                <th>Thursday</th>
                <th>Friday</th>
                <th>Saturday</th>
            </tr>
        </thead>
        <tbody>
            @foreach($calendar as $week)
            <tr>
                @foreach($week as $day)
                <td class="{{ $day['isToday'] ? 'today' : '' }}">
                    <div class="day-number {{ $day['isCurrentMonth'] ? 'current-month' : 'other-month' }}">
                        {{ $day['day'] }}
                    </div>
                    
                    @foreach($day['events'] as $event)
                    <div class="event event-{{ str_replace('_', '-', $event['type']) }}">
                        {{ substr($event['title'], 0, 20) }}
                    </div>
                    @endforeach
                </td>
                @endforeach
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="legend">
        <h3>Legend</h3>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #28a745;"></span>
            Project Start
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #dc3545;"></span>
            Project End
        </div>
    </div>

    @if($projects->count() > 0)
    <div class="project-list">
        <h3>Projects in {{ $monthName }}</h3>
        @foreach($projects as $project)
        <div class="project-item">
            <div class="project-name">{{ $project->name }}</div>
            <div class="project-dates">
                @if($project->start_date)
                    Start: {{ $project->start_date->format('M d, Y') }}
                @endif
                @if($project->end_date)
                    @if($project->start_date) | @endif
                    End: {{ $project->end_date->format('M d, Y') }}
                @endif
            </div>
            @if($project->description)
            <div style="font-size: 10px; color: #666; margin-top: 3px;">
                {{ substr($project->description, 0, 100) }}{{ strlen($project->description) > 100 ? '...' : '' }}
            </div>
            @endif
        </div>
        @endforeach
    </div>
    @endif

    <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
        Generated on {{ now()->format('F d, Y \a\t H:i') }} | DisaCloud Project Management
    </div>
</body>
</html>
