<nav class="bg-white shadow-sm">
    <div class="flex justify-between items-center p-4">
        <div>
            <h2 class="text-xl font-semibold">@yield('title', 'Dashboard')</h2>
        </div>
        
        <div class="flex items-center space-x-4">
            
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white">
                        <span>{{ auth()->user()->name[0] ?? 'U' }}</span>
                    </div>
                    <span>{{ auth()->user()->name ?? 'User' }}</span>
                </button>
                
                <div x-show="open" 
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2">
                    <a href="{{ route('profile') }}" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Profile</a>
                    <a href="{{ route('user.create') }}" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Buat Akun Baru</a>
                    <form method="POST" action="{{ route('logout') }}" class="block">
                        @csrf
                        <button type="submit" class="block w-full text-left px-4 py-2 text-gray-800 hover:bg-gray-100">
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</nav> 