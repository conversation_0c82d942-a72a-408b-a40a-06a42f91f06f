<aside class="w-64 bg-indigo-800 text-white">
    <div class="p-4">
        <h1 class="text-xl font-bold">DisaCloud</h1>
    </div>
    
    <nav class="mt-6">
        <ul>
            <li>
                <a href="{{ route('dashboard') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('dashboard') ? 'bg-indigo-700' : '' }}">
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{{ route('projects.index') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('projects.index') ? 'bg-indigo-700' : '' }}">
                    Semua Proyek
                </a>
            </li>
            <li>
                <a href="{{ route('workers.index') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('workers.*') ? 'bg-indigo-700' : '' }}">
                    Workers
                </a>
            </li>
            <li x-data="{open: false}" class="relative">
                <button @click="open = !open" class="flex items-center justify-between w-full py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('budgets.*') ? 'bg-indigo-700' : '' }}">
                    <span>Budgets</span>
                    <svg class="w-4 h-4 transition-transform" :class="{'rotate-180': open}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
                <div x-show="open" class="pl-4 bg-indigo-900">
                    <a href="{{ route('budgets.index') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('budgets.index') ? 'bg-indigo-700' : '' }}">
                        Semua Budget
                    </a>
                    <a href="{{ route('budgets.create') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('budgets.create') ? 'bg-indigo-700' : '' }}">
                        Tambah Budget
                    </a>
                    <a href="{{ route('budgets.compare') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('budgets.compare') ? 'bg-indigo-700' : '' }}">
                        Bandingkan Budget
                    </a>
                </div>
            </li>
            <li>
                <a href="{{ route('projects.calendar') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('projects.calendar') ? 'bg-indigo-700' : '' }}">
                    Calendar
                </a>
            </li>
            <li>
                <a href="{{ route('reports.index') }}" class="block py-2 px-4 hover:bg-indigo-700 {{ request()->routeIs('reports.*') ? 'bg-indigo-700' : '' }}">
                    Laporan
                </a>
            </li>
        </ul>
    </nav>
</aside>