@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Edit Aktivitas</h1>
            <a href="{{ route('projects.activities.index', $project) }}" class="text-gray-600 hover:text-gray-900">
                Ke<PERSON><PERSON> ke Daftar
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <form action="{{ route('projects.activities.update', [$project, $activity]) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Judul Aktivitas</label>
                    <input type="text" name="title" id="title" class="form-input w-full rounded-md" value="{{ old('title', $activity->title) }}" required>
                    @error('title')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
                    <textarea name="description" id="description" rows="4" class="form-textarea w-full rounded-md">{{ old('description', $activity->description) }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status" class="form-select w-full rounded-md">
                            <option value="planned" {{ old('status', $activity->status) == 'planned' ? 'selected' : '' }}>Direncanakan</option>
                            <option value="in_progress" {{ old('status', $activity->status) == 'in_progress' ? 'selected' : '' }}>Sedang Berjalan</option>
                            <option value="completed" {{ old('status', $activity->status) == 'completed' ? 'selected' : '' }}>Selesai</option>
                            <option value="on_hold" {{ old('status', $activity->status) == 'on_hold' ? 'selected' : '' }}>Ditunda</option>
                        </select>
                        @error('status')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Prioritas</label>
                        <select name="priority" id="priority" class="form-select w-full rounded-md">
                            <option value="low" {{ old('priority', $activity->priority) == 'low' ? 'selected' : '' }}>Rendah</option>
                            <option value="medium" {{ old('priority', $activity->priority) == 'medium' ? 'selected' : '' }}>Sedang</option>
                            <option value="high" {{ old('priority', $activity->priority) == 'high' ? 'selected' : '' }}>Tinggi</option>
                        </select>
                        @error('priority')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Tanggal Mulai</label>
                        <input type="datetime-local" name="start_date" id="start_date" class="form-input w-full rounded-md" 
                            value="{{ old('start_date', $activity->start_date ? $activity->start_date->format('Y-m-d\TH:i') : '') }}">
                        @error('start_date')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="due_date" class="block text-sm font-medium text-gray-700 mb-1">Tenggat Waktu</label>
                        <input type="datetime-local" name="due_date" id="due_date" class="form-input w-full rounded-md"
                            value="{{ old('due_date', $activity->due_date ? $activity->due_date->format('Y-m-d\TH:i') : '') }}">
                        @error('due_date')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mb-6">
                    <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-1">Ditugaskan Kepada</label>
                    <select name="assigned_to" id="assigned_to" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">Pilih Pekerja</option>
                        @foreach($project->workers as $worker)
                            <option value="{{ $worker->id }}" {{ $activity->assigned_to == $worker->id ? 'selected' : '' }}>
                                {{ $worker->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('assigned_to')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection 