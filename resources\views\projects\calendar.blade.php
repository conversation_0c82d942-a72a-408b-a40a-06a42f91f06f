@extends('layouts.app')

@section('title', 'Calendar')
@section('header', 'Project Calendar')

@section('content')
    <div class="mb-6 flex justify-between items-center">
        <div class="flex space-x-2">
            <a href="{{ route('projects.create') }}" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Tambah Proyek
            </a>
            
            <select id="projectFilter" onchange="filterProjects(this.value)" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <option value="">Semua Proyek</option>
                @foreach($projects as $project)
                    <option value="{{ $project->id }}" {{ $selectedProjectId == $project->id ? 'selected' : '' }}>
                        {{ $project->name }}
                    </option>
                @endforeach
            </select>

            <a href="{{ route('calendar.export.form') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                Export Calendar
            </a>
        </div>
        
        <div class="flex space-x-2">
            <a href="{{ route('projects.calendar', ['date' => now()->format('Y-m-d'), 'project_id' => $selectedProjectId]) }}" 
               class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Hari Ini
            </a>
            
            <div class="flex items-center space-x-1">
                <a href="{{ route('projects.calendar', ['date' => $date->copy()->subMonth()->format('Y-m-d'), 'project_id' => $selectedProjectId]) }}" 
                   class="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
                
                <a href="{{ route('projects.calendar', ['date' => $date->copy()->addMonth()->format('Y-m-d'), 'project_id' => $selectedProjectId]) }}" 
                   class="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
            
            <select id="monthPicker" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                @for($i = 0; $i < 12; $i++)
                    @php
                        $monthDate = now()->startOfYear()->addMonths($i);
                    @endphp
                    <option value="{{ $monthDate->format('Y-m-d') }}" 
                            {{ $date->format('m') == $monthDate->format('m') ? 'selected' : '' }}>
                        {{ $monthDate->format('F Y') }}
                    </option>
                @endfor
            </select>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-4 bg-gray-50 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold">
                {{ $date->format('F Y') }}
                @if($selectedProjectId)
                    - {{ $projects->find($selectedProjectId)->name }}
                @endif
            </h2>
            <div class="text-sm text-gray-500 flex space-x-2">
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full">Mulai Proyek</span>
                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full">Deadline</span>
                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full">Sedang Berjalan</span>
            </div>
        </div>
        
        <div class="grid grid-cols-7 text-center border-b bg-gray-50">
            <div class="py-2 border-r font-medium text-gray-600">Min</div>
            <div class="py-2 border-r font-medium text-gray-600">Sen</div>
            <div class="py-2 border-r font-medium text-gray-600">Sel</div>
            <div class="py-2 border-r font-medium text-gray-600">Rab</div>
            <div class="py-2 border-r font-medium text-gray-600">Kam</div>
            <div class="py-2 border-r font-medium text-gray-600">Jum</div>
            <div class="py-2 font-medium text-gray-600">Sab</div>
        </div>
        
        <div class="grid grid-cols-7 grid-rows-5 h-screen max-h-[600px]">
            @php
                $firstDayOfMonth = $date->copy()->startOfMonth();
                $lastDayOfMonth = $date->copy()->endOfMonth();
                $firstDayOfWeek = $firstDayOfMonth->copy()->startOfWeek();
                $lastDayOfWeek = $lastDayOfMonth->copy()->endOfWeek();
                $currentDay = $firstDayOfWeek->copy();
            @endphp

            @while($currentDay <= $lastDayOfWeek)
                <div class="min-h-[100px] border-r border-b p-2 date-box relative" 
                     data-date="{{ $currentDay->format('Y-m-d') }}"
                     {{ $currentDay->format('m') != $date->format('m') ? 'bg-gray-50' : 'bg-white' }} 
                     {{ $currentDay->isToday() ? 'bg-blue-50' : '' }}>
                    <div class="font-semibold mb-2 {{ $currentDay->isToday() ? 'text-blue-600' : ($currentDay->format('m') != $date->format('m') ? 'text-gray-400' : 'text-gray-700') }}">
                        {{ $currentDay->format('j') }}
                    </div>
                    
                    <div class="space-y-1 max-h-[80px] overflow-y-auto custom-scrollbar">
                        @foreach($displayedProjects as $project)
                            @if($project->start_date && $project->start_date->format('Y-m-d') == $currentDay->format('Y-m-d'))
                                <div class="project-event relative bg-green-100 text-green-800 text-xs p-1.5 rounded shadow-sm cursor-move hover:bg-green-200 transition-colors"
                                     data-project-id="{{ $project->id }}"
                                     draggable="true">
                                    <div class="font-medium truncate">{{ $project->name }}</div>
                                    <div class="text-green-600 text-[10px]">Mulai Proyek</div>
                                    <div class="project-tooltip absolute z-50 w-64 p-3 bg-white text-gray-900 rounded-lg shadow-xl border border-gray-200 left-full top-0 ml-2">
                                        <h4 class="font-semibold text-sm mb-1">{{ $project->name }}</h4>
                                        <p class="text-xs text-gray-600 mb-2">{{ Str::limit($project->description, 100) }}</p>
                                        <div class="text-xs space-y-1">
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Mulai:</span>
                                                <span>{{ $project->start_date->format('d M Y') }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Selesai:</span>
                                                <span>{{ $project->end_date->format('d M Y') }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Status:</span>
                                                <span class="capitalize">{{ str_replace('_', ' ', $project->status) }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Progress:</span>
                                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 ml-1">
                                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: {{ $project->progress ?? 0 }}%"></div>
                                                </div>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($project->end_date && $project->end_date->format('Y-m-d') == $currentDay->format('Y-m-d'))
                                <div class="project-event bg-red-100 text-red-800 text-xs p-1.5 rounded shadow-sm cursor-move hover:bg-red-200 transition-colors group"
                                     data-project-id="{{ $project->id }}">
                                    <div class="font-medium truncate">{{ $project->name }}</div>
                                    <div class="text-red-600 text-[10px]">Deadline</div>
                                    <div class="hidden group-hover:block absolute z-50 w-64 p-3 bg-white text-gray-900 rounded-lg shadow-xl border border-gray-200 left-full top-0 ml-2">
                                        <h4 class="font-semibold text-sm mb-1">{{ $project->name }}</h4>
                                        <p class="text-xs text-gray-600 mb-2">{{ Str::limit($project->description, 100) }}</p>
                                        <div class="text-xs space-y-1">
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Mulai:</span>
                                                <span>{{ $project->start_date->format('d M Y') }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Selesai:</span>
                                                <span>{{ $project->end_date->format('d M Y') }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Status:</span>
                                                <span>{{ ucfirst(str_replace('_', ' ', $project->status)) }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($project->start_date && $project->end_date && 
                                $currentDay->between($project->start_date, $project->end_date))
                                <div class="project-event bg-indigo-100 text-indigo-800 text-xs p-1.5 rounded shadow-sm cursor-move hover:bg-indigo-200 transition-colors group"
                                     data-project-id="{{ $project->id }}">
                                    <div class="font-medium truncate">{{ $project->name }}</div>
                                    <div class="text-indigo-600 text-[10px]">{{ ucfirst(str_replace('_', ' ', $project->status)) }}</div>
                                    <div class="hidden group-hover:block absolute z-50 w-64 p-3 bg-white text-gray-900 rounded-lg shadow-xl border border-gray-200 left-full top-0 ml-2">
                                        <h4 class="font-semibold text-sm mb-1">{{ $project->name }}</h4>
                                        <p class="text-xs text-gray-600 mb-2">{{ Str::limit($project->description, 100) }}</p>
                                        <div class="text-xs space-y-1">
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Mulai:</span>
                                                <span>{{ $project->start_date->format('d M Y') }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Selesai:</span>
                                                <span>{{ $project->end_date->format('d M Y') }}</span>
                                            </p>
                                            <p class="flex items-center">
                                                <span class="w-16 text-gray-500">Status:</span>
                                                <span>{{ ucfirst(str_replace('_', ' ', $project->status)) }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @php
                    $currentDay->addDay();
                @endphp
            @endwhile
        </div>
    </div>
    
    <div class="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-4 bg-gray-50 border-b">
            <h2 class="text-lg font-semibold">Daftar Proyek Aktif</h2>
        </div>
        
        <div class="divide-y">
            @forelse($activeProjects as $project)
                <div class="p-4 flex items-start hover:bg-gray-50 transition-colors">
                    <div class="w-14 mr-4 text-center">
                        <div class="font-bold text-lg text-gray-700">{{ $project->start_date ? $project->start_date->format('d') : '-' }}</div>
                        <div class="text-sm text-gray-500">{{ $project->start_date ? $project->start_date->format('M') : '-' }}</div>
                    </div>
                    
                    <div class="flex-1">
                        <h3 class="font-medium text-lg text-gray-900">{{ $project->name }}</h3>
                        <p class="text-sm text-gray-600 mt-1">{{ $project->description }}</p>
                        <div class="mt-3 flex items-center flex-wrap gap-3">
                            <div class="text-xs px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full">
                                Sedang Berjalan
                            </div>
                            @if($project->start_date && $project->end_date)
                                <div class="text-xs text-gray-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                    </svg>
                                    {{ $project->start_date->format('d M Y') }} - {{ $project->end_date->format('d M Y') }}
                                </div>
                            @endif
                            
                            @php
                                $daysLeft = (int)now()->diffInDays($project->end_date, false);
                            @endphp
                            
                            <div class="text-xs {{ $daysLeft < 7 ? 'text-red-600' : 'text-gray-500' }} flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                </svg>
                                @if($daysLeft > 0)
                                    {{ $daysLeft }} hari tersisa
                                @elseif($daysLeft == 0)
                                    Deadline hari ini
                                @else
                                    Lewat {{ abs($daysLeft) }} hari
                                @endif
                            </div>
                        </div>
                    
                    <div class="flex space-x-3">
                        <a href="{{ route('projects.edit', $project) }}" class="text-gray-400 hover:text-indigo-600 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                </div>
            @empty
                <div class="p-6 text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd" />
                    </svg>
                    <p class="text-lg">Tidak ada proyek aktif saat ini.</p>
                    <p class="text-sm text-gray-400 mt-1">Semua proyek yang sedang berjalan akan muncul di sini.</p>
                </div>
            @endforelse
        </div>
    </div>

    @push('scripts')
    <script>
        document.getElementById('monthPicker').addEventListener('change', function() {
            window.location.href = "{{ route('projects.calendar') }}?date=" + this.value + "&project_id={{ $selectedProjectId }}";
        });

        function filterProjects(projectId) {
            window.location.href = "{{ route('projects.calendar') }}?date={{ $date->format('Y-m-d') }}&project_id=" + projectId;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const projectEvents = document.querySelectorAll('.project-event');
            const dateBoxes = document.querySelectorAll('.date-box');

            projectEvents.forEach(event => {
                event.addEventListener('dragstart', handleDragStart);
                event.addEventListener('dragend', handleDragEnd);
            });

            dateBoxes.forEach(box => {
                box.addEventListener('dragover', handleDragOver);
                box.addEventListener('dragenter', handleDragEnter);
                box.addEventListener('dragleave', handleDragLeave);
                box.addEventListener('drop', handleDrop);
            });
        });

        function handleDragStart(e) {
            e.target.classList.add('opacity-50');
            e.dataTransfer.setData('projectId', e.target.dataset.projectId);
            e.dataTransfer.setData('oldDate', e.target.closest('.date-box').dataset.date);
        }

        function handleDragEnd(e) {
            e.target.classList.remove('opacity-50');
            document.querySelectorAll('.date-box').forEach(box => {
                box.classList.remove('bg-gray-100', 'border-dashed', 'border-2', 'border-gray-400');
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragEnter(e) {
            e.preventDefault();
            e.currentTarget.classList.add('bg-gray-100', 'border-dashed', 'border-2', 'border-gray-400');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('bg-gray-100', 'border-dashed', 'border-2', 'border-gray-400');
        }

        function handleDrop(e) {
            e.preventDefault();
            const projectId = e.dataTransfer.getData('projectId');
            const oldDate = e.dataTransfer.getData('oldDate');
            const newDate = e.currentTarget.dataset.date;
            
            if (oldDate === newDate) return;

            const loadingToast = showToast('Memperbarui tanggal...', 'info');

            fetch(`/projects/${projectId}/update-date`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ old_date: oldDate, new_date: newDate })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Tanggal berhasil diperbarui', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Gagal memperbarui tanggal', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Terjadi kesalahan', 'error');
            })
            .finally(() => {
                loadingToast.remove();
            });
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                'bg-blue-500'
            } text-white z-50`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => toast.remove(), 3000);
            return toast;
        }
    </script>
    @endpush

    <style>
        .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        .project-tooltip {
            visibility: hidden;
            opacity: 0;
            transition: visibility 0s, opacity 0.2s ease-in-out;
        }
        
        .project-event:hover .project-tooltip {
            visibility: visible;
            opacity: 1;
        }
    </style>
@endsection 