@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        @if (session('success'))
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p>{{ session('success') }}</p>
        </div>
        @endif
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Edit Proyek</h1>
            <a href="{{ route('projects.index') }}" class="text-gray-600 hover:text-gray-900">
                Ke<PERSON>li ke Daftar
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <form action="{{ route('projects.update', $project) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1"><PERSON><PERSON></label>
                    <input type="text" name="name" id="name" class="form-input w-full rounded-md" value="{{ old('name', $project->name) }}" required>
                    @error('name')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
                    <textarea name="description" id="description" rows="4" class="form-textarea w-full rounded-md">{{ old('description', $project->description) }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" id="status" class="form-select w-full rounded-md">
                        <option value="planning" {{ old('status', $project->status) == 'planning' ? 'selected' : '' }}>Perencanaan</option>
                        <option value="in_progress" {{ old('status', $project->status) == 'in_progress' ? 'selected' : '' }}>Sedang Berjalan</option>
                        <option value="completed" {{ old('status', $project->status) == 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="on_hold" {{ old('status', $project->status) == 'on_hold' ? 'selected' : '' }}>Ditunda</option>
                    </select>
                    @error('status')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Tanggal Mulai</label>
                        <input type="date" name="start_date" id="start_date" class="form-input w-full rounded-md" 
                               value="{{ old('start_date', $project->start_date ? $project->start_date->format('Y-m-d') : '') }}">
                        @error('start_date')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">Tanggal Selesai</label>
                        <input type="date" name="end_date" id="end_date" class="form-input w-full rounded-md" 
                               value="{{ old('end_date', $project->end_date ? $project->end_date->format('Y-m-d') : '') }}">
                        @error('end_date')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Budget -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="budget_material" class="block text-sm font-medium text-gray-700 mb-1">Budget Material</label>
                        <input type="number" name="budget_material" id="budget_material" class="form-input w-full rounded-md" value="{{ old('budget_material', $project->budget_material) }}" step="0.01" placeholder="Masukkan budget material">
                        @error('budget_material')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="budget_jasa" class="block text-sm font-medium text-gray-700 mb-1">Budget Jasa</label>
                        <input type="number" name="budget_jasa" id="budget_jasa" class="form-input w-full rounded-md" value="{{ old('budget_jasa', $project->budget_jasa) }}" step="0.01" placeholder="Masukkan budget jasa">
                        @error('budget_jasa')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Tasks -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-3">Daftar Tugas</h3>
                    <div id="tasks_container" class="space-y-3">
                        @foreach($project->tasks as $index => $task)
                            <div class="flex items-center space-x-3 task-item existing-task-item" data-task-id="{{ $task->id }}">
                                <input type="hidden" name="tasks[{{ $task->id }}][id]" value="{{ $task->id }}">
                                <div class="flex-grow">
                                    <input type="text" name="tasks[{{ $task->id }}][name]" 
                                           class="form-input w-full rounded-md task-name-input" 
                                           value="{{ old('tasks.'.$task->id.'.name', $task->name) }}" 
                                           placeholder="Nama Tugas">
                                </div>
                                <button type="button" class="remove-existing-task-btn text-red-600 hover:text-red-800 px-2 py-1 rounded-md border border-red-300 hover:bg-red-50 text-sm">
                                    Hapus
                                </button>
                            </div>
                        @endforeach
                        <!-- New Task Template (hidden) -->
                        <div class="flex items-center space-x-3 task-item new-task-template" style="display: none;">
                            <div class="flex-grow">
                                <input type="text" 
                                       class="form-input w-full rounded-md task-name-input" 
                                       placeholder="Nama Tugas Baru">
                            </div>
                            <button type="button" class="remove-new-task-btn text-red-600 hover:text-red-800 px-2 py-1 rounded-md border border-red-300 hover:bg-red-50 text-sm">
                                Hapus
                            </button>
                        </div>
                    </div>
                    <button type="button" id="add_task_btn" class="mt-3 text-sm inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-1.5 -ml-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>
                        Tambah Tugas
                    </button>
                    <div id="deleted_tasks_container"></div> <!-- Container for hidden inputs for deleted tasks -->
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>

        <!-- Daftar Worker -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Daftar Pekerja</h2>
                <div class="flex space-x-4">
                    <select id="statusFilter" class="text-sm border rounded-md px-2 py-1">
                        <option value="all">Semua Status</option>
                        <option value="active">Aktif</option>
                        <option value="inactive">Tidak Aktif</option>
                    </select>
                    <button id="showSelectWorkersModal" class="bg-blue-500 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-600">
                        Tambah Pekerja
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nama & Kontak</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Posisi & Keahlian</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Periode Kerja</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($project->workers as $worker)
                        <tr class="worker-row" data-end-date="{{ $worker->end_date ? $worker->end_date->format('Y-m-d') : '' }}">
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $worker->name }}</div>
                                <div class="text-sm text-gray-500">{{ $worker->email }}</div>
                                <div class="text-sm text-gray-500">{{ $worker->phone }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $worker->specialization }}</div>
                                <div class="flex flex-wrap gap-1 mt-1">
                                    @foreach(explode(',', $worker->skills) as $skill)
                                        <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">{{ trim($skill) }}</span>
                                    @endforeach
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($worker->end_date && $worker->end_date->isPast())
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        Tidak Aktif
                                    </span>
                                @else
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Aktif
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    @if($worker->join_date)
                                        Mulai: {{ $worker->join_date->format('d M Y') }}
                                    @else
                                        Mulai: -
                                    @endif
                                </div>
                                @if($worker->end_date)
                                    <div class="text-sm text-gray-500">
                                        Selesai: {{ $worker->end_date->format('d M Y') }}
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{{ route('workers.edit', $worker) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                                <form action="{{ route('projects.workers.detach', [$project, $worker]) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Apakah Anda yakin ingin menghapus pekerja ini dari proyek?')">
                                        Hapus
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                Belum ada pekerja yang ditambahkan ke proyek ini
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Include the worker selection modal -->
@include('partials.select-workers-modal', ['availableWorkers' => $availableWorkers])

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const tasksContainer = document.getElementById('tasks_container');
    const addTaskBtn = document.getElementById('add_task_btn');
    const newTaskTemplate = document.querySelector('.new-task-template');
    const deletedTasksContainer = document.getElementById('deleted_tasks_container');
    let newTaskIndex = 0;

    addTaskBtn.addEventListener('click', function () {
        const newTaskItem = newTaskTemplate.cloneNode(true);
        newTaskItem.classList.remove('new-task-template');
        newTaskItem.style.display = 'flex';
        
        const input = newTaskItem.querySelector('.task-name-input');
        const newName = `tasks[new_${newTaskIndex}][name]`;
        input.name = newName;
        input.placeholder = `Nama Tugas Baru ${newTaskIndex + 1}`;
        // input.value = ''; // Ensure it's empty

        newTaskItem.querySelector('.remove-new-task-btn').addEventListener('click', function () {
            newTaskItem.remove();
        });

        tasksContainer.appendChild(newTaskItem);
        newTaskIndex++;
    });

    // Event delegation for removing existing tasks
    tasksContainer.addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-existing-task-btn')) {
            const taskItem = event.target.closest('.existing-task-item');
            if (taskItem) {
                const taskId = taskItem.dataset.taskId;
                if (taskId) {
                    // Hide the task item
                    taskItem.style.display = 'none';
                    // Add a hidden input to mark for deletion
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'delete_tasks[]';
                    hiddenInput.value = taskId;
                    deletedTasksContainer.appendChild(hiddenInput);
                }
            }
        }
        // For newly added tasks that might be cloned with the 'remove-existing-task-btn' if template is not perfect
        // The specific 'remove-new-task-btn' should handle new ones primarily.
        else if (event.target.classList.contains('remove-new-task-btn')) {
             const taskItem = event.target.closest('.task-item');
             if (taskItem && !taskItem.classList.contains('new-task-template')) { // Ensure not removing the template itself
                taskItem.remove();
             }
        }
    });

});
</script>

<script>
// Status filter functionality
document.getElementById('statusFilter').addEventListener('change', function() {
    const status = this.value;
    const rows = document.querySelectorAll('.worker-row');
    
    rows.forEach(row => {
        const endDate = row.querySelector('[data-end-date]');
        const isActive = endDate ? new Date(endDate.dataset.endDate) > new Date() : true;
        
        if (status === 'all' || 
            (status === 'active' && isActive) || 
            (status === 'inactive' && !isActive)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Modal functionality
const modal = document.getElementById('selectWorkersModal');
const showModalBtn = document.getElementById('showSelectWorkersModal');
const closeModalBtn = document.getElementById('closeModal');
const cancelBtn = document.getElementById('cancelSelection');
const searchInput = document.getElementById('workerSearch');

showModalBtn.addEventListener('click', function() {
    modal.classList.remove('hidden');
});

function closeModal() {
    modal.classList.add('hidden');
}

closeModalBtn.addEventListener('click', closeModal);
cancelBtn.addEventListener('click', closeModal);

// Close modal when clicking outside of it
modal.addEventListener('click', function(e) {
    if (e.target === modal) {
        closeModal();
    }
});

// Search functionality
searchInput.addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const workerItems = document.querySelectorAll('.worker-item');
    
    workerItems.forEach(item => {
        const workerName = item.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const workerSpecialization = item.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const workerSkills = item.querySelector('td:nth-child(4)').textContent.toLowerCase();
        
        if (workerName.includes(searchTerm) || workerSpecialization.includes(searchTerm) || workerSkills.includes(searchTerm)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
});
</script>
@endpush
@endsection 