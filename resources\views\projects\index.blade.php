@extends('layouts.app')

@section('title', 'Projects')
@section('header', 'Projects')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Daftar Proyek</h1>
        <a href="{{ route('projects.create') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
            Tambah Proyek Baru
        </a>
    </div>

    <!-- Filter Section -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form action="{{ route('projects.index') }}" method="GET" id="filterForm">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Filter Status</label>
                    <select name="status" id="status" onchange="this.form.submit()" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">Semua Status</option>
                        <option value="in_progress" {{ $status === 'in_progress' ? 'selected' : '' }}>Sedang Berjalan</option>
                        <option value="planned" {{ $status === 'planned' ? 'selected' : '' }}>Direncanakan</option>
                        <option value="completed" {{ $status === 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="on_hold" {{ $status === 'on_hold' ? 'selected' : '' }}>Ditunda</option>
                    </select>
                </div>
                @if($status)
                <div class="flex items-end">
                    <a href="{{ route('projects.index') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                        Reset
                    </a>
                </div>
                @endif
            </div>
        </form>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('success') }}</span>
    </div>
    @endif

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($projects as $project)
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">{{ $project->name }}</h2>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        @if($project->status === 'completed') bg-green-100 text-green-800
                        @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                        @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                    </span>
                </div>
                
                <p class="text-gray-600 mb-4">{{ Str::limit($project->description, 100) }}</p>
                
                <div class="text-sm text-gray-500 mb-4">
                    <div class="mb-1">
                        <span class="font-medium">Mulai:</span> 
                        {{ $project->start_date ? $project->start_date->format('d M Y') : 'Belum ditentukan' }}
                    </div>
                    <div>
                        <span class="font-medium">Selesai:</span> 
                        {{ $project->end_date ? $project->end_date->format('d M Y') : 'Belum ditentukan' }}
                    </div>
                </div>

                <div class="flex justify-between items-center">
                    <div class="space-x-2">
                        <a href="{{ route('projects.edit', $project) }}" class="text-green-600 hover:text-green-900">Edit</a>
                    </div>
                    <div class="flex space-x-2">
                        @if($project->status !== 'completed')
                        <form action="{{ route('projects.mark-completed', $project) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="text-blue-600 hover:text-blue-900" 
                                onclick="return confirm('Apakah Anda yakin ingin menandai proyek ini sebagai selesai?')">
                                Selesai
                            </button>
                        </form>
                        @endif
                        <form action="{{ route('projects.destroy', $project) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:text-red-900" 
                                onclick="return confirm('Apakah Anda yakin ingin menghapus proyek ini?')">
                                Hapus
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-3 text-center py-8">
            <p class="text-gray-500">Belum ada proyek yang dibuat</p>
        </div>
        @endforelse
    </div>
</div>
@endsection 