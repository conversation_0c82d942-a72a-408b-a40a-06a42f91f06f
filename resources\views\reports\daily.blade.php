@extends('layouts.app')

@section('title', 'Daily Reports')
@section('header', 'Daily Reports')

@section('content')
    <div class="mb-6 flex justify-between items-center">
        <div class="flex space-x-2">
            <button class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Generate Report
            </button>
            
            <button class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Export PDF
            </button>
            
            <button class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Export Excel
            </button>
        </div>
        
        <div class="flex space-x-2">
            <div class="relative">
                <input type="date" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" value="{{ date('Y-m-d') }}">
            </div>
            
            <select class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <option value="">All Projects</option>
                @foreach($projects ?? [] as $project)
                    <option value="{{ $project->id }}">{{ $project->name }}</option>
                @endforeach
            </select>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Total Projects</h3>
            <p class="text-3xl font-bold text-indigo-600">{{ $totalProjects ?? 0 }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Projects in Progress</h3>
            <p class="text-3xl font-bold text-blue-600">{{ $inProgressProjects ?? 0 }}</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold mb-2">Today's Budget</h3>
            <p class="text-3xl font-bold text-green-600">{{ $todayBudget ?? '$0' }}</p>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        <div class="p-4 bg-gray-50 border-b">
            <h2 class="text-lg font-semibold">Project Status Overview</h2>
        </div>
        
        <div class="p-6">
            <div class="h-64">
                <!-- Placeholder for chart - in a real app, you would use Chart.js or similar -->
                <div class="bg-gray-100 h-full flex items-center justify-center text-gray-500">
                    Project Status Chart
                </div>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="p-4 bg-gray-50 border-b">
                <h2 class="text-lg font-semibold">Today's Activities</h2>
            </div>
            
            <div class="divide-y">
                @forelse($activities ?? [] as $activity)
                    <div class="p-4">
                        <div class="flex justify-between">
                            <h4 class="font-medium">{{ $activity->title ?? 'Activity' }}</h4>
                            <span class="text-xs text-gray-500">{{ $activity->time ?? 'Today' }}</span>
                        </div>
                        <p class="text-sm text-gray-500">{{ $activity->description ?? 'No description provided' }}</p>
                        <div class="mt-2">
                            <span class="text-xs px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full">
                                {{ $activity->project->name ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                @empty
                    <div class="p-6 text-center text-gray-500">
                        No activities recorded for today.
                    </div>
                @endforelse
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="p-4 bg-gray-50 border-b">
                <h2 class="text-lg font-semibold">Today's Due Tasks</h2>
            </div>
            
            <div class="divide-y">
                @forelse($tasks ?? [] as $task)
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mr-3">
                            <h4 class="font-medium">{{ $task->title ?? 'Task' }}</h4>
                        </div>
                        <p class="text-sm text-gray-500 ml-7">{{ $task->description ?? 'No description provided' }}</p>
                        <div class="mt-2 ml-7 flex justify-between">
                            <span class="text-xs px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full">
                                {{ $task->project->name ?? 'N/A' }}
                            </span>
                            <span class="text-xs px-2 py-1 {{ $task->priority == 'high' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }} rounded-full">
                                {{ ucfirst($task->priority ?? 'Normal') }} Priority
                            </span>
                        </div>
                    </div>
                @empty
                    <div class="p-6 text-center text-gray-500">
                        No tasks due for today.
                    </div>
                @endforelse
            </div>
        </div>
    </div>
@endsection 