@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit <PERSON><PERSON></h1>
                <p class="mt-2 text-sm text-gray-500">{{ $project->name }} - {{ $report->report_date->format('d M Y') }}</p>
            </div>
            <a href="{{ route('reports.show', $project) }}" class="text-blue-600 hover:text-blue-800">
                Kembali ke Daftar Laporan
            </a>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form action="{{ route('reports.update', [$project, $report]) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="px-4 py-5 space-y-6 sm:p-6">
                    <div>
                        <label for="activities_done" class="block text-sm font-medium text-gray-700">
                            Aktivitas yang <PERSON>
                        </label>
                        <div class="mt-1">
                            <textarea id="activities_done" name="activities_done" rows="4" 
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                                placeholder="Jelaskan aktivitas yang telah dilakukan hari ini">{{ old('activities_done', $report->activities_done) }}</textarea>
                        </div>
                        @error('activities_done')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="challenges" class="block text-sm font-medium text-gray-700">
                            Kendala yang Dihadapi
                        </label>
                        <div class="mt-1">
                            <textarea id="challenges" name="challenges" rows="3" 
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                                placeholder="Jelaskan kendala atau tantangan yang dihadapi (opsional)">{{ old('challenges', $report->challenges) }}</textarea>
                        </div>
                        @error('challenges')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="next_plan" class="block text-sm font-medium text-gray-700">
                            Rencana Selanjutnya
                        </label>
                        <div class="mt-1">
                            <textarea id="next_plan" name="next_plan" rows="3" 
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                                placeholder="Jelaskan rencana untuk hari berikutnya">{{ old('next_plan', $report->next_plan) }}</textarea>
                        </div>
                        @error('next_plan')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    {{-- Tasks Checklist --}}
                    @if(isset($tasks) && $tasks->count() > 0)
                    <div class="pt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Status Tugas Proyek
                        </label>
                        <div class="mt-2 space-y-3 bg-white p-4 border border-gray-200 rounded-md">
                            @foreach($tasks as $task)
                            <div class="flex items-center">
                                <input id="task_{{ $task->id }}"
                                       name="tasks_completed[{{ $task->id }}]"
                                       type="checkbox"
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                                       value="1"
                                       {{ $task->completed ? 'checked' : '' }}>
                                <label for="task_{{ $task->id }}" class="ml-3 block text-sm text-gray-700">
                                    {{ $task->name }}
                                </label>
                            </div>
                            @endforeach
                        </div>
                        @error('tasks_completed')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @foreach($tasks as $task)
                            @error('tasks_completed.'.$task->id)
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        @endforeach
                    </div>
                    @endif
                    {{-- End Tasks Checklist --}}

                    <div>
                        <label for="progress_percentage" class="block text-sm font-medium text-gray-700">
                            Progress (%)
                        </label>
                        <div class="mt-1">
                            <input type="number" name="progress_percentage" id="progress_percentage"
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md bg-gray-100"
                                min="0" max="100" value="{{ old('progress_percentage', $report->progress_percentage) }}" readonly>
                        </div>
                        @error('progress_percentage')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                    <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="tasks_completed"]');
    const progressInput = document.getElementById('progress_percentage');
    function updateProgress() {
        const total = checkboxes.length;
        if (total === 0) {
            progressInput.value = 0;
            return;
        }
        let checked = 0;
        checkboxes.forEach(cb => { if (cb.checked) checked++; });
        const percent = Math.round((checked / total) * 100);
        progressInput.value = percent;
    }
    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateProgress);
    });
    updateProgress(); // Inisialisasi saat halaman dimuat
});
</script>
@endsection 