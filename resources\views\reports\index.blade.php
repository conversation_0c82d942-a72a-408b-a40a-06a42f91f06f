@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-gray-900"><PERSON><PERSON><PERSON> Proyek</h1>
            <p class="mt-2 text-sm text-gray-500"><PERSON>lih proyek untuk melihat atau membuat laporan</p>
        </div>

        @if(session('error'))
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p>{{ session('error') }}</p>
        </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($projects as $project)
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">{{ $project->name }}</h2>
                        <span class="px-2 py-1 text-xs font-medium rounded-full
                            @if($project->status === 'completed') bg-green-100 text-green-800
                            @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                            @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                        </span>
                    </div>
                    
                    <p class="text-gray-600 mb-4">{{ Str::limit($project->description, 100) }}</p>
                    
                    <div class="text-sm text-gray-500 mb-4">
                        <div class="mb-1">
                            <span class="font-medium">Mulai:</span> 
                            {{ $project->start_date ? $project->start_date->format('d M Y') : 'Belum ditentukan' }}
                        </div>
                        <div>
                            <span class="font-medium">Selesai:</span> 
                            {{ $project->end_date ? $project->end_date->format('d M Y') : 'Belum ditentukan' }}
                        </div>
                    </div>

                    <a href="{{ route('reports.show', $project) }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Lihat Laporan
                    </a>
                </div>
            </div>
            @empty
            <div class="col-span-3">
                <div class="text-center py-12 bg-white rounded-lg shadow-sm">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada proyek aktif</h3>
                    <p class="mt-1 text-sm text-gray-500">Semua proyek yang sedang berjalan akan muncul di sini</p>
                </div>
            </div>
            @endforelse
        </div>
    </div>
</div>
@endsection 