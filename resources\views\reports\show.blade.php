@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $project->name }}</h1>
                <p class="mt-2 text-sm text-gray-500"><PERSON>poran <PERSON>k</p>
            </div>
            <a href="{{ route('reports.index') }}" class="text-blue-600 hover:text-blue-800">
                Kembali ke Daftar Proyek
            </a>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Informasi Proyek</h3>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Periode</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $project->start_date->format('d M Y') }} - {{ $project->end_date->format('d M Y') }}
                        </dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($project->status === 'completed') bg-green-100 text-green-800
                                @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                                @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                                @else bg-gray-100 text-gray-800
                                @endif">
                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
            <div class="border-t border-gray-200">
                <div class="px-4 py-5">
                    <h4 class="text-md font-semibold mb-2">Daftar Tugas Proyek</h4>
                    <ul class="space-y-2">
                        @forelse($tasks as $task)
                        <li class="flex items-center justify-between">
                            <span>{{ $task->name }}</span>
                            @if($task->status === 'completed' || $task->completed)
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-700">Selesai</span>
                            @else
                                <span class="px-2 py-1 text-xs rounded-full bg-gray-200 text-gray-700">Belum Selesai</span>
                            @endif
                        </li>
                        @empty
                        <li class="text-sm text-gray-500">Belum ada tugas pada proyek ini.</li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Daftar Laporan</h3>
                @php
                    $today = \Carbon\Carbon::today();
                    $canCreateTodayReport = $project->start_date->startOfDay() <= $today && $project->end_date->startOfDay() >= $today;
                    $todayReport = $project->dailyReports()->whereDate('report_date', $today)->first();
                @endphp
                
                @if($canCreateTodayReport && !$todayReport)
                    <a href="{{ route('reports.create', ['project' => $project, 'date' => $today->format('Y-m-d')]) }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Buat Laporan Hari Ini
                    </a>
                @endif
            </div>
            <div class="border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                    @foreach($reportDates as $item)
                    <div class="border rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200 
                        {{ $item['report'] ? 'border-green-300 bg-green-50' : 
                          ($item['is_past_or_today'] ? 'border-gray-300' : 'border-gray-200 bg-gray-50') }}">
                        <div class="px-4 py-3 {{ $item['is_past_or_today'] ? 'bg-gray-100' : 'bg-gray-200' }} border-b flex justify-between items-center">
                            <h4 class="text-sm font-medium text-gray-900">
                                {{ $item['date']->format('d M Y') }}
                                @if($item['date']->isToday())
                                    <span class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                        Hari Ini
                                    </span>
                                @endif
                            </h4>
                            <span class="text-xs text-gray-500">{{ $item['date']->isoFormat('dddd') }}</span>
                        </div>
                        <div class="p-4">
                            @if($item['report'])
                                <div class="mb-3">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-700">Progress:</span>
                                        <span class="text-sm font-medium {{ $item['report']->progress_percentage >= 75 ? 'text-green-600' : ($item['report']->progress_percentage >= 50 ? 'text-blue-600' : 'text-yellow-600') }}">{{ $item['report']->progress_percentage }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $item['report']->progress_percentage }}%"></div>
                                    </div>
                                </div>
                                @if($item['report']->activities_done)
                                    <div class="mb-2">
                                        <h5 class="text-xs font-medium text-gray-700 mb-1">Aktivitas:</h5>
                                        <p class="text-sm text-gray-600">{{ Str::limit($item['report']->activities_done, 100) }}</p>
                                    </div>
                                @endif
                                @if($item['report']->challenges)
                                    <div class="mb-2">
                                        <h5 class="text-xs font-medium text-gray-700 mb-1">Tantangan:</h5>
                                        <p class="text-sm text-gray-600">{{ Str::limit($item['report']->challenges, 100) }}</p>
                                    </div>
                                @endif
                                <div class="mt-3 text-right">
                                    <a href="{{ route('reports.edit', ['project' => $project, 'report' => $item['report']]) }}" 
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                                        Edit Laporan
                                    </a>
                                </div>
                            @else
                                <div class="flex flex-col items-center justify-center py-4">
                                    <svg class="h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                    <p class="text-sm text-gray-500 mb-3">Belum ada laporan</p>
                                    @if($item['is_past_or_today'])
                                        <a href="{{ route('reports.create', ['project' => $project, 'date' => $item['date']->format('Y-m-d')]) }}" 
                                           class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                            Buat Laporan
                                        </a>
                                    @else
                                        <span class="text-xs text-gray-500">Tanggal di masa depan</span>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 