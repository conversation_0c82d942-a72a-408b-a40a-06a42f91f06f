@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Daftar Pekerja</h1>
                    <p class="mt-2 text-sm text-gray-500"><PERSON><PERSON>la semua pekerja yang terlibat dalam proyek</p>
                </div>
                <a href="{{ route('workers.create') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    Tambah Pekerja
                </a>
            </div>
        </div>

        <!-- Filter dan Search -->
        <div class="mb-6 flex justify-between items-center">
            <div class="flex space-x-4">
                <select id="project-filter" class="border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Semua Proyek</option>
                    @foreach($projects ?? [] as $project)
                        <option value="{{ $project->id }}">{{ $project->name }}</option>
                    @endforeach
                </select>
                <select id="status-filter" class="border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Semua Status</option>
                    <option value="active">Aktif</option>
                    <option value="inactive">Tidak Aktif</option>
                </select>
            </div>
            <div class="flex-1 max-w-sm ml-4">
                <input type="text" 
                       id="search-input"
                       placeholder="Cari pekerja..." 
                       class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
        </div>

        <!-- Daftar Pekerja -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                @forelse($workers ?? [] as $worker)
                <li class="worker-item" data-end-date="{{ $worker->end_date }}" data-projects="{{ $worker->projects->pluck('id')->implode(',') }}">
                    <div class="px-4 py-4 flex items-center sm:px-6">
                        <div class="min-w-0 flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <div class="flex text-sm">
                                    <p class="font-medium text-blue-600 truncate">{{ $worker->name }}</p>
                                    <p class="ml-1 flex-shrink-0 font-normal text-gray-500">
                                        - {{ $worker->specialization }}
                                    </p>
                                    @php
                                        $isActive = !$worker->end_date || \Carbon\Carbon::parse($worker->end_date)->isFuture();
                                    @endphp
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $isActive ? 'Aktif' : 'Tidak Aktif' }}
                                    </span>
                                </div>
                                <div class="mt-2 flex">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <p>{{ $worker->email }}</p>
                                        @if($worker->phone)
                                            <span class="mx-2">•</span>
                                            <p>{{ $worker->phone }}</p>
                                        @endif
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <p>Bergabung: {{ $worker->join_date ? $worker->join_date->format('d/m/Y') : '-' }}</p>
                                        @if($worker->end_date)
                                            <span class="mx-2">•</span>
                                            <p>Berakhir: {{ $worker->end_date->format('d/m/Y') }}</p>
                                        @endif
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">Proyek:</p>
                                    <div class="flex flex-wrap gap-2 mt-1">
                                        @forelse($worker->projects as $project)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $project->name }}
                                            </span>
                                        @empty
                                            <span class="text-sm text-gray-500">Belum ada proyek</span>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 flex-shrink-0 sm:mt-0 sm:ml-5">
                                <div class="flex space-x-4">
                                    <a href="{{ route('workers.edit', $worker) }}" 
                                       class="text-gray-400 hover:text-gray-500">
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828l-11.314 11.314a4 4 0 01-1.414 1.414L3 21l1.172-3.586a4 4 0 011.414-1.414L16.172 5.414z"/>
                                        </svg>
                                    </a>
                                    <form action="{{ route('workers.destroy', $worker) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="text-gray-400 hover:text-red-500"
                                                onclick="return confirm('Apakah Anda yakin ingin menghapus pekerja ini?')">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                @empty
                <li class="px-4 py-5 text-center text-gray-500">
                    Belum ada pekerja yang ditambahkan
                </li>
                @endforelse
            </ul>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const projectFilter = document.getElementById('project-filter');
    const statusFilter = document.getElementById('status-filter');
    const searchInput = document.getElementById('search-input');
    const workerItems = document.querySelectorAll('.worker-item');

    function filterWorkers() {
        const selectedProject = projectFilter.value;
        const selectedStatus = statusFilter.value;
        const searchTerm = searchInput.value.toLowerCase().trim();

        workerItems.forEach(item => {
            const endDate = item.dataset.endDate;
            const isActive = !endDate || new Date(endDate) > new Date();
            const workerProjects = item.dataset.projects ? item.dataset.projects.split(',') : [];
            
            // Get worker name and other searchable text
            const workerName = item.querySelector('.font-medium').textContent.toLowerCase();
            const workerSpecialization = item.querySelector('.text-gray-500')?.textContent.toLowerCase() || '';
            const workerText = workerName + ' ' + workerSpecialization;
            
            let showItem = true;

            // Filter by status
            if (selectedStatus === 'active' && !isActive) {
                showItem = false;
            } else if (selectedStatus === 'inactive' && isActive) {
                showItem = false;
            }

            // Filter by project
            if (showItem && selectedProject && selectedProject !== '') {
                showItem = workerProjects.includes(selectedProject);
            }
            
            // Filter by search term
            if (showItem && searchTerm !== '') {
                showItem = workerText.includes(searchTerm);
            }

            // Show or hide the worker item
            if (showItem) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Add event listeners for all filters
    projectFilter.addEventListener('change', filterWorkers);
    statusFilter.addEventListener('change', filterWorkers);
    searchInput.addEventListener('input', filterWorkers);
    
    // Initialize filters on page load
    filterWorkers();
});
</script>
@endpush

@endsection 