<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\WorkerController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\BudgetController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\MaterialController;
use App\Http\Controllers\DailyExpenseController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;

// Authentication Routes
Route::get('/', [AuthController::class, 'showLandingPage'])->name('landing');

// Guest routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});

// Logout route
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    Route::prefix('projects')->group(function () {
    Route::get('/', [ProjectController::class, 'index'])->name('projects.index');
    Route::get('/create', [ProjectController::class, 'create'])->name('projects.create');
    Route::post('/', [ProjectController::class, 'store'])->name('projects.store');
    Route::get('/{project}/edit', [ProjectController::class, 'edit'])->name('projects.edit');
    Route::put('/{project}', [ProjectController::class, 'update'])->name('projects.update');
    Route::delete('/{project}', [ProjectController::class, 'destroy'])->name('projects.destroy');
    Route::post('/{project}/mark-completed', [ProjectController::class, 'markAsCompleted'])->name('projects.mark-completed');

    // Old budget route - will be redirected
    Route::get('/budgets', function() {
        return redirect()->route('budgets.index');
    })->name('projects.budgets');
    Route::get('/calendar', [ProjectController::class, 'calendar'])->name('projects.calendar');
    
    // Activities routes removed

    // Workers routes
    Route::get('/{project}/workers', [WorkerController::class, 'index'])->name('projects.workers.index');
    Route::get('/{project}/workers/create', [WorkerController::class, 'create'])->name('projects.workers.create');
    Route::post('/{project}/workers', [WorkerController::class, 'store'])->name('projects.workers.store');
    Route::post('/{project}/workers/add-existing', [WorkerController::class, 'addExistingWorkers'])->name('projects.workers.add-existing');
    Route::get('/{project}/workers/{worker}/edit', [WorkerController::class, 'edit'])->name('projects.workers.edit');
    Route::put('/{project}/workers/{worker}', [WorkerController::class, 'update'])->name('projects.workers.update');
    Route::delete('/{project}/workers/{worker}', [WorkerController::class, 'destroy'])->name('projects.workers.destroy');
    Route::delete('/{project}/workers/{worker}/detach', [WorkerController::class, 'detachFromProject'])->name('projects.workers.detach');

    Route::post('/projects/{project}/update-date', [ProjectController::class, 'updateDate'])->name('projects.update-date');
});

    // Global Workers routes
    Route::get('/workers', [WorkerController::class, 'index'])->name('workers.index');
    Route::get('/workers/create', [WorkerController::class, 'create'])->name('workers.create');
    Route::post('/workers', [WorkerController::class, 'store'])->name('workers.store');
    Route::get('/workers/{worker}/edit', [WorkerController::class, 'edit'])->name('workers.edit');
    Route::put('/workers/{worker}', [WorkerController::class, 'update'])->name('workers.update');
    Route::delete('/workers/{worker}', [WorkerController::class, 'destroy'])->name('workers.destroy');

    // Budget Management Routes
    Route::prefix('budgets')->group(function () {
        Route::get('/', [BudgetController::class, 'index'])->name('budgets.index');
        Route::get('/create', [BudgetController::class, 'create'])->name('budgets.create');
        Route::get('/compare', [BudgetController::class, 'compare'])->name('budgets.compare');
        Route::post('/', [BudgetController::class, 'store'])->name('budgets.store');
        Route::get('/{budget}/download-invoice', [BudgetController::class, 'downloadInvoice'])->name('budgets.download-invoice');
        Route::get('/{budget}/edit', [BudgetController::class, 'edit'])->name('budgets.edit');
        Route::put('/{budget}', [BudgetController::class, 'update'])->name('budgets.update');
        Route::delete('/{budget}', [BudgetController::class, 'destroy'])->name('budgets.destroy');
        Route::get('/{budget}', [BudgetController::class, 'show'])->name('budgets.show');
    });

    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('reports.index');
        Route::get('/{project}', [ReportController::class, 'show'])->name('reports.show');
        Route::get('/{project}/create', [ReportController::class, 'create'])->name('reports.create');
        Route::post('/{project}', [ReportController::class, 'store'])->name('reports.store');
        Route::get('/{project}/reports/{report}/edit', [ReportController::class, 'edit'])->name('reports.edit');
        Route::put('/{project}/reports/{report}', [ReportController::class, 'update'])->name('reports.update');
        Route::get('/{project}/dashboard', [ReportController::class, 'dashboard'])->name('reports.dashboard');
        Route::patch('/{project}/tasks/{task}/status', [ReportController::class, 'updateTaskStatus'])->name('reports.tasks.update-status');
    });
    
    // Task Management Routes
    Route::prefix('tasks')->group(function () {
        Route::get('/', [TaskController::class, 'index'])->name('tasks.index');
        Route::get('/create', [TaskController::class, 'create'])->name('tasks.create');
        Route::post('/', [TaskController::class, 'store'])->name('tasks.store');
        Route::get('/{task}', [TaskController::class, 'show'])->name('tasks.show');
        Route::get('/{task}/edit', [TaskController::class, 'edit'])->name('tasks.edit');
        Route::put('/{task}', [TaskController::class, 'update'])->name('tasks.update');
        Route::delete('/{task}', [TaskController::class, 'destroy'])->name('tasks.destroy');
        Route::patch('/{task}/status', [TaskController::class, 'updateStatus'])->name('tasks.update-status');
    });
    
    // Material Management Routes
    Route::prefix('materials')->group(function () {
        Route::get('/', [MaterialController::class, 'index'])->name('materials.index');
        Route::get('/create', [MaterialController::class, 'create'])->name('materials.create');
        Route::post('/', [MaterialController::class, 'store'])->name('materials.store');
        Route::get('/{material}', [MaterialController::class, 'show'])->name('materials.show');
        Route::get('/{material}/edit', [MaterialController::class, 'edit'])->name('materials.edit');
        Route::put('/{material}', [MaterialController::class, 'update'])->name('materials.update');
        Route::delete('/{material}', [MaterialController::class, 'destroy'])->name('materials.destroy');
        Route::get('/project/{projectId}', [MaterialController::class, 'getProjectMaterials'])->name('materials.project');
    });

    Route::get('/projects/calendar/export', [ProjectController::class, 'exportCalendar'])->name('projects.calendar.export');

    Route::get('/calendar', [CalendarController::class, 'index'])->name('calendar.index');
    Route::get('/calendar/events', [CalendarController::class, 'events'])->name('calendar.events');
    Route::get('/calendar/export-form', [App\Http\Controllers\CalendarExportController::class, 'showExportForm'])->name('calendar.export.form');
    Route::get('/calendar/export', [App\Http\Controllers\CalendarExportController::class, 'export'])->name('calendar.export');
    
    // Daily Expenses Management Routes
    Route::prefix('daily-expenses')->group(function () {
        Route::get('/', [DailyExpenseController::class, 'index'])->name('daily_expenses.index');
        Route::get('/create', [DailyExpenseController::class, 'create'])->name('daily_expenses.create');
        Route::post('/', [DailyExpenseController::class, 'store'])->name('daily_expenses.store');
        Route::get('/{dailyExpense}', [DailyExpenseController::class, 'show'])->name('daily_expenses.show');
        Route::get('/{dailyExpense}/edit', [DailyExpenseController::class, 'edit'])->name('daily_expenses.edit');
        Route::put('/{dailyExpense}', [DailyExpenseController::class, 'update'])->name('daily_expenses.update');
        Route::delete('/{dailyExpense}', [DailyExpenseController::class, 'destroy'])->name('daily_expenses.destroy');
    });

    // Route::get('/update-all-reports-progress', [ReportController::class, 'updateAllReportsProgress']);

    Route::get('/profile', [ProfileController::class, 'show'])->name('profile');

    Route::get('/user-create', [UserController::class, 'create'])->name('user.create');
    Route::post('/user-create', [UserController::class, 'store'])->name('user.store');
});
