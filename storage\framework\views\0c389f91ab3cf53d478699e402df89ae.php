<?php $__env->startSection('title', 'Bandingkan Budget Proyek'); ?>
<?php $__env->startSection('header', 'Bandingkan Budget Antar Proyek'); ?>

<?php $__env->startSection('content'); ?>
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Pilih Proyek untuk Perbandingan</h3>
        <form action="<?php echo e(route('budgets.compare')); ?>" method="GET">
            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-3">Pilih minimal 2 proyek untuk melihat perbandingan anggaran dan estimasi keuntungan antar proyek.</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center p-3 border rounded-lg <?php echo e(in_array($project->id, $selectedProjects) ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200'); ?>">
                            <input type="checkbox" name="projects[]" id="project_<?php echo e($project->id); ?>" value="<?php echo e($project->id); ?>" 
                                class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                <?php echo e(in_array($project->id, $selectedProjects) ? 'checked' : ''); ?>>
                            <label for="project_<?php echo e($project->id); ?>" class="ml-2 block text-sm font-medium text-gray-900 flex-grow">
                                <?php echo e($project->name); ?>

                            </label>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            
            <div class="flex justify-between">
                <a href="<?php echo e(route('budgets.index')); ?>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Kembali ke Daftar Budget
                </a>
                <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Bandingkan Proyek
                </button>
            </div>
        </form>
    </div>
    
    <?php if(!empty($comparisonData)): ?>
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">Hasil Perbandingan Anggaran</h2>
                <div class="flex space-x-2">
                    <button type="button" id="showChartView" class="px-3 py-1 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                        </svg>
                        Tampilan Grafik
                    </button>
                    <button type="button" id="showTableView" class="px-3 py-1 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd" />
                        </svg>
                        Tampilan Tabel
                    </button>
                </div>
            </div>
            
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-<?php echo e(count($comparisonData)); ?> gap-4 mb-6">
                <?php $__currentLoopData = $comparisonData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-gray-50 p-4 rounded-lg border-l-4 <?php echo e($index % 3 == 0 ? 'border-indigo-500' : ($index % 3 == 1 ? 'border-green-500' : 'border-purple-500')); ?>">
                        <h3 class="font-bold text-lg mb-2"><?php echo e($data['project_name']); ?></h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Total Budget:</span>
                                <span class="font-semibold">Rp <?php echo e(number_format($data['total_budget'], 0, ',', '.')); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Estimasi Profit:</span>
                                <span class="font-semibold text-green-600">Rp <?php echo e(number_format($data['total_profit'], 0, ',', '.')); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Persentase Profit:</span>
                                <span class="font-semibold <?php echo e($data['avg_profit_percentage'] > 20 ? 'text-green-600' : 'text-yellow-600'); ?>"><?php echo e(number_format($data['avg_profit_percentage'], 1)); ?>%</span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Chart View -->
            <div id="chartView" class="mb-6">
                <!-- Bar Chart for Budget Comparison -->
                <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2">Perbandingan Total Budget</h3>
                    <div class="h-64">
                        <canvas id="budgetComparisonChart"></canvas>
                    </div>
                </div>
                
                <!-- Bar Chart for Profit Comparison -->
                <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2">Perbandingan Estimasi Keuntungan</h3>
                    <div class="h-64">
                        <canvas id="profitComparisonChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Table View -->
            <div id="tableView" class="mb-6 bg-gray-50 p-4 rounded-lg hidden">
                <h3 class="text-lg font-semibold mb-2">Detail Perbandingan</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-100">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Proyek</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Budget</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estimasi Keuntungan</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Persentase Keuntungan</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah Budget</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Approved</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Pending</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php $__currentLoopData = $comparisonData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo e($data['project_name']); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Rp <?php echo e(number_format($data['total_budget'], 0, ',', '.')); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Rp <?php echo e(number_format($data['total_profit'], 0, ',', '.')); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo e(number_format($data['avg_profit_percentage'], 2)); ?>%
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo e($data['budget_count']); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Rp <?php echo e(number_format($data['approved_budget'], 0, ',', '.')); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Rp <?php echo e(number_format($data['pending_budget'], 0, ',', '.')); ?>

                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php elseif(!empty($selectedProjects)): ?>
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada data budget untuk dibandingkan</h3>
            <p class="text-gray-600 mb-4">Proyek yang dipilih tidak memiliki data budget yang dapat dibandingkan.</p>
            <a href="<?php echo e(route('budgets.index')); ?>" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                Lihat Semua Budget
            </a>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php if(!empty($comparisonData)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between chart and table views
        document.getElementById('showChartView').addEventListener('click', function() {
            document.getElementById('chartView').classList.remove('hidden');
            document.getElementById('tableView').classList.add('hidden');
            this.classList.remove('bg-gray-200', 'text-gray-700');
            this.classList.add('bg-indigo-600', 'text-white');
            document.getElementById('showTableView').classList.remove('bg-indigo-600', 'text-white');
            document.getElementById('showTableView').classList.add('bg-gray-200', 'text-gray-700');
        });
        
        document.getElementById('showTableView').addEventListener('click', function() {
            document.getElementById('tableView').classList.remove('hidden');
            document.getElementById('chartView').classList.add('hidden');
            this.classList.remove('bg-gray-200', 'text-gray-700');
            this.classList.add('bg-indigo-600', 'text-white');
            document.getElementById('showChartView').classList.remove('bg-indigo-600', 'text-white');
            document.getElementById('showChartView').classList.add('bg-gray-200', 'text-gray-700');
        });
        
        // Generate random colors for charts
        function generateColors(count) {
            const colors = [
                'rgba(99, 102, 241, 0.7)',  // Indigo
                'rgba(16, 185, 129, 0.7)',  // Green
                'rgba(139, 92, 246, 0.7)',  // Purple
                'rgba(245, 158, 11, 0.7)',  // Amber
                'rgba(239, 68, 68, 0.7)',   // Red
                'rgba(6, 182, 212, 0.7)'    // Cyan
            ];
            
            const borderColors = [
                'rgba(99, 102, 241, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(139, 92, 246, 1)',
                'rgba(245, 158, 11, 1)',
                'rgba(239, 68, 68, 1)',
                'rgba(6, 182, 212, 1)'
            ];
            
            const result = {
                backgroundColor: [],
                borderColor: []
            };
            
            for (let i = 0; i < count; i++) {
                const colorIndex = i % colors.length;
                result.backgroundColor.push(colors[colorIndex]);
                result.borderColor.push(borderColors[colorIndex]);
            }
            
            return result;
        }
        
        // Format currency for chart labels
        function formatCurrency(value) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
        }
        
        // Budget Comparison Chart
        const projectNames = <?php echo json_encode(array_column($comparisonData, 'project_name')); ?>;
        const budgetData = <?php echo json_encode(array_column($comparisonData, 'total_budget')); ?>;
        const profitData = <?php echo json_encode(array_column($comparisonData, 'total_profit')); ?>;
        const profitPercentageData = <?php echo json_encode(array_column($comparisonData, 'avg_profit_percentage')); ?>;
        
        const colors = generateColors(projectNames.length);
        
        const budgetCtx = document.getElementById('budgetComparisonChart').getContext('2d');
        const budgetChart = new Chart(budgetCtx, {
            type: 'bar',
            data: {
                labels: projectNames,
                datasets: [{
                    label: 'Total Budget',
                    data: budgetData,
                    backgroundColor: colors.backgroundColor,
                    borderColor: colors.borderColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Total Budget: ' + formatCurrency(context.raw);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        },
                        title: {
                            display: true,
                            text: 'Total Budget (Rp)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Proyek'
                        }
                    }
                }
            }
        });
        
        // Profit Comparison Chart
        const profitCtx = document.getElementById('profitComparisonChart').getContext('2d');
        const profitChart = new Chart(profitCtx, {
            type: 'bar',
            data: {
                labels: projectNames,
                datasets: [{
                    label: 'Estimasi Keuntungan',
                    data: profitData,
                    backgroundColor: colors.backgroundColor,
                    borderColor: colors.borderColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const index = context.dataIndex;
                                return [
                                    'Estimasi Keuntungan: ' + formatCurrency(context.raw),
                                    'Persentase Keuntungan: ' + profitPercentageData[index].toFixed(1) + '%'
                                ];
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        },
                        title: {
                            display: true,
                            text: 'Estimasi Keuntungan (Rp)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Proyek'
                        }
                    }
                }
            }
        });
    });
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\DisaCloud05-v4\resources\views/budgets/compare.blade.php ENDPATH**/ ?>