

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit <PERSON><PERSON><PERSON></h1>
                <p class="mt-2 text-sm text-gray-500"><?php echo e($project->name); ?> - <?php echo e($report->report_date->format('d M Y')); ?></p>
            </div>
            <a href="<?php echo e(route('reports.show', $project)); ?>" class="text-blue-600 hover:text-blue-800">
                Ke<PERSON>li ke Daftar Laporan
            </a>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form action="<?php echo e(route('reports.update', [$project, $report])); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="px-4 py-5 space-y-6 sm:p-6">
                    <div>
                        <label for="activities_done" class="block text-sm font-medium text-gray-700">
                            Aktivitas yang Dilakukan
                        </label>
                        <div class="mt-1">
                            <textarea id="activities_done" name="activities_done" rows="4" 
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                                placeholder="Jelaskan aktivitas yang telah dilakukan hari ini"><?php echo e(old('activities_done', $report->activities_done)); ?></textarea>
                        </div>
                        <?php $__errorArgs = ['activities_done'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="challenges" class="block text-sm font-medium text-gray-700">
                            Kendala yang Dihadapi
                        </label>
                        <div class="mt-1">
                            <textarea id="challenges" name="challenges" rows="3" 
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                                placeholder="Jelaskan kendala atau tantangan yang dihadapi (opsional)"><?php echo e(old('challenges', $report->challenges)); ?></textarea>
                        </div>
                        <?php $__errorArgs = ['challenges'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="next_plan" class="block text-sm font-medium text-gray-700">
                            Rencana Selanjutnya
                        </label>
                        <div class="mt-1">
                            <textarea id="next_plan" name="next_plan" rows="3" 
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                                placeholder="Jelaskan rencana untuk hari berikutnya"><?php echo e(old('next_plan', $report->next_plan)); ?></textarea>
                        </div>
                        <?php $__errorArgs = ['next_plan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    
                    <?php if(isset($tasks) && $tasks->count() > 0): ?>
                    <div class="pt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Status Tugas Proyek
                        </label>
                        <div class="mt-2 space-y-3 bg-white p-4 border border-gray-200 rounded-md">
                            <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center">
                                <input id="task_<?php echo e($task->id); ?>"
                                       name="tasks_completed[<?php echo e($task->id); ?>]"
                                       type="checkbox"
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                                       value="1"
                                       <?php echo e($task->completed ? 'checked' : ''); ?>>
                                <label for="task_<?php echo e($task->id); ?>" class="ml-3 block text-sm text-gray-700">
                                    <?php echo e($task->name); ?>

                                </label>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php $__errorArgs = ['tasks_completed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $__errorArgs = ['tasks_completed.'.$task->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>
                    

                    <div>
                        <label for="progress_percentage" class="block text-sm font-medium text-gray-700">
                            Progress (%)
                        </label>
                        <div class="mt-1">
                            <input type="number" name="progress_percentage" id="progress_percentage"
                                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md bg-gray-100"
                                min="0" max="100" value="<?php echo e(old('progress_percentage', $report->progress_percentage)); ?>" readonly>
                        </div>
                        <?php $__errorArgs = ['progress_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                    <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function () {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="tasks_completed"]');
    const progressInput = document.getElementById('progress_percentage');
    function updateProgress() {
        const total = checkboxes.length;
        if (total === 0) {
            progressInput.value = 0;
            return;
        }
        let checked = 0;
        checkboxes.forEach(cb => { if (cb.checked) checked++; });
        const percent = Math.round((checked / total) * 100);
        progressInput.value = percent;
    }
    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateProgress);
    });
    updateProgress(); // Inisialisasi saat halaman dimuat
});
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\DisaCloud05-v4\resources\views/reports/edit.blade.php ENDPATH**/ ?>