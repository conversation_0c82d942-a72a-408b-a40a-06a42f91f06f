<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Export - <?php echo e($monthName); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: #fff;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px 0;
            border-bottom: 2px solid #4F46E5;
        }
        
        .header h1 {
            font-size: 28px;
            color: #4F46E5;
            margin-bottom: 5px;
        }
        
        .header h2 {
            font-size: 20px;
            color: #6B7280;
            font-weight: normal;
        }
        
        .calendar-container {
            width: 100%;
            margin: 0 auto;
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .calendar-table th {
            background-color: #F3F4F6;
            color: #374151;
            font-weight: bold;
            text-align: center;
            padding: 12px 8px;
            border: 1px solid #D1D5DB;
            font-size: 14px;
        }
        
        .calendar-table td {
            border: 1px solid #D1D5DB;
            vertical-align: top;
            height: 100px;
            width: 14.28%;
            padding: 5px;
            position: relative;
        }
        
        .day-number {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .current-month {
            color: #111827;
        }
        
        .other-month {
            color: #9CA3AF;
        }
        
        .today {
            background-color: #FEF3C7;
        }
        
        .today .day-number {
            color: #D97706;
            background-color: #F59E0B;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .event {
            font-size: 9px;
            padding: 2px 4px;
            margin: 1px 0;
            border-radius: 3px;
            color: white;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
        }
        
        .event-project-start {
            background-color: #10B981;
        }
        
        .event-project-end {
            background-color: #EF4444;
        }
        
        .event-calendar {
            background-color: #3B82F6;
        }
        
        .legend {
            margin-top: 20px;
            padding: 15px;
            background-color: #F9FAFB;
            border-radius: 8px;
        }
        
        .legend h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #374151;
        }
        
        .legend-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        
        .legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 3px;
            margin-right: 5px;
            vertical-align: middle;
        }
        
        .legend-text {
            font-size: 12px;
            vertical-align: middle;
        }
        
        .project-list {
            margin-top: 20px;
            page-break-inside: avoid;
        }
        
        .project-list h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #374151;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 5px;
        }
        
        .project-item {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #F9FAFB;
            border-left: 4px solid #4F46E5;
            border-radius: 4px;
        }
        
        .project-name {
            font-weight: bold;
            color: #111827;
            margin-bottom: 3px;
        }
        
        .project-dates {
            font-size: 11px;
            color: #6B7280;
        }
        
        .project-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            margin-top: 3px;
        }
        
        .status-planning {
            background-color: #FEF3C7;
            color: #92400E;
        }
        
        .status-in_progress {
            background-color: #DBEAFE;
            color: #1E40AF;
        }
        
        .status-completed {
            background-color: #D1FAE5;
            color: #065F46;
        }
        
        .status-on_hold {
            background-color: #FEE2E2;
            color: #991B1B;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #6B7280;
            border-top: 1px solid #E5E7EB;
            padding-top: 10px;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DisaCloud Project Calendar</h1>
        <h2><?php echo e($monthName); ?></h2>
    </div>

    <div class="calendar-container">
        <table class="calendar-table">
            <thead>
                <tr>
                    <th>Sunday</th>
                    <th>Monday</th>
                    <th>Tuesday</th>
                    <th>Wednesday</th>
                    <th>Thursday</th>
                    <th>Friday</th>
                    <th>Saturday</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $calendar; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $week): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <?php $__currentLoopData = $week; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td class="<?php echo e($day['isToday'] ? 'today' : ''); ?>">
                        <div class="day-number <?php echo e($day['isCurrentMonth'] ? 'current-month' : 'other-month'); ?>">
                            <?php echo e($day['day']); ?>

                        </div>
                        
                        <?php $__currentLoopData = $day['events']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="event event-<?php echo e($event['type'] == 'project_start' ? 'project-start' : ($event['type'] == 'project_end' ? 'project-end' : 'calendar')); ?>" 
                             title="<?php echo e($event['title']); ?>">
                            <?php echo e(Str::limit($event['title'], 20)); ?>

                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

    <div class="legend">
        <h3>Legend</h3>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #10B981;"></span>
            <span class="legend-text">Project Start</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #EF4444;"></span>
            <span class="legend-text">Project End</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #3B82F6;"></span>
            <span class="legend-text">Calendar Event</span>
        </div>
    </div>

    <?php if($projects->count() > 0): ?>
    <div class="project-list">
        <h3>Projects in <?php echo e($monthName); ?></h3>
        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="project-item">
            <div class="project-name"><?php echo e($project->name); ?></div>
            <div class="project-dates">
                <?php if($project->start_date): ?>
                    Start: <?php echo e($project->start_date->format('M d, Y')); ?>

                <?php endif; ?>
                <?php if($project->end_date): ?>
                    | End: <?php echo e($project->end_date->format('M d, Y')); ?>

                <?php endif; ?>
            </div>
            <span class="project-status status-<?php echo e($project->status); ?>">
                <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

            </span>
            <?php if($project->description): ?>
            <div style="font-size: 11px; color: #6B7280; margin-top: 5px;">
                <?php echo e(Str::limit($project->description, 100)); ?>

            </div>
            <?php endif; ?>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <?php endif; ?>

    <div class="footer">
        <p>Generated on <?php echo e(now()->format('F d, Y \a\t H:i')); ?> | DisaCloud Project Management System</p>
    </div>
</body>
</html>
<?php /**PATH C:\laragon\www\DisaCloud05-v4\resources\views/exports/calendar-pdf.blade.php ENDPATH**/ ?>