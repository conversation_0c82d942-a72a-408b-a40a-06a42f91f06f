<?php $__env->startSection('title', 'Detail Budget'); ?>
<?php $__env->startSection('header', 'Detail Budget'); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="flex justify-between items-start mb-6">
                <div>
                    <h2 class="text-xl font-bold text-gray-800">Budget untuk Proyek: <?php echo e($budget->project->name); ?></h2>
                    <p class="text-gray-600"><?php echo e(\Carbon\Carbon::parse($budget->budget_date)->format('d M Y')); ?></p>
                </div>
                <div>
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full
                        <?php echo e($budget->status == 'approved' ? 'bg-green-100 text-green-800' : ''); ?>

                        <?php echo e($budget->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                        <?php echo e($budget->status == 'rejected' ? 'bg-red-100 text-red-800' : ''); ?>">
                        <?php echo e(ucfirst($budget->status)); ?>

                    </span>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2">Informasi Budget</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Jumlah Budget:</span>
                            <span class="font-medium">Rp <?php echo e(number_format($budget->amount, 0, ',', '.')); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Estimasi Keuntungan:</span>
                            <span class="font-medium">Rp <?php echo e(number_format($budget->estimated_profit, 0, ',', '.')); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Persentase Keuntungan:</span>
                            <span class="font-medium"><?php echo e(number_format($budget->profit_percentage, 2)); ?>%</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-2">Informasi Proyek</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Nama Proyek:</span>
                            <span class="font-medium"><?php echo e($budget->project->name); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status Proyek:</span>
                            <span class="font-medium"><?php echo e(ucfirst($budget->project->status)); ?></span>
                        </div>
                        <?php if($budget->project->start_date && $budget->project->end_date): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Periode Proyek:</span>
                            <span class="font-medium">
                                <?php echo e(\Carbon\Carbon::parse($budget->project->start_date)->format('d M Y')); ?> - 
                                <?php echo e(\Carbon\Carbon::parse($budget->project->end_date)->format('d M Y')); ?>

                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Deskripsi</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-800 whitespace-pre-line"><?php echo e($budget->description); ?></p>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Invoice</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <?php if($budget->invoice_file): ?>
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <a href="<?php echo e(route('budgets.download-invoice', $budget)); ?>" class="text-indigo-600 hover:text-indigo-900 font-medium">
                                Download Invoice PDF
                            </a>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500">Tidak ada file invoice untuk budget ini.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="flex justify-between">
                <a href="<?php echo e(route('budgets.index')); ?>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                    Kembali ke Daftar
                </a>
                <div class="flex space-x-2">
                    <a href="<?php echo e(route('budgets.edit', $budget)); ?>" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        Edit Budget
                    </a>
                    <form action="<?php echo e(route('budgets.destroy', $budget)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700" 
                            onclick="return confirm('Yakin ingin menghapus budget ini?')">
                            Hapus Budget
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\DisaCloud05-v4\resources\views/budgets/show.blade.php ENDPATH**/ ?>