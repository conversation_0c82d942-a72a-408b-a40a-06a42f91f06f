<?php

// Simple test script for calendar export
require_once 'vendor/autoload.php';

use Barryvdh\DomPDF\Facade\Pdf;

// Test HTML content
$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Calendar</title>
    <style>
        body { font-family: DejaVu Sans, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 20px; }
        .calendar-table { width: 100%; border-collapse: collapse; }
        .calendar-table th, .calendar-table td { border: 1px solid #ddd; padding: 8px; }
        .calendar-table th { background-color: #f5f5f5; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Calendar Export</h1>
        <h2>July 2025</h2>
    </div>
    
    <table class="calendar-table">
        <thead>
            <tr>
                <th>Sun</th><th>Mon</th><th>Tue</th><th>Wed</th><th>Thu</th><th>Fri</th><th>Sat</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>29</td><td>30</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td>
            </tr>
            <tr>
                <td>6</td><td>7</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td>
            </tr>
            <tr>
                <td>13</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td><td>19</td>
            </tr>
            <tr>
                <td>20</td><td>21</td><td>22</td><td>23</td><td>24</td><td>25</td><td>26</td>
            </tr>
            <tr>
                <td>27</td><td>28</td><td>29</td><td>30</td><td>31</td><td>1</td><td>2</td>
            </tr>
        </tbody>
    </table>
    
    <p>This is a test calendar export to verify PDF generation is working.</p>
</body>
</html>
';

try {
    echo "Testing PDF generation...\n";
    
    // Test basic PDF generation
    $pdf = Pdf::loadHTML($html)
              ->setPaper('A4', 'landscape')
              ->setOptions([
                  'defaultFont' => 'DejaVu Sans',
                  'isHtml5ParserEnabled' => true,
                  'isRemoteEnabled' => false
              ]);
    
    $output = $pdf->output();
    
    if ($output) {
        echo "✅ PDF generation successful!\n";
        echo "PDF size: " . strlen($output) . " bytes\n";
        
        // Save test file
        file_put_contents('test-calendar.pdf', $output);
        echo "✅ Test PDF saved as 'test-calendar.pdf'\n";
    } else {
        echo "❌ PDF generation failed - no output\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
